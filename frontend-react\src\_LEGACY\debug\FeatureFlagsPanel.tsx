// Feature Flags Debug Panel
// 开发环境下的功能开关调试面板

import React, { useState } from 'react';
import { useFeatureFlags } from '@/utils/hooks/useFeatureFlags';
import type { FeatureFlags } from '@/utils/featureFlags';
import { IntegrationTest } from './IntegrationTest';

// ============================================================================
// 功能开关分组配置
// ============================================================================

const FLAG_GROUPS = {
  'UI Components': [
    'useNewNavigationSidebar',
    'useNewGallery',
    'useNewDetailsSidebar',
    'useNewWorkspace',
    'useNewLayout',
  ] as (keyof FeatureFlags)[],

  'Features': [
    'enableNewDataAdapters',
    'enableNewStyling',
    'enableAdvancedTagging',
    'enableBatchOperations',
  ] as (keyof FeatureFlags)[],

  'Development': [
    'enableDebugMode',
    'enablePerformanceMonitoring',
    'showComponentBoundaries',
    'logDataTransformations',
  ] as (keyof FeatureFlags)[],
};

// 功能开关显示名称映射
const FLAG_LABELS: Record<keyof FeatureFlags, string> = {
  useNewNavigationSidebar: '新导航侧边栏',
  useNewGallery: '新画廊组件',
  useNewDetailsSidebar: '新详情侧边栏',
  useNewWorkspace: '新工作台',
  useNewLayout: '新布局系统',
  enableNewDataAdapters: '新数据适配器',
  enableNewStyling: '新样式系统',
  enableAdvancedTagging: '高级标签功能',
  enableBatchOperations: '批量操作',
  enableDebugMode: '调试模式',
  enablePerformanceMonitoring: '性能监控',
  showComponentBoundaries: '显示组件边界',
  logDataTransformations: '记录数据转换',
};

// ============================================================================
// 功能开关调试面板组件
// ============================================================================

interface FeatureFlagsPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

export const FeatureFlagsPanel: React.FC<FeatureFlagsPanelProps> = ({
  isVisible,
  onClose,
}) => {
  const {
    flags,
    setFlag,
    resetToDefaults,
    enableNewUI,
    disableNewUI,
    enableDevelopmentMode,
    enableProductionMode,
  } = useFeatureFlags();

  const [searchTerm, setSearchTerm] = useState('');
  const [showIntegrationTest, setShowIntegrationTest] = useState(false);

  if (!isVisible) return null;

  // 过滤功能开关
  const filteredGroups = Object.entries(FLAG_GROUPS).reduce((acc, [groupName, flagKeys]) => {
    const filteredFlags = flagKeys.filter(key =>
      FLAG_LABELS[key].toLowerCase().includes(searchTerm.toLowerCase()) ||
      key.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filteredFlags.length > 0) {
      acc[groupName] = filteredFlags;
    }

    return acc;
  }, {} as Record<string, (keyof FeatureFlags)[]>);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card border border-border rounded-lg shadow-lg w-[600px] max-h-[80vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h2 className="text-lg font-medium text-foreground">功能开关调试面板</h2>
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            ✕
          </button>
        </div>

        {/* 搜索和快捷操作 */}
        <div className="p-4 border-b border-border space-y-3">
          {/* 搜索框 */}
          <input
            type="text"
            placeholder="搜索功能开关..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 bg-input border border-border rounded text-foreground placeholder-muted-foreground"
          />

          {/* 快捷操作按钮 */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={enableNewUI}
              className="px-3 py-1 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90 transition-colors"
            >
              启用新UI
            </button>
            <button
              onClick={disableNewUI}
              className="px-3 py-1 bg-secondary text-secondary-foreground rounded text-sm hover:bg-secondary/90 transition-colors"
            >
              禁用新UI
            </button>
            <button
              onClick={enableDevelopmentMode}
              className="px-3 py-1 bg-accent text-accent-foreground rounded text-sm hover:bg-accent/90 transition-colors"
            >
              开发模式
            </button>
            <button
              onClick={enableProductionMode}
              className="px-3 py-1 bg-muted text-muted-foreground rounded text-sm hover:bg-muted/90 transition-colors"
            >
              生产模式
            </button>
            <button
              onClick={resetToDefaults}
              className="px-3 py-1 bg-destructive text-destructive-foreground rounded text-sm hover:bg-destructive/90 transition-colors"
            >
              重置默认
            </button>
            <button
              onClick={() => setShowIntegrationTest(!showIntegrationTest)}
              className="px-3 py-1 bg-accent text-accent-foreground rounded text-sm hover:bg-accent/90 transition-colors"
            >
              {showIntegrationTest ? '隐藏测试' : '集成测试'}
            </button>
          </div>
        </div>

        {/* 集成测试面板 */}
        {showIntegrationTest && (
          <div className="p-4 border-b border-border">
            <IntegrationTest />
          </div>
        )}

        {/* 功能开关列表 */}
        <div className="overflow-y-auto max-h-[400px]">
          {Object.entries(filteredGroups).map(([groupName, flagKeys]) => (
            <div key={groupName} className="p-4 border-b border-border last:border-b-0">
              <h3 className="text-sm font-medium text-foreground mb-3">{groupName}</h3>
              <div className="space-y-2">
                {flagKeys.map((flagKey) => (
                  <div key={flagKey} className="flex items-center justify-between">
                    <div className="flex-1">
                      <label className="text-sm text-foreground cursor-pointer">
                        {FLAG_LABELS[flagKey]}
                      </label>
                      <div className="text-xs text-muted-foreground font-mono">
                        {flagKey}
                      </div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={flags[flagKey] as boolean}
                        onChange={(e) => setFlag(flagKey, e.target.checked as any)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-muted peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-ring rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 底部信息 */}
        <div className="p-4 bg-muted/50 text-xs text-muted-foreground">
          <div className="flex justify-between">
            <span>开发环境功能开关面板</span>
            <span>按 Ctrl+Shift+F 快速打开</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// 功能开关快捷键Hook
// ============================================================================

export const useFeatureFlagsShortcut = (onToggle: () => void) => {
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'F') {
        event.preventDefault();
        onToggle();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onToggle]);
};

// ============================================================================
// 功能开关调试包装器
// ============================================================================

interface FeatureFlagsDebugWrapperProps {
  children: React.ReactNode;
}

export const FeatureFlagsDebugWrapper: React.FC<FeatureFlagsDebugWrapperProps> = ({
  children,
}) => {
  const [isPanelVisible, setIsPanelVisible] = useState(false);
  const { flags } = useFeatureFlags();

  // 快捷键支持
  useFeatureFlagsShortcut(() => setIsPanelVisible(!isPanelVisible));

  // 只在开发环境和调试模式下显示
  if (!import.meta.env.DEV || !flags.enableDebugMode) {
    return <>{children}</>;
  }

  return (
    <>
      {children}

      {/* 调试面板触发按钮 */}
      <button
        onClick={() => setIsPanelVisible(true)}
        className="fixed bottom-4 right-4 bg-primary text-primary-foreground p-2 rounded-full shadow-lg hover:bg-primary/90 transition-colors z-40"
        title="功能开关调试面板 (Ctrl+Shift+F)"
      >
        🚩
      </button>

      {/* 调试面板 */}
      <FeatureFlagsPanel
        isVisible={isPanelVisible}
        onClose={() => setIsPanelVisible(false)}
      />
    </>
  );
};
