// src/components/ui/ImageGrid.tsx
import React from 'react';

interface ImageItem {
  id: string;
  src: string;
  alt?: string;
  title?: string;
  isSelected?: boolean;
}

interface ImageGridProps {
  images: ImageItem[];
  columns?: number;
  gap?: string;
  onImageClick?: (image: ImageItem) => void;
  onImageSelect?: (image: ImageItem, selected: boolean) => void;
  className?: string;
}

export function ImageGrid({ 
  images, 
  columns = 4, 
  gap = 'gap-space-2', 
  onImageClick, 
  onImageSelect, 
  className 
}: ImageGridProps) {
  return (
    <div 
      className={`grid ${gap} ${className}`}
      style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
    >
      {images.map((image) => (
        <div
          key={image.id}
          className={`
            relative group cursor-pointer rounded-sm overflow-hidden
            border-2 transition-all
            ${image.isSelected 
              ? 'border-mizzy-border-highlight' 
              : 'border-transparent hover:border-mizzy-border-base'
            }
          `}
          onClick={() => onImageClick?.(image)}
        >
          {/* 图像容器 */}
          <div className="aspect-square bg-mizzy-bg-input">
            <img
              src={image.src}
              alt={image.alt || image.title || '图像'}
              className="w-full h-full object-cover"
              loading="lazy"
            />
          </div>

          {/* 选择框 */}
          {onImageSelect && (
            <div 
              className="absolute top-space-1 right-space-1 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                onImageSelect(image, !image.isSelected);
              }}
            >
              <div className={`
                w-[20px] h-[20px] rounded-sm border-2 flex items-center justify-center
                ${image.isSelected 
                  ? 'bg-mizzy-icon-hover border-mizzy-icon-hover' 
                  : 'bg-mizzy-bg-panel border-mizzy-border-base hover:border-mizzy-border-highlight'
                }
              `}>
                {image.isSelected && (
                  <span className="text-white text-xs">✓</span>
                )}
              </div>
            </div>
          )}

          {/* 图像标题 */}
          {image.title && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-space-2">
              <p className="text-white text-xs truncate">{image.title}</p>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
