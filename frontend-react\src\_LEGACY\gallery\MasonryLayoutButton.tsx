import { Grid3X3 } from 'lucide-react';

interface MasonryLayoutButtonProps {
  layoutMode: 'grid' | 'masonry';
  onToggle: () => void;
}

export function MasonryLayoutButton({ layoutMode, onToggle }: MasonryLayoutButtonProps) {
  return (
    <button
      style={{
        padding: '6px 8px',
        backgroundColor: layoutMode === 'masonry' 
          ? 'var(--color-surface-button-active)' 
          : 'var(--color-surface-button)',
        border: '1px solid var(--color-border-primary)',
        borderRadius: 'var(--border-radius-sm)',
        color: 'var(--color-text-title)',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'background-color 0.2s ease'
      }}
      onMouseEnter={(e) => {
        if (layoutMode !== 'masonry') {
          e.currentTarget.style.backgroundColor = 'var(--color-surface-button-hover)';
        }
      }}
      onMouseLeave={(e) => {
        if (layoutMode !== 'masonry') {
          e.currentTarget.style.backgroundColor = 'var(--color-surface-button)';
        }
      }}
      onClick={onToggle}
      title={layoutMode === 'grid' ? '切换到流式布局' : '切换到网格布局'}
    >
      <Grid3X3 size={16} />
    </button>
  );
}
