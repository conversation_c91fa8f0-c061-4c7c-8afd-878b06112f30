import { useState } from 'react';
import { Button } from '@/components/ui';
import { SimpleTestLayout } from '@/components/Layout/SimpleTestLayout';
import { create } from 'zustand';

// 任务 2.1 - 简洁的 Zustand Store 直接实现
interface AppState {
  // 1. 面板可见性状态
  isCatalogVisible: boolean;
  isInfoPanelVisible: boolean;
  isWorkbenchVisible: boolean;

  // 2. 核心数据选择状态
  activeTagId: string | null;      // 当前选中的标签ID
  selectedFileId: string | null;  // 当前选中的文件ID

  // 3. 所有的状态变更操作 (Actions)
  toggleCatalog: () => void;
  toggleInfoPanel: () => void;
  toggleWorkbench: () => void;
  setActiveTag: (tagId: string | null) => void;
  setSelectedFile: (fileId: string | null) => void;
}

// 创建 Store
const useAppStore = create<AppState>((set) => ({
  // 定义所有状态的初始值
  isCatalogVisible: true,
  isInfoPanelVisible: true,
  isWorkbenchVisible: true, // 默认让它也可见，便于调试
  activeTagId: null,
  selectedFileId: null,

  // 实现所有的状态变更操作
  toggleCatalog: () => set((state) => ({ isCatalogVisible: !state.isCatalogVisible })),
  toggleInfoPanel: () => set((state) => ({ isInfoPanelVisible: !state.isInfoPanelVisible })),
  toggleWorkbench: () => set((state) => ({ isWorkbenchVisible: !state.isWorkbenchVisible })),
  setActiveTag: (tagId) => set({ activeTagId: tagId }), // 设置当前标签
  setSelectedFile: (fileId) => set({ selectedFileId: fileId }), // 设置当前文件
}));

/**
 * 最小化的 MizzyStarApp - 用于隔离问题
 */
function MinimalMizzyStarApp() {
  // console.log('MinimalMizzyStarApp rendering...'); // [CLEANED]

  // 本地状态
  const [showDevControls, setShowDevControls] = useState(true);

  // 从 AppStore 获取状态 - 使用新的简洁 Store
  const {
    isCatalogVisible,
    isInfoPanelVisible,
    isWorkbenchVisible,
    toggleCatalog,
    toggleInfoPanel,
    toggleWorkbench,
    activeTagId,
    selectedFileId,
    setActiveTag,
    setSelectedFile,
  } = useAppStore();

  // console.log('Store state:', { isCatalogVisible, isInfoPanelVisible, isWorkbenchVisible, activeTagId, selectedFileId }); // [CLEANED]

  // 布局切换控制 - 简化版本
  const togglePanel = (panel: 'catalog' | 'workbench' | 'info') => {
    switch (panel) {
      case 'catalog':
        toggleCatalog();
        break;
      case 'workbench':
        toggleWorkbench();
        break;
      case 'info':
        toggleInfoPanel();
        break;
    }
  };

  return (
    <div className="h-screen w-screen bg-[#191012]">
      {/* 开发控制面板 */}
      {showDevControls && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-[#040709] border border-[#2A2A2A] rounded-lg p-2 shadow-lg">
          <div className="flex gap-2">
            <Button
              variant={isCatalogVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('catalog')}
            >
              📚 目录栏
            </Button>
            <Button
              variant={isWorkbenchVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('workbench')}
            >
              🛠️ 工作台
            </Button>
            <Button
              variant={isInfoPanelVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('info')}
            >
              📄 信息栏
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowDevControls(false)}
            >
              ✕ 隐藏
            </Button>
          </div>
        </div>
      )}

      {/* 简单测试布局 */}
      <SimpleTestLayout
        catalogPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">📚 目录栏</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">可见: {isCatalogVisible ? '是' : '否'}</div>
            </div>
          </div>
        }
        galleryPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">🖼️ 画廊面板</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">全屏: 否</div>
            </div>
          </div>
        }
        workbenchPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">🛠️ 工作台</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">可见: {isWorkbenchVisible ? '是' : '否'}</div>
            </div>
          </div>
        }
        infoPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">📄 信息栏</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">可见: {isInfoPanelVisible ? '是' : '否'}</div>
            </div>
          </div>
        }
        showCatalogPanel={isCatalogVisible}
        showWorkbench={isWorkbenchVisible}
        showInfoPanel={isInfoPanelVisible}
        isFullscreenGallery={false}
      />

      {/* 隐藏的开发控制按钮 */}
      {!showDevControls && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 z-50 opacity-50 hover:opacity-100"
          onClick={() => setShowDevControls(true)}
        >
          ⚙️
        </Button>
      )}
    </div>
  );
}

export default MinimalMizzyStarApp;
