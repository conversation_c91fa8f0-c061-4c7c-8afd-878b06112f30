import React, { useEffect, useCallback } from 'react';

interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'archive-manager' | 'upload' | 'create-case';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  className?: string;
}

export function BaseModal({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  className = ''
}: BaseModalProps) {
  // 处理ESC键关闭
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // 防止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // 处理遮罩层点击
  const handleOverlayClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget && closeOnOverlayClick) {
      onClose();
    }
  }, [onClose, closeOnOverlayClick]);

  if (!isOpen) return null;

  // 尺寸映射 - 根据设计需求定制
  const sizeClasses = {
    sm: 'w-[400px] h-auto max-h-[500px]',
    md: 'w-[600px] h-auto max-h-[600px]',
    lg: 'w-[800px] h-auto max-h-[700px]',
    xl: 'w-[1000px] h-auto max-h-[800px]',
    'archive-manager': 'w-[1024px] h-[768px]', // 管理档案库专用尺寸
    'upload': 'w-[600px] h-auto max-h-[80vh]',  // 上传模态框专用尺寸
    'create-case': 'w-[1024px] h-[768px]'       // 新建档案库专用尺寸
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1000]"
      onClick={handleOverlayClick}
    >
      <div
        className={`
          bg-[#1F2023] 
          rounded-lg 
          border border-mizzy-border-base 
          font-sans 
          text-mizzy-text-title 
          overflow-hidden
          shadow-2xl
          ${sizeClasses[size]}
          ${className}
        `}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 标题栏 */}
        {(title || showCloseButton) && (
          <div className="flex justify-between items-center p-6 border-b border-mizzy-border-base">
            {title && (
              <h2 className="text-xl font-medium text-mizzy-text-title">
                {title}
              </h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="text-mizzy-text-content hover:text-mizzy-text-title transition-colors text-xl w-8 h-8 flex items-center justify-center rounded hover:bg-mizzy-bg-hover"
              >
                ×
              </button>
            )}
          </div>
        )}

        {/* 内容区域 */}
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </div>
    </div>
  );
}

// 导出常用的模态框按钮样式
export const modalButtonStyles = {
  primary: 'px-4 py-2 text-sm bg-mizzy-accent-primary text-white rounded hover:bg-mizzy-accent-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
  secondary: 'px-4 py-2 text-sm bg-mizzy-bg-main border border-mizzy-border-base rounded text-mizzy-text-content hover:bg-mizzy-bg-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
  danger: 'px-4 py-2 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
};

// 导出常用的输入框样式
export const modalInputStyles = {
  base: 'w-full px-3 py-2 bg-mizzy-bg-main border border-mizzy-border-base rounded text-mizzy-text-title placeholder-mizzy-text-content focus:outline-none focus:border-mizzy-accent-primary transition-colors',
  textarea: 'w-full px-3 py-2 bg-mizzy-bg-main border border-mizzy-border-base rounded text-mizzy-text-title placeholder-mizzy-text-content focus:outline-none focus:border-mizzy-accent-primary resize-none transition-colors'
};
