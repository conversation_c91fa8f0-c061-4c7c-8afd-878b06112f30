interface UpwardTriangleIconProps {
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export function UpwardTriangleIcon({ className = '', onClick, style }: UpwardTriangleIconProps) {
  return (
    <svg
      className={`icon ${className}`}
      onClick={onClick}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ cursor: 'pointer', ...style }}
    >
      <path
        d="M4 10l4-4 4 4"
        stroke="var(--color-text-title)"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
