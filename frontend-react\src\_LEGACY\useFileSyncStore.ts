// useFileSyncStore.ts - Phase 7B: 文件同步状态管理
// 管理本地文件路径与后端file_id的映射关系

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import apiClient, { type FileRegistrationResponse, type ApiError } from '@/lib/apiClient';

// ============================================================================
// 类型定义
// ============================================================================

export interface FileSyncState {
  // 核心映射数据
  fileIdCache: Map<string, number>; // filePath -> file_id
  fileDataCache: Map<number, FileRegistrationResponse>; // file_id -> complete file data

  // 同步状态
  isSyncing: boolean;
  syncProgress: {
    total: number;
    completed: number;
    failed: number;
    currentFile?: string;
  };

  // 错误状态
  syncErrors: Array<{
    filePath: string;
    error: string;
    timestamp: number;
  }>;

  // 案例管理
  currentCaseId: number | null;
  availableCases: Array<{ id: number; name: string }>;

  // 性能统计
  syncStats: {
    totalFilesSynced: number;
    lastSyncTime: number;
    averageSyncTime: number;
    cacheHitRate: number;
  };
}

export interface FileSyncActions {
  // 核心同步功能
  syncDirectory: (filePaths: string[], caseId?: number) => Promise<void>;
  syncSingleFile: (filePath: string, caseId?: number) => Promise<number | null>;

  // 缓存管理
  getFileId: (filePath: string) => number | null;
  getFileData: (fileId: number) => FileRegistrationResponse | null;
  clearCache: () => void;

  // 案例管理
  setCurrentCase: (caseId: number) => void;
  loadAvailableCases: () => Promise<void>;

  // Phase 7C: 案例切换相关
  onCaseChange: (newCaseId: number) => void;
  clearCacheForCase: (caseId?: number) => void;

  // 错误处理
  clearErrors: () => void;
  retryFailedSync: (filePath: string) => Promise<void>;

  // 状态重置
  resetSync: () => void;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: FileSyncState = {
  fileIdCache: new Map(),
  fileDataCache: new Map(),
  isSyncing: false,
  syncProgress: {
    total: 0,
    completed: 0,
    failed: 0,
  },
  syncErrors: [],
  currentCaseId: null,
  availableCases: [],
  syncStats: {
    totalFilesSynced: 0,
    lastSyncTime: 0,
    averageSyncTime: 0,
    cacheHitRate: 0,
  },
};

// ============================================================================
// Store实现
// ============================================================================

export const useFileSyncStore = create<FileSyncState & FileSyncActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // ========================================
        // 核心同步功能实现
        // ========================================

        syncDirectory: async (filePaths: string[], caseId?: number) => {
          const state = get();
          // Phase 7C: 优先使用传入的caseId，然后是store中的currentCaseId
          const targetCaseId = caseId || state.currentCaseId;

          if (!targetCaseId) {
            console.error('❌ 无法同步：未指定案例ID');
            return;
          }

          set({
            isSyncing: true,
            syncProgress: {
              total: filePaths.length,
              completed: 0,
              failed: 0,
            },
            syncErrors: [],
          });

          const startTime = Date.now();
          const newFileIdCache = new Map(state.fileIdCache);
          const newFileDataCache = new Map(state.fileDataCache);
          const errors: typeof state.syncErrors = [];

          for (let i = 0; i < filePaths.length; i++) {
            const filePath = filePaths[i];

            set((state) => ({
              syncProgress: {
                ...state.syncProgress,
                currentFile: filePath,
              },
            }));

            try {
              // 检查缓存
              if (newFileIdCache.has(filePath)) {
                // console.log(`📋 缓存命中: ${filePath}`); // [CLEANED]
                set((state) => ({
                  syncProgress: {
                    ...state.syncProgress,
                    completed: state.syncProgress.completed + 1,
                  },
                }));
                continue;
              }

              // 调用API注册文件
              const fileData = await apiClient.registerFile(targetCaseId, filePath);

              // 更新缓存
              newFileIdCache.set(filePath, fileData.id);
              newFileDataCache.set(fileData.id, fileData);

              // console.log(`✅ 文件同步成功: ${filePath} -> ID:${fileData.id}`); // [CLEANED]

              set((state) => ({
                syncProgress: {
                  ...state.syncProgress,
                  completed: state.syncProgress.completed + 1,
                },
              }));

            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              console.error(`❌ 文件同步失败: ${filePath}`, error);

              errors.push({
                filePath,
                error: errorMessage,
                timestamp: Date.now(),
              });

              set((state) => ({
                syncProgress: {
                  ...state.syncProgress,
                  failed: state.syncProgress.failed + 1,
                },
              }));
            }
          }

          // 更新最终状态
          const endTime = Date.now();
          const syncDuration = endTime - startTime;

          set((state) => ({
            fileIdCache: newFileIdCache,
            fileDataCache: newFileDataCache,
            isSyncing: false,
            syncErrors: errors,
            syncStats: {
              totalFilesSynced: state.syncStats.totalFilesSynced + filePaths.length - errors.length,
              lastSyncTime: endTime,
              averageSyncTime: syncDuration / filePaths.length,
              cacheHitRate: (filePaths.length - errors.length) / filePaths.length,
            },
          }));

          console.log(`🎉 目录同步完成: ${filePaths.length - errors.length}/${filePaths.length} 成功`);
        },

        syncSingleFile: async (filePath: string, caseId?: number) => {
          const state = get();
          const targetCaseId = caseId || state.currentCaseId;

          if (!targetCaseId) {
            console.error('❌ 无法同步：未指定案例ID');
            return null;
          }

          // 检查缓存
          const cachedId = state.fileIdCache.get(filePath);
          if (cachedId) {
            // console.log(`📋 缓存命中: ${filePath} -> ID:${cachedId}`); // [CLEANED]
            return cachedId;
          }

          try {
            set({ isSyncing: true });

            const fileData = await apiClient.registerFile(targetCaseId, filePath);

            set((state) => ({
              fileIdCache: new Map(state.fileIdCache).set(filePath, fileData.id),
              fileDataCache: new Map(state.fileDataCache).set(fileData.id, fileData),
              isSyncing: false,
            }));

            // console.log(`✅ 单文件同步成功: ${filePath} -> ID:${fileData.id}`); // [CLEANED]
            return fileData.id;

          } catch (error) {
            console.error(`❌ 单文件同步失败: ${filePath}`, error);
            set({ isSyncing: false });
            return null;
          }
        },

        // ========================================
        // 缓存管理实现
        // ========================================

        getFileId: (filePath: string) => {
          return get().fileIdCache.get(filePath) || null;
        },

        getFileData: (fileId: number) => {
          return get().fileDataCache.get(fileId) || null;
        },

        clearCache: () => {
          set({
            fileIdCache: new Map(),
            fileDataCache: new Map(),
            syncErrors: [],
          });
        },

        // ========================================
        // 案例管理实现
        // ========================================

        setCurrentCase: (caseId: number) => {
          set({ currentCaseId: caseId });
        },

        loadAvailableCases: async () => {
          try {
            const cases = await apiClient.getCases();
            set({
              availableCases: cases.map((c: any) => ({
                id: c.id,
                name: c.name,
              })),
            });
          } catch (error) {
            console.error('❌ 加载案例列表失败:', error);
          }
        },

        // ========================================
        // 错误处理实现
        // ========================================

        clearErrors: () => {
          set({ syncErrors: [] });
        },

        retryFailedSync: async (filePath: string) => {
          const state = get();
          const targetCaseId = state.currentCaseId;

          if (!targetCaseId) {
            console.error('❌ 无法重试：未指定案例ID');
            return;
          }

          try {
            const fileData = await apiClient.registerFile(targetCaseId, filePath);

            set((state) => ({
              fileIdCache: new Map(state.fileIdCache).set(filePath, fileData.id),
              fileDataCache: new Map(state.fileDataCache).set(fileData.id, fileData),
              syncErrors: state.syncErrors.filter(e => e.filePath !== filePath),
            }));

            // console.log(`✅ 重试同步成功: ${filePath} -> ID:${fileData.id}`); // [CLEANED]

          } catch (error) {
            console.error(`❌ 重试同步失败: ${filePath}`, error);
          }
        },

        // ========================================
        // Phase 7C: 案例切换相关实现
        // ========================================

        onCaseChange: (newCaseId: number) => {
          // console.log(`🔄 Phase 7C: 案例切换，清理文件同步缓存 ${get().currentCaseId} -> ${newCaseId}`); // [CLEANED]

          // 清理当前缓存
          set({
            fileIdCache: new Map(),
            fileDataCache: new Map(),
            syncErrors: [],
            syncProgress: {
              total: 0,
              completed: 0,
              failed: 0,
            },
            currentCaseId: newCaseId,
          });
        },

        clearCacheForCase: (caseId?: number) => {
          const targetCaseId = caseId || get().currentCaseId;
          // console.log(`🧹 Phase 7C: 清理案例 ${targetCaseId} 的文件缓存`); // [CLEANED]

          // 如果是当前案例，清理所有缓存
          if (targetCaseId === get().currentCaseId) {
            set({
              fileIdCache: new Map(),
              fileDataCache: new Map(),
              syncErrors: [],
            });
          }
        },

        // ========================================
        // 状态重置实现
        // ========================================

        resetSync: () => {
          set({
            ...initialState,
            currentCaseId: get().currentCaseId, // 保留当前案例
            availableCases: get().availableCases, // 保留案例列表
          });
        },
      }),
      {
        name: 'file-sync-store',
        // 自定义序列化，处理Map类型
        serialize: (state) => {
          return JSON.stringify({
            ...state.state,
            fileIdCache: Array.from(state.state.fileIdCache.entries()),
            fileDataCache: Array.from(state.state.fileDataCache.entries()),
          });
        },
        deserialize: (str) => {
          const parsed = JSON.parse(str);
          return {
            state: {
              ...parsed,
              fileIdCache: new Map(parsed.fileIdCache || []),
              fileDataCache: new Map(parsed.fileDataCache || []),
            },
          };
        },
      }
    ),
    { name: 'FileSyncStore' }
  )
);

// ============================================================================
// 选择器钩子
// ============================================================================

export const useFileSyncStatus = () => {
  return useFileSyncStore((state) => ({
    isSyncing: state.isSyncing,
    syncProgress: state.syncProgress,
    syncErrors: state.syncErrors,
  }), shallow);
};

export const useFileIdMapping = () => {
  return useFileSyncStore((state) => ({
    getFileId: state.getFileId,
    getFileData: state.getFileData,
    syncSingleFile: state.syncSingleFile,
  }), shallow);
};

export const useCaseManagement = () => {
  return useFileSyncStore((state) => ({
    currentCaseId: state.currentCaseId,
    availableCases: state.availableCases,
    setCurrentCase: state.setCurrentCase,
    loadAvailableCases: state.loadAvailableCases,
  }), shallow);
};
