/* SearchPanel.css - Phase 7A: 高级搜索面板样式 */

/* ============================================================================
 * 搜索面板主容器
 * ============================================================================ */

.search-panel {
  border: 1px solid #333;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.search-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #333;
  background-color: rgba(164, 159, 154, 0.05);
}

.panel-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #A49F9A;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  padding: 0;
  transition: color 0.2s ease;
}

.panel-toggle:hover {
  color: #B5B0AB;
}

.toggle-icon {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.panel-title {
  font-weight: 600;
}

.active-indicator {
  background-color: rgba(164, 159, 154, 0.2);
  color: #A49F9A;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.result-count {
  font-size: 12px;
  color: #A49F9A;
  opacity: 0.8;
  font-weight: 500;
}

.search-panel-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* ============================================================================
 * 搜索条件构建器
 * ============================================================================ */

.search-criterion-builder {
  border: 1px solid rgba(164, 159, 154, 0.2);
  border-radius: 6px;
  padding: 12px;
  background-color: rgba(164, 159, 154, 0.05);
}

.builder-row {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr auto;
  gap: 8px;
  align-items: center;
}

.field-select,
.operator-select {
  padding: 6px 8px;
  border: 1px solid rgba(164, 159, 154, 0.3);
  border-radius: 4px;
  background-color: #040709;
  color: #A49F9A;
  font-size: 12px;
  cursor: pointer;
}

.field-select:focus,
.operator-select:focus {
  outline: none;
  border-color: #A49F9A;
  box-shadow: 0 0 0 2px rgba(164, 159, 154, 0.2);
}

.value-input-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.value-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid rgba(164, 159, 154, 0.3);
  border-radius: 4px;
  background-color: #040709;
  color: #A49F9A;
  font-size: 12px;
}

.value-input:focus {
  outline: none;
  border-color: #A49F9A;
  box-shadow: 0 0 0 2px rgba(164, 159, 154, 0.2);
}

.value-input::placeholder {
  color: rgba(164, 159, 154, 0.5);
}

.date-input {
  color-scheme: dark;
}

.case-sensitive-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  user-select: none;
}

.case-sensitive-toggle input[type="checkbox"] {
  display: none;
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 1px solid rgba(164, 159, 154, 0.3);
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  color: rgba(164, 159, 154, 0.5);
  transition: all 0.2s ease;
}

.case-sensitive-toggle input[type="checkbox"]:checked + .toggle-label {
  background-color: rgba(164, 159, 154, 0.2);
  border-color: #A49F9A;
  color: #A49F9A;
}

.add-button {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(164, 159, 154, 0.3);
  border-radius: 6px;
  background-color: rgba(164, 159, 154, 0.1);
  color: #A49F9A;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.add-button:hover:not(:disabled) {
  background-color: rgba(164, 159, 154, 0.2);
  border-color: #A49F9A;
  transform: translateY(-1px);
}

.add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ============================================================================
 * 活跃条件列表
 * ============================================================================ */

.active-criteria {
  min-height: 40px;
  padding: 8px;
  border: 1px dashed rgba(164, 159, 154, 0.2);
  border-radius: 6px;
  background-color: rgba(164, 159, 154, 0.02);
}

/* ============================================================================
 * 预设搜索
 * ============================================================================ */

.search-presets {
  border-top: 1px solid rgba(164, 159, 154, 0.2);
  padding-top: 16px;
}

.presets-title {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #A49F9A;
  opacity: 0.8;
}

.presets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.preset-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  border: 1px solid rgba(164, 159, 154, 0.2);
  border-radius: 6px;
  background-color: rgba(164, 159, 154, 0.05);
  color: #A49F9A;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.preset-button:hover {
  background-color: rgba(164, 159, 154, 0.1);
  border-color: rgba(164, 159, 154, 0.4);
  transform: translateY(-1px);
}

.preset-label {
  font-size: 12px;
  font-weight: 500;
}

.preset-description {
  font-size: 10px;
  opacity: 0.7;
  line-height: 1.2;
}

/* ============================================================================
 * 响应式设计
 * ============================================================================ */

@media (max-width: 600px) {
  .builder-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .value-input-container {
    grid-column: 1;
  }

  .add-button {
    justify-self: center;
    width: 100%;
    max-width: 120px;
  }

  .presets-grid {
    grid-template-columns: 1fr 1fr;
  }

  .search-panel-content {
    padding: 12px;
    gap: 12px;
  }
}

/* ============================================================================
 * 动画效果
 * ============================================================================ */

.search-panel-content {
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}

.search-criterion-builder {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ============================================================================
 * 状态指示器
 * ============================================================================ */

.search-panel.has-results .search-panel-header {
  border-left: 3px solid rgba(164, 159, 154, 0.5);
}

.search-panel.no-results .result-count {
  color: rgba(255, 107, 107, 0.8);
}

.search-panel.loading .result-count::after {
  content: '...';
  animation: dots 1s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* ============================================================================
   Phase 8A: 搜索模式切换样式
   ============================================================================ */

.search-mode-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: rgba(164, 159, 154, 0.1);
  border-radius: 6px;
  padding: 2px;
}

.mode-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
}

.mode-button:hover {
  background-color: rgba(164, 159, 154, 0.1);
  color: #A49F9A;
}

.mode-button--active {
  background-color: #A49F9A;
  color: #040709;
}

.mode-button--active:hover {
  background-color: #B5B0AB;
}

/* 自然语言搜索集成样式 */
.natural-search-integration {
  margin: 0;
  background: none;
  border: none;
  padding: 0;
}
