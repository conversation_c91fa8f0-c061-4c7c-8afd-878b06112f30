// src/components/ui/WorkbenchContent.tsx
import React from 'react';

interface WorkbenchContentProps {
  className?: string;
}

export function WorkbenchContent({ className }: WorkbenchContentProps) {
  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 工作台标题栏 */}
      <div className="flex items-center justify-between mb-space-2">
        <h3 className="text-sm font-medium text-mizzy-text-title">工作台</h3>
        <div className="flex items-center gap-space-1">
          <button className="text-xs text-mizzy-text-content hover:text-mizzy-text-title">
            清空
          </button>
        </div>
      </div>

      {/* 工作台内容区域 */}
      <div className="flex-1 bg-mizzy-bg-main">
        {/* 清空所有提示内容 */}
      </div>

      {/* 工作台操作栏 */}
      <div className="flex items-center justify-between mt-space-2 pt-space-2 border-t border-mizzy-border-base">
        <div className="flex items-center gap-space-1">
          <button className="px-space-2 py-space-1 text-xs bg-mizzy-bg-button-special border border-mizzy-border-base rounded-sm hover:border-mizzy-border-highlight text-mizzy-text-content">
            批量处理
          </button>
          <button className="px-space-2 py-space-1 text-xs bg-mizzy-bg-button-special border border-mizzy-border-base rounded-sm hover:border-mizzy-border-highlight text-mizzy-text-content">
            导出
          </button>
        </div>
        <div className="text-xs text-mizzy-text-content">
          0 项目
        </div>
      </div>
    </div>
  );
}
