interface ArchiveIconProps {
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export function ArchiveIcon({ className = '', onClick, style }: ArchiveIconProps) {
  return (
    <svg
      className={`icon ${className}`}
      onClick={onClick}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ cursor: 'pointer', ...style }}
    >
      <path
        d="M2 3h12v2H2V3zM3 6h10v7a1 1 0 01-1 1H4a1 1 0 01-1-1V6z"
        stroke="var(--color-text-title)"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 9h4"
        stroke="var(--color-text-title)"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
}
