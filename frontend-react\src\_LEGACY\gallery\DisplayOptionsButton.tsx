export function DisplayOptionsButton() {
  return (
    <button
      style={{
        padding: '6px 8px',
        backgroundColor: 'var(--color-surface-button)',
        border: '1px solid var(--color-border-primary)',
        borderRadius: 'var(--border-radius-sm)',
        color: 'var(--color-text-title)',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'background-color 0.2s ease'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = 'var(--color-surface-button-hover)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'var(--color-surface-button)';
      }}
      // onClick={() => console.log('Display options clicked')} // [CLEANED]
    >
      {/* Filter/Display Options Icon */}
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2 4h12M4 8h8M6 12h4"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </svg>
    </button>
  );
}
