// 🔥 BEDROCK: 案例列表容器组件 - 骨架核心组件
// 职责：渲染从 GET /api/v1/cases 获取的案例列表

import React from 'react';
import { useCaseStore } from '@/store/useCaseStore';
import { CaseItem } from './CaseItem';

export function CaseList() {
  console.log(`%c🔥 BEDROCK: Rendering CaseList (案例列表容器)`, 'color: #FF6B35; font-weight: bold;');

  // 🔥 BEDROCK: 只使用核心状态 - 案例列表数据
  const allCases = useCaseStore(state => state.allCases);
  const isLoading = useCaseStore(state => state.isLoading);
  const error = useCaseStore(state => state.error);

  // 🔥 BEDROCK: 简单的加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载案例中...</div>
      </div>
    );
  }

  // 🔥 BEDROCK: 简单的错误状态
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">加载失败: {error}</div>
      </div>
    );
  }

  // 🔥 BEDROCK: 简单的空状态
  if (allCases.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">暂无案例</div>
      </div>
    );
  }

  // 🔥 BEDROCK: 核心渲染 - 简单的案例列表
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4 text-gray-800">案例列表</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {allCases.map((caseItem) => (
          <CaseItem key={caseItem.id} case={caseItem} />
        ))}
      </div>
    </div>
  );
}
