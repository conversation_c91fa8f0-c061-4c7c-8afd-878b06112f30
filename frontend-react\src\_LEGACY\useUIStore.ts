// useUIStore.ts - Phase 6: 核心UI状态管理（重构后）
// 专注于核心UI关注点：加载状态、模态框、通知、主题等

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';

// ============================================================================
// 类型定义
// ============================================================================

interface UIState {
  // ========================================
  // 核心UI状态
  // ========================================
  // 全局加载状态
  isLoading: boolean;
  loadingMessage: string;

  // 模态框状态
  activeModal: string | null;
  modalData: any;

  // 通知状态
  notifications: Notification[];

  // 主题状态
  theme: 'light' | 'dark' | 'auto';

  // 全屏状态
  isFullscreenGallery: boolean;

  // 搜索状态
  searchQuery: string;

  // ========================================
  // 画廊视图状态（保留核心UI相关）
  // ========================================
  galleryLayout: 'grid' | 'list';
  galleryZoomLevel: number;
  gallerySortBy: 'name' | 'date' | 'size' | 'type';
  gallerySortOrder: 'asc' | 'desc';
  showFileName: boolean;
  showFileInfo: boolean;

  // 新UI画廊状态
  galleryViewMode: 'grid' | 'list';
  galleryZoomLevelNew: number[];
  showImageInfo: boolean;
}

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
}

interface UIActions {
  // ========================================
  // 全局加载状态控制
  // ========================================
  setLoading: (loading: boolean, message?: string) => void;

  // ========================================
  // 模态框控制
  // ========================================
  openModal: (modalId: string, data?: any) => void;
  closeModal: () => void;

  // ========================================
  // 通知控制
  // ========================================
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // ========================================
  // 主题控制
  // ========================================
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;

  // ========================================
  // 全屏控制
  // ========================================
  toggleFullscreenGallery: () => void;
  setFullscreenGallery: (fullscreen: boolean) => void;

  // ========================================
  // 搜索控制
  // ========================================
  setSearchQuery: (query: string) => void;
  clearSearch: () => void;

  // ========================================
  // 画廊视图控制
  // ========================================
  setGalleryLayout: (layout: 'grid' | 'list') => void;
  setGalleryZoomLevel: (level: number) => void;
  setGallerySortBy: (sortBy: 'name' | 'date' | 'size' | 'type') => void;
  setGallerySortOrder: (order: 'asc' | 'desc') => void;
  toggleShowFileName: () => void;
  toggleShowFileInfo: () => void;

  // 新UI画廊控制
  setGalleryViewMode: (mode: 'grid' | 'list') => void;
  setGalleryZoomNew: (level: number[]) => void;
  toggleShowImageInfo: () => void;

  // ========================================
  // 重置功能
  // ========================================
  resetUI: () => void;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: UIState = {
  // 核心UI状态
  isLoading: false,
  loadingMessage: '',
  activeModal: null,
  modalData: null,
  notifications: [],
  theme: 'auto',
  isFullscreenGallery: false,
  searchQuery: '',

  // 画廊视图状态
  galleryLayout: 'grid',
  galleryZoomLevel: 50,
  gallerySortBy: 'name',
  gallerySortOrder: 'asc',
  showFileName: true,
  showFileInfo: false,

  // 新UI画廊状态
  galleryViewMode: 'grid',
  galleryZoomLevelNew: [5],
  showImageInfo: true,
};

// ============================================================================
// Store实现
// ============================================================================

export const useUIStore = create<UIState & UIActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // ========================================
      // 全局加载状态控制实现
      // ========================================
      setLoading: (loading, message = '') =>
        set({ isLoading: loading, loadingMessage: message }),

      // ========================================
      // 模态框控制实现
      // ========================================
      openModal: (modalId, data = null) =>
        set({ activeModal: modalId, modalData: data }),

      closeModal: () =>
        set({ activeModal: null, modalData: null }),

      // ========================================
      // 通知控制实现
      // ========================================
      addNotification: (notification) =>
        set((state) => ({
          notifications: [
            ...state.notifications,
            {
              ...notification,
              id: `notification-${Date.now()}-${Math.random()}`,
              timestamp: Date.now(),
            },
          ],
        })),

      removeNotification: (id) =>
        set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id),
        })),

      clearNotifications: () =>
        set({ notifications: [] }),

      // ========================================
      // 主题控制实现
      // ========================================
      setTheme: (theme) => set({ theme }),

      // ========================================
      // 全屏控制实现
      // ========================================
      toggleFullscreenGallery: () =>
        set((state) => ({ isFullscreenGallery: !state.isFullscreenGallery })),

      setFullscreenGallery: (fullscreen) =>
        set({ isFullscreenGallery: fullscreen }),

      // ========================================
      // 搜索控制实现
      // ========================================
      setSearchQuery: (query) => set({ searchQuery: query }),

      clearSearch: () => set({ searchQuery: '' }),

      // ========================================
      // 画廊视图控制实现
      // ========================================
      setGalleryLayout: (layout) => set({ galleryLayout: layout }),

      setGalleryZoomLevel: (level) => set({ galleryZoomLevel: level }),

      setGallerySortBy: (sortBy) => set({ gallerySortBy: sortBy }),

      setGallerySortOrder: (order) => set({ gallerySortOrder: order }),

      toggleShowFileName: () =>
        set((state) => ({ showFileName: !state.showFileName })),

      toggleShowFileInfo: () =>
        set((state) => ({ showFileInfo: !state.showFileInfo })),

      // 新UI画廊控制实现
      setGalleryViewMode: (mode) => set({ galleryViewMode: mode }),

      setGalleryZoomNew: (level) => set({ galleryZoomLevelNew: level }),

      toggleShowImageInfo: () =>
        set((state) => ({ showImageInfo: !state.showImageInfo })),

      // ========================================
      // 重置功能实现
      // ========================================
      resetUI: () => set(initialState),
    }),
    {
      name: 'mizzy-star-ui-store',
    }
  )
);

// ============================================================================
// 选择器Hooks（性能优化）
// ============================================================================

export const useLoadingState = () => useUIStore((state) => ({
  isLoading: state.isLoading,
  loadingMessage: state.loadingMessage,
  setLoading: state.setLoading,
}), shallow);

export const useModalState = () => useUIStore((state) => ({
  activeModal: state.activeModal,
  modalData: state.modalData,
  openModal: state.openModal,
  closeModal: state.closeModal,
}), shallow);

export const useNotifications = () => useUIStore((state) => ({
  notifications: state.notifications,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications,
}), shallow);

export const useTheme = () => useUIStore((state) => ({
  theme: state.theme,
  setTheme: state.setTheme,
}), shallow);

export const useGallerySettings = () => useUIStore((state) => ({
  galleryViewMode: state.galleryViewMode,
  galleryZoomLevelNew: state.galleryZoomLevelNew,
  showImageInfo: state.showImageInfo,
  setGalleryViewMode: state.setGalleryViewMode,
  setGalleryZoomNew: state.setGalleryZoomNew,
  toggleShowImageInfo: state.toggleShowImageInfo,
}), shallow);

export const useUISearchState = () => useUIStore((state) => ({
  searchQuery: state.searchQuery,
  setSearchQuery: state.setSearchQuery,
  clearSearch: state.clearSearch,
}), shallow);

export default useUIStore;
