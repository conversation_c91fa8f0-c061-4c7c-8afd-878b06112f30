// src/components/toolbars/WorkbenchToolbarSet.tsx
import React from 'react';
// 根据蓝图导入所有需要的图标
import { ButtonAddIcon } from '../../assets/icons/ButtonAddIcon';
import { RefreshAltIcon } from '../../assets/icons/RefreshAltIcon';
import { MinusIcon } from '../../assets/icons/MinusIcon';
import { PlusAddIcon } from '../../assets/icons/PlusAddIcon';

import { IconButton } from '../ui/IconButton';
import { TextButton } from '../ui/TextButton';

// 创建一个简单的 Toolbar 组件来统一布局
function Toolbar({ children, justify = 'justify-between' }: { children: React.ReactNode, justify?: string }) {
    // 严格按照蓝图：高度16px，间距4px
    return (
        <div className={`w-full h-[16px] flex items-center ${justify} mb-[4px]`}>
            {children}
        </div>
    );
}

export function WorkbenchToolbarSet() {
    return (
        <div className="flex flex-col">
            {/* 工作台工具栏第一行 */}
            <Toolbar>
                {/* 左对齐：工作台标题 + 添加按钮 */}
                <div className="flex items-center gap-[4px]">
                    <TextButton>工作台</TextButton>
                    <IconButton icon={ButtonAddIcon} tooltip="添加" />
                </div>
                {/* 右对齐：刷新按钮 */}
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={RefreshAltIcon} tooltip="刷新" />
                </div>
            </Toolbar>


        </div>
    );
}
