import * as React from 'react';
import { Button, Input, TagItem } from '@/components/ui';
import { cn } from '@/utils/cn';

// ============================================================================
// CatalogPanel Component Interface
// ============================================================================

export interface CatalogPanelProps {
  /**
   * 当前档案库名称
   */
  currentLibraryName?: string;
  
  /**
   * 搜索查询
   */
  searchQuery?: string;
  
  /**
   * 搜索变化回调
   */
  onSearchChange?: (query: string) => void;
  
  /**
   * 标签数据 - 模拟数据结构
   */
  tagCategories?: Array<{
    id: string;
    name: string;
    icon: string;
    tags: Array<{
      id: string;
      name: string;
      count: number;
      selected?: boolean;
    }>;
    expanded?: boolean;
  }>;
  
  /**
   * 标签点击回调
   */
  onTagClick?: (categoryId: string, tagId: string) => void;
  
  /**
   * 标签类别展开/收起回调
   */
  onCategoryToggle?: (categoryId: string) => void;
  
  className?: string;
}

// ============================================================================
// CatalogPanel Component Implementation
// ============================================================================

/**
 * CatalogPanel 组件 - 目录栏面板
 * 
 * 实现 Mizzy Star 的标签管理和筛选功能：
 * 1. 档案库选择和管理
 * 2. 标签搜索和筛选
 * 3. 分层标签系统展示
 * 4. 标签看板和收藏
 * 
 * 对应原始需求中的【目录栏】功能
 */
const CatalogPanel = React.forwardRef<HTMLDivElement, CatalogPanelProps>(
  ({
    currentLibraryName = "默认档案库",
    searchQuery = "",
    onSearchChange,
    tagCategories = [],
    onTagClick,
    onCategoryToggle,
    className,
    ...props
  }, ref) => {
    
    return (
      <div
        ref={ref}
        className={cn(
          'h-full flex flex-col bg-card',
          className
        )}
        {...props}
      >
        {/* 顶部工具栏 */}
        <div className="flex-shrink-0 p-4 border-b border-border space-y-3">
          {/* 首选项和全局视图 */}
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="sm">
              ⚙️ 首选项
            </Button>
            <Button variant="ghost" size="sm">
              🔍 全局视图
            </Button>
          </div>
          
          {/* 档案库名称 */}
          <div className="space-y-2">
            <Button variant="outline" className="w-full justify-start">
              📚 {currentLibraryName}
              <span className="ml-auto">▼</span>
            </Button>
          </div>
          
          {/* 标签搜索 */}
          <div className="space-y-2">
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">🔍</span>
              <Input
                placeholder="搜索标签..."
                value={searchQuery}
                onChange={(e) => onSearchChange?.(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* 筛选器 */}
            <Button variant="outline" size="sm" className="w-full justify-start">
              🔽 筛选器
            </Button>
          </div>
        </div>

        {/* 标签区域 */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4 space-y-4">
            
            {/* 标签看板 */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                ⭐ 标签看板
              </h3>
              <div className="flex flex-wrap gap-1">
                <TagItem variant="primary" size="sm">风景</TagItem>
                <TagItem variant="secondary" size="sm">人像</TagItem>
                <TagItem variant="success" size="sm">街拍</TagItem>
              </div>
            </div>

            {/* 标签分类 */}
            {tagCategories.length > 0 ? (
              tagCategories.map((category) => (
                <div key={category.id} className="space-y-2">
                  <button
                    className="w-full flex items-center justify-between text-sm font-medium text-foreground hover:text-primary transition-colors"
                    onClick={() => onCategoryToggle?.(category.id)}
                  >
                    <span className="flex items-center gap-2">
                      <span>{category.icon}</span>
                      <span>{category.name}</span>
                      <span className="text-xs text-muted-foreground">
                        ({category.tags.length})
                      </span>
                    </span>
                    <span className="text-xs">
                      {category.expanded ? '▼' : '▶'}
                    </span>
                  </button>
                  
                  {category.expanded && (
                    <div className="pl-4 space-y-1">
                      {category.tags.map((tag) => (
                        <TagItem
                          key={tag.id}
                          variant={tag.selected ? "primary" : "outline"}
                          size="sm"
                          count={tag.count}
                          clickable
                          onClick={() => onTagClick?.(category.id, tag.id)}
                          className="w-full justify-start"
                        >
                          {tag.name}
                        </TagItem>
                      ))}
                    </div>
                  )}
                </div>
              ))
            ) : (
              // 默认标签分类展示
              <>
                {/* 研究者标签 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-foreground flex items-center gap-2">
                    👤 研究者标签
                  </h3>
                  <div className="space-y-1">
                    <TagItem variant="outline" size="sm" count={24} clickable>
                      摄影师：张三
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={18} clickable>
                      拍摄地点：北京
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={12} clickable>
                      拍摄年代：2024
                    </TagItem>
                  </div>
                </div>

                {/* 元数据 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-foreground flex items-center gap-2">
                    📊 元数据
                  </h3>
                  <div className="space-y-1">
                    <TagItem variant="outline" size="sm" count={156} clickable>
                      JPEG
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={89} clickable>
                      1920×1080
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={67} clickable>
                      Canon EOS R5
                    </TagItem>
                  </div>
                </div>

                {/* 计算机视觉 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-foreground flex items-center gap-2">
                    🤖 计算机视觉
                  </h3>
                  <div className="space-y-1">
                    <TagItem variant="success" size="sm" count={45} clickable>
                      标准曝光
                    </TagItem>
                    <TagItem variant="warning" size="sm" count={23} clickable>
                      轻微过曝
                    </TagItem>
                    <TagItem variant="primary" size="sm" count={78} clickable>
                      高清晰度
                    </TagItem>
                  </div>
                </div>

                {/* 内容识别 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-foreground flex items-center gap-2">
                    🎯 内容识别
                  </h3>
                  <div className="space-y-1">
                    <TagItem variant="outline" size="sm" count={89} clickable>
                      风景
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={67} clickable>
                      建筑
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={45} clickable>
                      人物
                    </TagItem>
                  </div>
                </div>

                {/* 语义识别 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-foreground flex items-center gap-2">
                    🧠 语义识别
                  </h3>
                  <div className="space-y-1">
                    <TagItem variant="outline" size="sm" count={34} clickable>
                      城市夜景
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={28} clickable>
                      自然风光
                    </TagItem>
                    <TagItem variant="outline" size="sm" count={19} clickable>
                      人文纪实
                    </TagItem>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* 底部屏蔽器 */}
        <div className="flex-shrink-0 p-4 border-t border-border">
          <Button variant="outline" size="sm" className="w-full">
            🚫 屏蔽器
          </Button>
        </div>
      </div>
    );
  }
);

CatalogPanel.displayName = 'CatalogPanel';

export { CatalogPanel };
