// MainLayout.tsx - Phase 6: 重构后的配置驱动布局
// 使用layoutConfig和专用stores实现解耦的布局系统

import React from 'react';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import { 
  usePanelVisibility, 
  useLayoutSizes,
  useFileSystemStore,
  useSelectionStore 
} from '@/store';
import { 
  layoutConfig, 
  calculateHorizontalLayout, 
  calculateVerticalLayout,
  validateLayoutSizes,
  normalizeLayoutSizes 
} from '@/config/layoutConfig';
import { useDebounce } from '@/hooks/useDebounce';
import './MainLayout.css';

// ============================================================================
// 布局持久化Hook
// ============================================================================

const useLayoutPersistence = () => {
  const { 
    isCatalogVisible, 
    isInfoVisible, 
    isWorkbenchVisible 
  } = usePanelVisibility();
  
  const { 
    horizontalSizes, 
    verticalSizes, 
    setHorizontalSizes, 
    setVerticalSizes 
  } = useLayoutSizes();

  // 从localStorage恢复布局尺寸
  const getStoredLayout = React.useCallback((key: string, fallback: number[]) => {
    try {
      const stored = localStorage.getItem(key);
      if (stored) {
        const parsed = JSON.parse(stored);
        if (validateLayoutSizes(parsed, fallback.length)) {
          // console.log('🔧 Phase 6: 恢复布局尺寸:', key, parsed); // [CLEANED]
          return normalizeLayoutSizes(parsed);
        }
      }
    } catch (error) {
      console.warn('🔧 Phase 6: 恢复布局失败:', error);
    }
    return fallback;
  }, []);

  // 计算默认布局
  const defaultHorizontalLayout = React.useMemo(() => 
    calculateHorizontalLayout(isCatalogVisible, isInfoVisible),
    [isCatalogVisible, isInfoVisible]
  );

  const defaultVerticalLayout = React.useMemo(() => 
    calculateVerticalLayout(isWorkbenchVisible),
    [isWorkbenchVisible]
  );

  // 获取实际使用的布局尺寸
  const currentHorizontalLayout = React.useMemo(() => 
    horizontalSizes.length > 0 ? horizontalSizes : 
    getStoredLayout(layoutConfig.storageKeys.horizontal, defaultHorizontalLayout),
    [horizontalSizes, getStoredLayout, defaultHorizontalLayout]
  );

  const currentVerticalLayout = React.useMemo(() => 
    verticalSizes.length > 0 ? verticalSizes :
    getStoredLayout(layoutConfig.storageKeys.vertical, defaultVerticalLayout),
    [verticalSizes, getStoredLayout, defaultVerticalLayout]
  );

  // 防抖的localStorage保存
  const debouncedSaveHorizontal = useDebounce((sizes: number[]) => {
    try {
      localStorage.setItem(layoutConfig.storageKeys.horizontal, JSON.stringify(sizes));
      // console.log('🔧 Phase 6: 保存水平布局:', sizes); // [CLEANED]
    } catch (error) {
      console.warn('🔧 Phase 6: 保存水平布局失败:', error);
    }
  }, layoutConfig.constraints.debounceDelay);

  const debouncedSaveVertical = useDebounce((sizes: number[]) => {
    try {
      localStorage.setItem(layoutConfig.storageKeys.vertical, JSON.stringify(sizes));
      // console.log('🔧 Phase 6: 保存垂直布局:', sizes); // [CLEANED]
    } catch (error) {
      console.warn('🔧 Phase 6: 保存垂直布局失败:', error);
    }
  }, layoutConfig.constraints.debounceDelay);

  // 布局变化处理
  const handleHorizontalLayoutChange = React.useCallback((sizes: number[]) => {
    setHorizontalSizes(sizes);
    debouncedSaveHorizontal(sizes);
  }, [setHorizontalSizes, debouncedSaveHorizontal]);

  const handleVerticalLayoutChange = React.useCallback((sizes: number[]) => {
    setVerticalSizes(sizes);
    debouncedSaveVertical(sizes);
  }, [setVerticalSizes, debouncedSaveVertical]);

  return {
    currentHorizontalLayout,
    currentVerticalLayout,
    handleHorizontalLayoutChange,
    handleVerticalLayoutChange,
  };
};

// ============================================================================
// 面板组件（临时，将在后续步骤中提取）
// ============================================================================

// 这些组件将在后续步骤中从原MainLayout提取
const CatalogPanel: React.FC = () => <div>Catalog Panel (临时)</div>;
const GalleryPanel: React.FC = () => <div>Gallery Panel (临时)</div>;
const WorkbenchPanel: React.FC = () => <div>Workbench Panel (临时)</div>;
const InfoPanel: React.FC = () => <div>Info Panel (临时)</div>;

// 导出面板组件供layoutConfig使用
export { CatalogPanel, GalleryPanel, WorkbenchPanel, InfoPanel };

// ============================================================================
// 主布局组件
// ============================================================================

export const MainLayout: React.FC = () => {
  const { isCatalogVisible, isInfoVisible, isWorkbenchVisible } = usePanelVisibility();
  const {
    currentHorizontalLayout,
    currentVerticalLayout,
    handleHorizontalLayoutChange,
    handleVerticalLayoutChange,
  } = useLayoutPersistence();

  // 错误边界状态
  const [hasError, setHasError] = React.useState(false);

  // 错误边界处理
  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('🚨 Layout Error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return (
      <div className="main-layout error-state">
        <div className="error-message">
          <h2>布局系统错误</h2>
          <p>布局系统遇到错误，请刷新页面重试。</p>
          <button onClick={() => window.location.reload()}>刷新页面</button>
        </div>
      </div>
    );
  }

  return (
    <div className="main-layout">
      <React.Suspense fallback={<div className="loading-layout">加载布局中...</div>}>
        <PanelGroup 
          direction="horizontal" 
          onLayout={handleHorizontalLayoutChange}
          className="panel-group-horizontal"
        >
          {/* 左侧：Catalog 面板 */}
          {isCatalogVisible && (
            <>
              <Panel 
                defaultSize={currentHorizontalLayout[0] || 20} 
                minSize={15} 
                maxSize={35}
                className="panel-catalog"
              >
                <CatalogPanel />
              </Panel>
              <PanelResizeHandle className="panel-resize-handle" />
            </>
          )}

          {/* 中央区域：Gallery 和 Workbench 的垂直分割 */}
          <Panel 
            defaultSize={currentHorizontalLayout[isCatalogVisible ? 1 : 0] || 50} 
            minSize={30}
            className="panel-center"
          >
            {isWorkbenchVisible ? (
              <PanelGroup 
                direction="vertical"
                onLayout={handleVerticalLayoutChange}
                className="panel-group-vertical"
              >
                {/* Gallery 面板 */}
                <Panel 
                  defaultSize={currentVerticalLayout[0] || 70} 
                  minSize={40}
                  className="panel-gallery"
                >
                  <GalleryPanel />
                </Panel>
                
                <PanelResizeHandle className="panel-resize-handle vertical" />
                
                {/* Workbench 面板 */}
                <Panel 
                  defaultSize={currentVerticalLayout[1] || 30} 
                  minSize={20} 
                  maxSize={60}
                  className="panel-workbench"
                >
                  <WorkbenchPanel />
                </Panel>
              </PanelGroup>
            ) : (
              <GalleryPanel />
            )}
          </Panel>

          {/* 右侧：Info 面板 */}
          {isInfoVisible && (
            <>
              <PanelResizeHandle className="panel-resize-handle" />
              <Panel 
                defaultSize={currentHorizontalLayout[currentHorizontalLayout.length - 1] || 15} 
                minSize={12} 
                maxSize={40}
                className="panel-info"
              >
                <InfoPanel />
              </Panel>
            </>
          )}
        </PanelGroup>
      </React.Suspense>
    </div>
  );
};

export default MainLayout;
