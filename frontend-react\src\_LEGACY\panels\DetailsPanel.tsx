// 详情面板 (D区) - 负责显示选中项目的详细信息和元数据
import React from 'react';

interface DetailsPanelProps {
  className?: string;
}

const DetailsPanel = React.forwardRef<HTMLDivElement, DetailsPanelProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`flex-1 bg-mizzy-bg-panel flex flex-col ${className || ''}`}
        {...props}
      >
        {/* 标签内容布局 - 在工具栏第二行和底部工具栏之间 */}
        <div className="flex-1 p-4 text-mizzy-text-content">
          {/* 详情面板内容将在后续实现 */}
        </div>
      </div>
    );
  }
);

DetailsPanel.displayName = 'DetailsPanel';

export { DetailsPanel };
export default DetailsPanel;
