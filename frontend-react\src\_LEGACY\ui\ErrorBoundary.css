/* ErrorBoundary.css - Phase 6: 错误边界样式 */

.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 20px;
  background-color: #040709;
  color: #A49F9A;
  border-radius: 8px;
  border: 1px solid #333;
}

.error-boundary-content {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.error-boundary h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #A49F9A;
}

.error-message {
  margin: 0 0 24px 0;
  font-size: 16px;
  line-height: 1.5;
  color: #A49F9A;
  opacity: 0.8;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 24px;
}

.error-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-button.primary {
  background-color: #A49F9A;
  color: #040709;
}

.error-button.primary:hover {
  background-color: #B5B0AB;
  transform: translateY(-1px);
}

.error-button.secondary {
  background-color: transparent;
  color: #A49F9A;
  border: 1px solid #A49F9A;
}

.error-button.secondary:hover {
  background-color: rgba(164, 159, 154, 0.1);
  transform: translateY(-1px);
}

.error-details {
  text-align: left;
  margin-top: 20px;
  border: 1px solid #333;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
}

.error-details summary {
  padding: 12px;
  cursor: pointer;
  font-weight: 500;
  border-bottom: 1px solid #333;
  background-color: rgba(164, 159, 154, 0.05);
}

.error-details summary:hover {
  background-color: rgba(164, 159, 154, 0.1);
}

.error-stack {
  margin: 0;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #A49F9A;
  background-color: rgba(0, 0, 0, 0.3);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .error-boundary {
    padding: 16px;
    min-height: 150px;
  }

  .error-boundary-content {
    max-width: 100%;
  }

  .error-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }

  .error-boundary h2 {
    font-size: 20px;
  }

  .error-message {
    font-size: 14px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-button {
    width: 100%;
    max-width: 200px;
  }
}
