// GalleryWrapper - Feature flag controlled gallery component wrapper
// 功能开关控制的画廊组件包装器

import React from 'react';
import { useFeatureFlag } from '@/utils/hooks/useFeatureFlags';
import { Gallery } from './Gallery';
// 🚨 移除僵尸组件：GalleryPanel
// import { GalleryPanel } from '@/components/panels/GalleryPanel';
import { VirtualizedGallery } from './VirtualizedGallery'; // Phase 4: 导入虚拟化Gallery
import { StatusDisplay } from '@/components/feedback/StatusDisplay';
// ✅ 导入纯净的文件显示组件
import { FileDisplayArea } from '@/components/files/FileDisplayArea';
import { useUIStore, useFileSystemState, useSearchCriteria, useSearchState, useSearchPerformance } from '@/store';
import { shallow } from 'zustand/shallow';
// Phase 7B: 导入元数据存储
import { useMetadataStore } from '@/store/useMetadataStore';
// Phase 7C: 导入案例管理
import { useCurrentCase } from '@/store';
import { useDirectoryContents } from '@/hooks/useFileSystem';
import { applySearch } from '@/lib/filterEngine'; // Phase 7A: 导入搜索引擎
import type { FileEntry } from '@/types/electron'; // Phase 5C: 导入FileEntry类型

// ============================================================================
// 接口定义
// ============================================================================

interface GalleryWrapperProps {
  className?: string;
  isVisible?: boolean;
}

// ============================================================================
// GalleryWrapper 组件
// ============================================================================

export const GalleryWrapper: React.FC<GalleryWrapperProps> = (props) => {
  // 🚨 临时禁用新Gallery组件，强制使用GalleryPanel
  const useNewGallery = false; // useFeatureFlag('useNewGallery');

  console.log(`%c🔧 DEBUG: GalleryWrapper useNewGallery = ${useNewGallery}`, 'color: #2196F3; font-weight: bold;');

  // 获取状态和数据 - 使用专用选择器避免无限循环
  const {
    galleryLayout,
    galleryZoomLevel,
    gallerySortBy,
    gallerySortOrder,
    searchQuery,
    showFileName,
    showFileInfo,
    toggleFileSelection,
    setSearchQuery,
    activeFilter,
  } = useUIStore((state) => ({
    galleryLayout: state.galleryLayout,
    galleryZoomLevel: state.galleryZoomLevel,
    gallerySortBy: state.gallerySortBy,
    gallerySortOrder: state.gallerySortOrder,
    searchQuery: state.searchQuery,
    showFileName: state.showFileName,
    showFileInfo: state.showFileInfo,
    toggleFileSelection: state.toggleFileSelection,
    setSearchQuery: state.setSearchQuery,
    activeFilter: state.activeFilter,
  }), shallow);

  // Phase 3 & 5C: 获取文件系统状态，包括选中文件功能
  const { currentDirectory, setSelectedFile } = useFileSystemState();

  // Phase 5C: 获取多选状态和操作 - 使用专用选择器避免无限循环
  const {
    selectedFiles,
    setSelectedFiles,
    addToSelection,
    removeFromSelection,
    clearSelection,
  } = useUIStore((state) => ({
    selectedFiles: state.selectedFiles,
    setSelectedFiles: state.setSelectedFiles,
    addToSelection: state.addToSelection,
    removeFromSelection: state.removeFromSelection,
    clearSelection: state.clearSelection,
  }), shallow);

  // Phase 7A: 获取搜索状态和操作
  const { criteria } = useSearchCriteria();
  const { setResultCount } = useSearchState();
  const { recordSearchPerformance } = useSearchPerformance();

  // Phase 7B: 获取元数据存储操作
  const { selectFile } = useMetadataStore();

  // Phase 7C: 获取当前案例信息
  const { currentCase, currentCaseId } = useCurrentCase();

  // Phase 3: 使用文件系统Hook获取丰富的FileEntry数据
  const { files: fileEntries, isLoading: filesLoading, error: filesError } = useDirectoryContents(currentDirectory);

  // Phase 7A: 应用高级搜索过滤 - ✅ 修复无限循环：移除不稳定的函数依赖
  const { files, filteredFiles, searchExecutionTime } = React.useMemo(() => {
    // 首先过滤掉目录，只保留文件
    const allFiles = fileEntries.filter(entry => !entry.isDirectory);

    // 应用高级搜索条件
    let searchResult = { files: allFiles, filteredCount: allFiles.length, executionTime: 0 };

    if (criteria.length > 0) {
      searchResult = applySearch(allFiles, criteria);
    }

    // 向后兼容：应用旧的文件扩展名过滤
    let finalFiles = searchResult.files;
    if (activeFilter?.type === 'fileExtension') {
      finalFiles = finalFiles.filter(entry => {
        const extension = entry.name.split('.').pop()?.toUpperCase() || 'NO_EXT';
        return extension === activeFilter.value;
      });
    }

    // 转换为Gallery期望的格式
    const formattedFiles = finalFiles.map((entry, index) => ({
      id: index + 1, // 临时ID，基于索引
      fileName: entry.name,
      filePath: entry.path,
      fileType: getFileType(entry.name),
      fileSize: entry.size, // Phase 3: 使用真实文件大小
      width: undefined,
      height: undefined,
      thumbnailPath: entry.thumbnail, // Phase 3: 使用生成的缩略图
      // Phase 3: 保存原始FileEntry引用，用于点击处理
      _fileEntry: entry,
    }));

    return {
      files: formattedFiles,
      filteredFiles: finalFiles,
      searchExecutionTime: searchResult.executionTime,
    };
  }, [fileEntries, criteria, activeFilter]); // ✅ 移除了不稳定的recordSearchPerformance依赖

  // ✅ 将性能记录移到单独的useEffect中，避免影响useMemo稳定性
  React.useEffect(() => {
    if (searchExecutionTime > 0) {
      recordSearchPerformance(searchExecutionTime);
    }
  }, [searchExecutionTime, recordSearchPerformance]);

  // Phase 7A: 更新搜索结果计数
  React.useEffect(() => {
    setResultCount(filteredFiles.length);
  }, [filteredFiles.length, setResultCount]);

  // 辅助函数：根据文件扩展名判断文件类型
  const getFileType = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase() || '';
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg'];

    if (imageExts.includes(ext)) return 'image';
    if (videoExts.includes(ext)) return 'video';
    if (audioExts.includes(ext)) return 'audio';
    return 'document';
  };

  // Phase 5C: 基于selectedFiles计算selectedFileIds（向后兼容）
  const selectedFileIds = React.useMemo(() => {
    return files
      .filter(file => selectedFiles.some(sf => sf.path === file._fileEntry?.path))
      .map(file => file.id);
  }, [files, selectedFiles]);

  // Phase 4: 检查是否使用虚拟化Gallery（大量文件时自动启用）
  const useVirtualizedGallery = files.length > 100; // 超过100个文件时使用虚拟化

  // 如果启用新画廊，使用新组件
  if (useNewGallery) {
    return <Gallery {...props} />;
  }

  const handleFileSelect = (fileId: number, selected: boolean, event?: React.MouseEvent) => {
    // 找到对应的FileEntry
    const selectedFileData = files.find(f => f.id === fileId);
    if (!selectedFileData || !selectedFileData._fileEntry) return;

    const fileEntry = selectedFileData._fileEntry;
    const isCurrentlySelected = selectedFiles.some(f => f.path === fileEntry.path);

    // Phase 5C: 多选逻辑
    if (event?.ctrlKey || event?.metaKey) {
      // Ctrl/Cmd+点击：切换选择
      if (isCurrentlySelected) {
        removeFromSelection(fileEntry.path);
        // console.log('🎯 Phase 5C: 从选择中移除文件:', fileEntry.name); // [CLEANED]
      } else {
        addToSelection(fileEntry);
        // console.log('🎯 Phase 5C: 添加文件到选择:', fileEntry.name); // [CLEANED]
      }
    } else if (event?.shiftKey && selectedFiles.length > 0) {
      // Shift+点击：范围选择
      const lastSelectedFile = selectedFiles[selectedFiles.length - 1];
      const lastSelectedIndex = files.findIndex(f => f._fileEntry?.path === lastSelectedFile.path);
      const currentIndex = files.findIndex(f => f.id === fileId);

      if (lastSelectedIndex !== -1 && currentIndex !== -1) {
        const startIndex = Math.min(lastSelectedIndex, currentIndex);
        const endIndex = Math.max(lastSelectedIndex, currentIndex);
        const rangeFiles = files.slice(startIndex, endIndex + 1)
          .map(f => f._fileEntry)
          .filter((entry): entry is FileEntry => entry !== undefined);

        setSelectedFiles(rangeFiles);
        // console.log('🎯 Phase 5C: 范围选择文件:', rangeFiles.length, '个文件'); // [CLEANED]
      }
    } else {
      // 普通点击：单选
      setSelectedFiles([fileEntry]);
      // console.log('🎯 Phase 5C: 单选文件:', fileEntry.name); // [CLEANED]

      // Phase 7B: 触发元数据加载
      selectFile(fileEntry.path);
    }

    // Phase 3: 向后兼容 - 保持旧的toggleFileSelection调用
    toggleFileSelection(fileId);
  };

  const handleFileDoubleClick = (file: any) => {
    // Phase 3: 双击时也选中文件
    if (file._fileEntry) {
      // console.log('🎯 Phase 3: 双击文件:', file._fileEntry.name); // [CLEANED]
      setSelectedFile(file._fileEntry);
    }
    // TODO: 实现文件预览逻辑
    // console.log('Double clicked file:', file); // [CLEANED]
  };

  const handleFileDelete = (file: any) => {
    // TODO: 实现文件删除逻辑
    // console.log('Delete file:', file); // [CLEANED]
  };

  // Phase 2: 处理不同的状态
  // 如果没有选择目录，显示提示
  if (!currentDirectory) {
    return (
      <StatusDisplay
        status="empty"
        message={
          <div className="space-y-4">
            <div className="text-6xl">📂</div>
            <h3 className="text-lg font-medium">开始导入图像</h3>
            <p className="text-muted-foreground">
              点击右上角的"🧪 真实数据测试"按钮选择包含图像的文件夹
            </p>
            <div className="text-sm text-info bg-info/10 border border-info/20 rounded-lg p-3">
              💡 您也可以直接拖拽文件夹到此区域（功能开发中）
            </div>
          </div>
        }
      />
    );
  }

  // 如果有错误，显示错误信息
  if (filesError) {
    return (
      <StatusDisplay
        status="error"
        message="加载失败"
        errorMessage={filesError}
      />
    );
  }

  // Phase 4: 使用虚拟化Gallery处理大量文件
  if (useVirtualizedGallery) {
    return (
      <VirtualizedGallery
        files={files}
        selectedFileIds={selectedFileIds}
        showFileName={showFileName}
        showFileInfo={showFileInfo}
        onFileSelect={handleFileSelect}
        onFileDoubleClick={handleFileDoubleClick}
        className="h-full"
      />
    );
  }

  // 🚨 使用纯净的FileDisplayArea替代有问题的GalleryPanel
  return <FileDisplayArea {...props} />;
};

// ============================================================================
// 导出
// ============================================================================

export default GalleryWrapper;
