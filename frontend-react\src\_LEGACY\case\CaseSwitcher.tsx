// CaseSwitcher.tsx - Phase 7C: 案例切换器组件
// 专业的案例切换下拉组件，集成到主应用工具栏

import React, { useState, useRef, useEffect } from 'react';
import { useCurrentCase, useCaseList, useFetchCases, useCaseOperations, useCaseErrors } from '@/store';
import type { Case } from '@/store/useCaseStore';
import './CaseSwitcher.css';

// ============================================================================
// 类型定义
// ============================================================================

interface CaseSwitcherProps {
  className?: string;
  showCreateButton?: boolean;
  maxDisplayLength?: number;
}

// ============================================================================
// 主组件
// ============================================================================

export const CaseSwitcher: React.FC<CaseSwitcherProps> = ({
  className = '',
  showCreateButton = true,
  maxDisplayLength = 30,
}) => {
  // Store状态
  const { currentCase, currentCaseId, isSwitching } = useCurrentCase();
  const { allCases, isLoading } = useCaseList();
  const fetchCases = useFetchCases();
  const { switchToCase, createCase } = useCaseOperations();
  const { error, clearError } = useCaseErrors();

  // 本地状态
  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newCaseName, setNewCaseName] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Refs
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // ========================================
  // 副作用
  // ========================================

  // 🔧 FIX: 初始化时获取案例列表，fetchCases现在是稳定的引用
  useEffect(() => {
    if (allCases.length === 0 && !isLoading) {
      // console.log('🚀 [CaseSwitcher] 初始化获取案例列表'); // 调试标记点 // [CLEANED]
      fetchCases();
    }
  }, [allCases.length, isLoading, fetchCases]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsCreating(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // ========================================
  // 事件处理
  // ========================================

  const handleCaseSelect = async (caseId: number) => {
    if (caseId === currentCaseId) {
      setIsOpen(false);
      return;
    }

    try {
      await switchToCase(caseId);
      setIsOpen(false);
      setSearchTerm('');
      // console.log(`✅ Phase 7C: 案例切换成功 ID:${caseId}`); // [CLEANED]
    } catch (error) {
      console.error('❌ Phase 7C: 案例切换失败:', error);
    }
  };

  const handleCreateCase = async () => {
    if (!newCaseName.trim()) return;

    try {
      const newCase = await createCase(newCaseName.trim());
      if (newCase) {
        setNewCaseName('');
        setIsCreating(false);
        await switchToCase(newCase.id);
        // console.log(`✅ Phase 7C: 新案例创建并切换成功: ${newCase.name}`); // [CLEANED]
      }
    } catch (error) {
      console.error('❌ Phase 7C: 创建案例失败:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (isCreating) {
        handleCreateCase();
      }
    } else if (e.key === 'Escape') {
      setIsOpen(false);
      setIsCreating(false);
      setSearchTerm('');
    }
  };

  // ========================================
  // 渲染辅助函数
  // ========================================

  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  const formatFileCount = (count: number) => {
    if (count === 0) return '无文件';
    if (count === 1) return '1个文件';
    return `${count}个文件`;
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return '';
    }
  };

  // 过滤案例
  const filteredCases = allCases.filter(caseItem =>
    caseItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (caseItem.description && caseItem.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // ========================================
  // 渲染
  // ========================================

  return (
    <div className={`case-switcher ${className}`} ref={dropdownRef}>
      {/* 当前案例显示按钮 */}
      <button
        className={`case-switcher__trigger ${isOpen ? 'case-switcher__trigger--open' : ''}`}
        onClick={() => setIsOpen(!isOpen)}
        disabled={isSwitching || isLoading}
        title={currentCase ? `当前案例: ${currentCase.name}` : '选择案例'}
      >
        <div className="case-switcher__current">
          {isSwitching ? (
            <div className="case-switcher__loading">
              <div className="case-switcher__spinner"></div>
              <span>切换中...</span>
            </div>
          ) : currentCase ? (
            <>
              <div className="case-switcher__icon">📁</div>
              <div className="case-switcher__info">
                <div className="case-switcher__name">
                  {truncateText(currentCase.name, maxDisplayLength)}
                </div>
                <div className="case-switcher__meta">
                  {formatFileCount(currentCase.file_count)}
                </div>
              </div>
            </>
          ) : (
            <div className="case-switcher__placeholder">
              <div className="case-switcher__icon">📂</div>
              <span>选择案例</span>
            </div>
          )}
        </div>
        <div className="case-switcher__arrow">
          {isOpen ? '▲' : '▼'}
        </div>
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="case-switcher__dropdown">
          {/* 搜索框 */}
          <div className="case-switcher__search">
            <input
              type="text"
              placeholder="搜索案例..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="case-switcher__search-input"
              onKeyDown={handleKeyDown}
            />
          </div>

          {/* 错误显示 */}
          {error && (
            <div className="case-switcher__error">
              <span className="case-switcher__error-icon">⚠️</span>
              <span className="case-switcher__error-text">{error}</span>
              <button
                className="case-switcher__error-close"
                onClick={clearError}
              >
                ×
              </button>
            </div>
          )}

          {/* 案例列表 */}
          <div className="case-switcher__list">
            {isLoading ? (
              <div className="case-switcher__loading-item">
                <div className="case-switcher__spinner"></div>
                <span>加载案例中...</span>
              </div>
            ) : filteredCases.length > 0 ? (
              filteredCases.map((caseItem) => (
                <button
                  key={caseItem.id}
                  className={`case-switcher__item ${
                    caseItem.id === currentCaseId ? 'case-switcher__item--active' : ''
                  }`}
                  onClick={() => handleCaseSelect(caseItem.id)}
                  disabled={isSwitching}
                >
                  <div className="case-switcher__item-icon">📁</div>
                  <div className="case-switcher__item-content">
                    <div className="case-switcher__item-name">
                      {caseItem.name}
                    </div>
                    <div className="case-switcher__item-meta">
                      <span>{formatFileCount(caseItem.file_count)}</span>
                      {caseItem.created_at && (
                        <span className="case-switcher__item-date">
                          {formatDate(caseItem.created_at)}
                        </span>
                      )}
                    </div>
                    {caseItem.description && (
                      <div className="case-switcher__item-desc">
                        {truncateText(caseItem.description, 50)}
                      </div>
                    )}
                  </div>
                  {caseItem.id === currentCaseId && (
                    <div className="case-switcher__item-check">✓</div>
                  )}
                </button>
              ))
            ) : (
              <div className="case-switcher__empty">
                <div className="case-switcher__empty-icon">📂</div>
                <div className="case-switcher__empty-text">
                  {searchTerm ? '未找到匹配的案例' : '暂无案例'}
                </div>
              </div>
            )}
          </div>

          {/* 创建新案例 */}
          {showCreateButton && (
            <div className="case-switcher__create">
              {isCreating ? (
                <div className="case-switcher__create-form">
                  <input
                    ref={inputRef}
                    type="text"
                    placeholder="输入案例名称..."
                    value={newCaseName}
                    onChange={(e) => setNewCaseName(e.target.value)}
                    className="case-switcher__create-input"
                    onKeyDown={handleKeyDown}
                    autoFocus
                  />
                  <div className="case-switcher__create-actions">
                    <button
                      className="case-switcher__create-confirm"
                      onClick={handleCreateCase}
                      disabled={!newCaseName.trim()}
                    >
                      ✓
                    </button>
                    <button
                      className="case-switcher__create-cancel"
                      onClick={() => {
                        setIsCreating(false);
                        setNewCaseName('');
                      }}
                    >
                      ×
                    </button>
                  </div>
                </div>
              ) : (
                <button
                  className="case-switcher__create-button"
                  onClick={() => setIsCreating(true)}
                >
                  <span className="case-switcher__create-icon">➕</span>
                  <span>新建案例</span>
                </button>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CaseSwitcher;