import * as React from 'react';
import { cn } from '@/utils/cn';

interface DividerProps {
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

/**
 * 简单的分割线组件，带有悬停效果
 */
export const Divider: React.FC<DividerProps> = ({ 
  orientation = 'vertical', 
  className 
}) => {
  const baseClasses = 'bg-mizzy-resizer-line hover:bg-mizzy-resizer-hover transition-colors duration-200';
  
  const orientationClasses = orientation === 'vertical' 
    ? 'w-[1px] h-full' 
    : 'h-[1px] w-full';

  return (
    <div 
      className={cn(baseClasses, orientationClasses, className)}
      role="separator"
      aria-orientation={orientation}
    />
  );
};