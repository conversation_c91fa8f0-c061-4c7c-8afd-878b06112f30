import { useState } from 'react';

function SearchIcon() {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 12A5 5 0 1 0 7 2a5 5 0 0 0 0 10zM13 13l-3-3"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function SearchBar() {
  const [searchValue, setSearchValue] = useState('');

  return (
    <div 
      className="search-bar-container"
      style={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: 'var(--color-surface-input)',
        border: '1px solid var(--color-border-primary)',
        borderRadius: 'var(--border-radius-sm)',
        padding: '4px 8px',
        gap: '6px',
        minWidth: '120px'
      }}
    >
      <SearchIcon />
      <input
        type="text"
        placeholder="搜索"
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        style={{
          background: 'transparent',
          border: 'none',
          outline: 'none',
          color: 'var(--color-text-body)',
          fontSize: 'var(--font-size-body)',
          fontFamily: 'var(--font-family-sans)',
          flex: 1
        }}
      />
    </div>
  );
}
