{"version": 3, "sources": ["../../zustand/esm/vanilla/shallow.mjs", "../../zustand/esm/react/shallow.mjs"], "sourcesContent": ["const isIterable = (obj) => Symbol.iterator in obj;\nconst hasIterableEntries = (value) => (\n  // HACK: avoid checking entries type\n  \"entries\" in value\n);\nconst compareEntries = (valueA, valueB) => {\n  const mapA = valueA instanceof Map ? valueA : new Map(valueA.entries());\n  const mapB = valueB instanceof Map ? valueB : new Map(valueB.entries());\n  if (mapA.size !== mapB.size) {\n    return false;\n  }\n  for (const [key, value] of mapA) {\n    if (!Object.is(value, mapB.get(key))) {\n      return false;\n    }\n  }\n  return true;\n};\nconst compareIterables = (valueA, valueB) => {\n  const iteratorA = valueA[Symbol.iterator]();\n  const iteratorB = valueB[Symbol.iterator]();\n  let nextA = iteratorA.next();\n  let nextB = iteratorB.next();\n  while (!nextA.done && !nextB.done) {\n    if (!Object.is(nextA.value, nextB.value)) {\n      return false;\n    }\n    nextA = iteratorA.next();\n    nextB = iteratorB.next();\n  }\n  return !!nextA.done && !!nextB.done;\n};\nfunction shallow(valueA, valueB) {\n  if (Object.is(valueA, valueB)) {\n    return true;\n  }\n  if (typeof valueA !== \"object\" || valueA === null || typeof valueB !== \"object\" || valueB === null) {\n    return false;\n  }\n  if (Object.getPrototypeOf(valueA) !== Object.getPrototypeOf(valueB)) {\n    return false;\n  }\n  if (isIterable(valueA) && isIterable(valueB)) {\n    if (hasIterableEntries(valueA) && hasIterableEntries(valueB)) {\n      return compareEntries(valueA, valueB);\n    }\n    return compareIterables(valueA, valueB);\n  }\n  return compareEntries(\n    { entries: () => Object.entries(valueA) },\n    { entries: () => Object.entries(valueB) }\n  );\n}\n\nexport { shallow };\n", "import React from 'react';\nimport { shallow } from 'zustand/vanilla/shallow';\n\nfunction useShallow(selector) {\n  const prev = React.useRef(void 0);\n  return (state) => {\n    const next = selector(state);\n    return shallow(prev.current, next) ? prev.current : prev.current = next;\n  };\n}\n\nexport { useShallow };\n"], "mappings": ";;;;;;;;AAAA,IAAM,aAAa,CAAC,QAAQ,OAAO,YAAY;AAC/C,IAAM,qBAAqB,CAAC;AAAA;AAAA,EAE1B,aAAa;AAAA;AAEf,IAAM,iBAAiB,CAAC,QAAQ,WAAW;AACzC,QAAM,OAAO,kBAAkB,MAAM,SAAS,IAAI,IAAI,OAAO,QAAQ,CAAC;AACtE,QAAM,OAAO,kBAAkB,MAAM,SAAS,IAAI,IAAI,OAAO,QAAQ,CAAC;AACtE,MAAI,KAAK,SAAS,KAAK,MAAM;AAC3B,WAAO;AAAA,EACT;AACA,aAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,QAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,mBAAmB,CAAC,QAAQ,WAAW;AAC3C,QAAM,YAAY,OAAO,OAAO,QAAQ,EAAE;AAC1C,QAAM,YAAY,OAAO,OAAO,QAAQ,EAAE;AAC1C,MAAI,QAAQ,UAAU,KAAK;AAC3B,MAAI,QAAQ,UAAU,KAAK;AAC3B,SAAO,CAAC,MAAM,QAAQ,CAAC,MAAM,MAAM;AACjC,QAAI,CAAC,OAAO,GAAG,MAAM,OAAO,MAAM,KAAK,GAAG;AACxC,aAAO;AAAA,IACT;AACA,YAAQ,UAAU,KAAK;AACvB,YAAQ,UAAU,KAAK;AAAA,EACzB;AACA,SAAO,CAAC,CAAC,MAAM,QAAQ,CAAC,CAAC,MAAM;AACjC;AACA,SAAS,QAAQ,QAAQ,QAAQ;AAC/B,MAAI,OAAO,GAAG,QAAQ,MAAM,GAAG;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,WAAW,YAAY,WAAW,MAAM;AAClG,WAAO;AAAA,EACT;AACA,MAAI,OAAO,eAAe,MAAM,MAAM,OAAO,eAAe,MAAM,GAAG;AACnE,WAAO;AAAA,EACT;AACA,MAAI,WAAW,MAAM,KAAK,WAAW,MAAM,GAAG;AAC5C,QAAI,mBAAmB,MAAM,KAAK,mBAAmB,MAAM,GAAG;AAC5D,aAAO,eAAe,QAAQ,MAAM;AAAA,IACtC;AACA,WAAO,iBAAiB,QAAQ,MAAM;AAAA,EACxC;AACA,SAAO;AAAA,IACL,EAAE,SAAS,MAAM,OAAO,QAAQ,MAAM,EAAE;AAAA,IACxC,EAAE,SAAS,MAAM,OAAO,QAAQ,MAAM,EAAE;AAAA,EAC1C;AACF;;;ACpDA,mBAAkB;AAGlB,SAAS,WAAW,UAAU;AAC5B,QAAM,OAAO,aAAAA,QAAM,OAAO,MAAM;AAChC,SAAO,CAAC,UAAU;AAChB,UAAM,OAAO,SAAS,KAAK;AAC3B,WAAO,QAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,KAAK,UAAU;AAAA,EACrE;AACF;", "names": ["React"]}