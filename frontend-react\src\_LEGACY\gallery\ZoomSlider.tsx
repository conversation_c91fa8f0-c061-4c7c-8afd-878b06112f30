interface ZoomSliderProps {
  zoomLevel: number;
  onZoomChange: (level: number) => void;
}

export function ZoomSlider({ zoomLevel, onZoomChange }: ZoomSliderProps) {

  return (
    <div 
      className="zoom-slider-container"
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}
    >
      <span
        style={{
          color: 'var(--color-text-title)',
          fontSize: 'var(--font-size-body)',
          cursor: 'pointer',
          userSelect: 'none'
        }}
        onClick={() => onZoomChange(Math.max(1, zoomLevel - 1))}
      >
        -
      </span>
      <input
        type="range"
        min="1"
        max="10"
        value={zoomLevel}
        onChange={(e) => onZoomChange(Number(e.target.value))}
        className="zoom-slider"
        style={{
          width: '80px',
          height: '4px',
          background: 'var(--color-surface-input)',
          borderRadius: '2px',
          outline: 'none',
          cursor: 'pointer'
        }}
      />
      <span
        style={{
          color: 'var(--color-text-title)',
          fontSize: 'var(--font-size-body)',
          cursor: 'pointer',
          userSelect: 'none'
        }}
        onClick={() => onZoomChange(Math.min(10, zoomLevel + 1))}
      >
        +
      </span>
    </div>
  );
}
