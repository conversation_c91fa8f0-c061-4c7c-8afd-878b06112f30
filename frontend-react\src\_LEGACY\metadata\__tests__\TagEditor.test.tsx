// TagEditor.test.tsx - Phase 7B: 标签编辑器组件测试

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import TagEditor from '../TagEditor';
import * as storeHooks from '@/store';

// Mock the store hooks
vi.mock('@/store', () => ({
  useCurrentFileTags: vi.fn(),
  useTagOperations: vi.fn(),
  useMetadataErrors: vi.fn(),
  useCurrentCase: vi.fn(),
  useRateLimitStatus: vi.fn(),
  useServiceHealth: vi.fn(),
  useAiErrors: vi.fn(),
}));

// Mock the API client
vi.mock('@/lib/apiClient', () => ({
  default: {
    registerFile: vi.fn(),
    addTagToFile: vi.fn(),
    removeTagFromFile: vi.fn(),
    getTagsForFile: vi.fn(),
  },
}));

// Mock AI client instance
vi.mock('@/lib/aiClientInstance', () => ({
  getAiClient: vi.fn(),
  isAiServiceAvailable: vi.fn().mockReturnValue(true),
  isThrottled: vi.fn().mockReturnValue(false),
}));

describe('TagEditor', () => {
  const mockUseCurrentFileTags = vi.mocked(storeHooks.useCurrentFileTags);
  const mockUseTagOperations = vi.mocked(storeHooks.useTagOperations);
  const mockUseMetadataErrors = vi.mocked(storeHooks.useMetadataErrors);
  const mockUseCurrentCase = vi.mocked(storeHooks.useCurrentCase);
  const mockUseRateLimitStatus = vi.mocked(storeHooks.useRateLimitStatus);
  const mockUseServiceHealth = vi.mocked(storeHooks.useServiceHealth);
  const mockUseAiErrors = vi.mocked(storeHooks.useAiErrors);

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    mockUseCurrentFileTags.mockReturnValue({
      tags: null,
      filePath: null,
      fileId: null,
      isLoading: false,
    });

    mockUseTagOperations.mockReturnValue({
      addTag: vi.fn(),
      removeTag: vi.fn(),
      addTagToMultiple: vi.fn(),
      isSaving: false,
    });

    mockUseMetadataErrors.mockReturnValue({
      error: null,
      lastError: null,
      clearError: vi.fn(),
      operationHistory: [],
    });

    // Phase 8A: AI相关mock
    mockUseCurrentCase.mockReturnValue({
      currentCase: { id: 1, name: '测试案例' } as any,
      currentCaseId: 1,
      isSwitching: false,
    });

    mockUseRateLimitStatus.mockReturnValue({
      suggestionRemaining: 95,
      queryRemaining: 180,
      isSuggestionThrottled: false,
      isQueryThrottled: false,
      resetTime: null,
      showWarning: false,
      getRemainingTime: vi.fn().mockReturnValue(0),
      dismissWarning: vi.fn(),
    });

    mockUseServiceHealth.mockReturnValue({
      health: 'healthy',
      availableServices: ['tag-suggestion', 'query-parsing'],
      lastCheck: Date.now(),
      showMessage: false,
      performHealthCheck: vi.fn(),
      dismissMessage: vi.fn(),
    });

    mockUseAiErrors.mockReturnValue({
      lastError: null,
      errorCount: 0,
      shouldRetry: vi.fn().mockReturnValue(false),
      clearError: vi.fn(),
    });
  });

  describe('Empty State', () => {
    it('应该显示空状态当没有选中文件时', () => {
      render(<TagEditor filePath={null} />);

      expect(screen.getByText('选择文件以查看和编辑标签')).toBeInTheDocument();
      expect(screen.getByText('🏷️')).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('应该显示加载状态', () => {
      mockUseCurrentFileTags.mockReturnValue({
        tags: null,
        filePath: '/test/file.jpg',
        fileId: null,
        isLoading: true,
      });

      render(<TagEditor filePath="/test/file.jpg" />);

      expect(screen.getByText('正在加载标签...')).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    it('应该显示错误状态', () => {
      mockUseMetadataErrors.mockReturnValue({
        error: '获取标签失败',
        lastError: null,
        clearError: vi.fn(),
        operationHistory: [],
      });

      render(<TagEditor filePath="/test/file.jpg" />);

      expect(screen.getByText('获取标签失败')).toBeInTheDocument();
      expect(screen.getByText('重试')).toBeInTheDocument();
    });
  });

  describe('Normal State', () => {
    beforeEach(() => {
      mockUseCurrentFileTags.mockReturnValue({
        tags: {
          user: ['重要', '待处理'],
          ai: ['人物', '风景'],
          cv: ['face_detected'],
          metadata: {
            camera: 'Canon EOS R5',
            iso: '400',
          },
        },
        filePath: '/test/file.jpg',
        fileId: 123,
        isLoading: false,
      });
    });

    it('应该显示文件标签', () => {
      render(<TagEditor filePath="/test/file.jpg" />);

      expect(screen.getByText('文件标签')).toBeInTheDocument();
      expect(screen.getByText('file.jpg')).toBeInTheDocument();

      // 用户标签
      expect(screen.getByText('重要')).toBeInTheDocument();
      expect(screen.getByText('待处理')).toBeInTheDocument();

      // AI标签
      expect(screen.getByText('人物')).toBeInTheDocument();
      expect(screen.getByText('风景')).toBeInTheDocument();

      // CV标签
      expect(screen.getByText('face_detected')).toBeInTheDocument();

      // 元数据
      expect(screen.getByText('camera: Canon EOS R5')).toBeInTheDocument();
      expect(screen.getByText('iso: 400')).toBeInTheDocument();
    });

    it('应该允许添加新标签', async () => {
      const mockAddTag = vi.fn().mockResolvedValue(true);
      mockUseTagOperations.mockReturnValue({
        addTag: mockAddTag,
        removeTag: vi.fn(),
        addTagToMultiple: vi.fn(),
        isSaving: false,
      });

      render(<TagEditor filePath="/test/file.jpg" />);

      const input = screen.getByPlaceholderText('输入新标签...');
      const addButton = screen.getByText('添加');

      fireEvent.change(input, { target: { value: '新标签' } });
      fireEvent.click(addButton);

      await waitFor(() => {
        expect(mockAddTag).toHaveBeenCalledWith('/test/file.jpg', '新标签');
      });
    });

    it('应该允许通过回车键添加标签', async () => {
      const mockAddTag = vi.fn().mockResolvedValue(true);
      mockUseTagOperations.mockReturnValue({
        addTag: mockAddTag,
        removeTag: vi.fn(),
        addTagToMultiple: vi.fn(),
        isSaving: false,
      });

      render(<TagEditor filePath="/test/file.jpg" />);

      const input = screen.getByPlaceholderText('输入新标签...');

      fireEvent.change(input, { target: { value: '回车标签' } });
      fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });

      await waitFor(() => {
        expect(mockAddTag).toHaveBeenCalledWith('/test/file.jpg', '回车标签');
      });
    });

    it('应该显示保存状态', () => {
      mockUseTagOperations.mockReturnValue({
        addTag: vi.fn(),
        removeTag: vi.fn(),
        addTagToMultiple: vi.fn(),
        isSaving: true,
      });

      render(<TagEditor filePath="/test/file.jpg" />);

      expect(screen.getByText('保存中...')).toBeInTheDocument();
    });
  });

  describe('Empty Tags State', () => {
    it('应该显示空标签状态', () => {
      mockUseCurrentFileTags.mockReturnValue({
        tags: {
          user: [],
          ai: [],
          cv: [],
          metadata: {},
        },
        filePath: '/test/file.jpg',
        fileId: 123,
        isLoading: false,
      });

      render(<TagEditor filePath="/test/file.jpg" />);

      expect(screen.getByText('此文件暂无标签')).toBeInTheDocument();
      expect(screen.getByText('在上方输入框中添加第一个标签')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('应该有正确的ARIA标签', () => {
      mockUseCurrentFileTags.mockReturnValue({
        tags: {
          user: ['测试标签'],
          ai: [],
          cv: [],
          metadata: {},
        },
        filePath: '/test/file.jpg',
        fileId: 123,
        isLoading: false,
      });

      render(<TagEditor filePath="/test/file.jpg" />);

      const input = screen.getByPlaceholderText('输入新标签...');
      expect(input).toHaveAttribute('type', 'text');

      const addButton = screen.getByText('添加');
      expect(addButton).toHaveAttribute('type', 'button');
    });
  });
});
