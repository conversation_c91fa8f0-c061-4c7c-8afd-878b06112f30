// useCaseStore.test.ts - Phase 7C: 案例状态管理测试

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useCaseStore, useCurrentCase, useCaseList, useCaseOperations, useCaseErrors } from '../useCaseStore';
import type { Case } from '../useCaseStore';

// Mock API client
vi.mock('@/lib/apiClient', () => ({
  default: {
    getCases: vi.fn(),
    getCase: vi.fn(),
  },
}));

import apiClient from '@/lib/apiClient';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
});

describe('useCaseStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
    // Reset store state
    useCaseStore.getState().resetCaseStore();
  });

  afterEach(() => {
    // Clean up localStorage mock
    localStorageMock.clear();
  });

  describe('Initial State', () => {
    it('应该有正确的初始状态', () => {
      const { result } = renderHook(() => useCaseStore());

      expect(result.current.allCases).toEqual([]);
      expect(result.current.currentCaseId).toBeNull();
      expect(result.current.currentCase).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isSwitching).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('fetchCases', () => {
    it('应该成功获取案例列表', async () => {
      const mockCases: Case[] = [
        {
          id: 1,
          name: '测试案例1',
          description: '测试描述1',
          created_at: '2024-07-28T10:00:00Z',
          status: 'ACTIVE',
          file_count: 10,
        },
        {
          id: 2,
          name: '测试案例2',
          created_at: '2024-07-28T11:00:00Z',
          status: 'ACTIVE',
          file_count: 5,
        },
      ];

      vi.mocked(apiClient.getCases).mockResolvedValue(mockCases);

      const { result } = renderHook(() => useCaseStore());

      await act(async () => {
        await result.current.fetchCases();
      });

      expect(result.current.allCases).toEqual(mockCases);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.caseStats.totalCases).toBe(2);
      expect(result.current.caseStats.activeCases).toBe(2);
    });

    it('应该处理获取案例失败', async () => {
      const mockError = {
        error: 'Network error',
        error_code: 'NETWORK_ERROR',
      };

      vi.mocked(apiClient.getCases).mockRejectedValue(mockError);

      const { result } = renderHook(() => useCaseStore());

      await act(async () => {
        await result.current.fetchCases();
      });

      expect(result.current.allCases).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe('Network error');
      expect(result.current.lastError).toEqual(mockError);
    });

    it('应该自动选择第一个案例', async () => {
      const mockCases: Case[] = [
        {
          id: 1,
          name: '测试案例1',
          created_at: '2024-07-28T10:00:00Z',
          status: 'ACTIVE',
          file_count: 10,
        },
      ];

      vi.mocked(apiClient.getCases).mockResolvedValue(mockCases);

      const { result } = renderHook(() => useCaseStore());

      await act(async () => {
        await result.current.fetchCases();
      });

      expect(result.current.currentCaseId).toBe(1);
      expect(result.current.currentCase).toEqual(mockCases[0]);
    });
  });

  describe('setCurrentCase', () => {
    it('应该成功设置当前案例', async () => {
      const mockCase: Case = {
        id: 1,
        name: '测试案例',
        created_at: '2024-07-28T10:00:00Z',
        status: 'ACTIVE',
        file_count: 10,
      };

      const { result } = renderHook(() => useCaseStore());

      // 先添加案例到列表
      act(() => {
        result.current._updateCurrentCase(mockCase);
        useCaseStore.setState({ allCases: [mockCase] });
      });

      await act(async () => {
        await result.current.setCurrentCase(1);
      });

      expect(result.current.currentCaseId).toBe(1);
      expect(result.current.currentCase).toEqual(mockCase);
      expect(result.current.caseStats.switchCount).toBe(1);
    });

    it('应该持久化当前案例ID到localStorage', async () => {
      const mockCase: Case = {
        id: 1,
        name: '测试案例',
        created_at: '2024-07-28T10:00:00Z',
        status: 'ACTIVE',
        file_count: 10,
      };

      const { result } = renderHook(() => useCaseStore());

      act(() => {
        useCaseStore.setState({ allCases: [mockCase] });
      });

      await act(async () => {
        await result.current.setCurrentCase(1);
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith('mizzy-star-current-case-id', '1');
    });

    it('应该处理不存在的案例ID', async () => {
      const { result } = renderHook(() => useCaseStore());

      await act(async () => {
        await result.current.setCurrentCase(999);
      });

      expect(result.current.currentCaseId).toBeNull();
      expect(result.current.error).toContain('案例 ID:999 不存在');
    });
  });

  describe('switchToCase', () => {
    it('应该成功切换案例', async () => {
      const mockCase: Case = {
        id: 1,
        name: '测试案例',
        created_at: '2024-07-28T10:00:00Z',
        status: 'ACTIVE',
        file_count: 10,
      };

      const { result } = renderHook(() => useCaseStore());

      act(() => {
        useCaseStore.setState({ allCases: [mockCase] });
      });

      await act(async () => {
        await result.current.switchToCase(1);
      });

      expect(result.current.currentCaseId).toBe(1);
      expect(result.current.isSwitching).toBe(false);
    });
  });

  describe('createCase', () => {
    it('应该创建新案例', async () => {
      const { result } = renderHook(() => useCaseStore());

      let newCase: Case | null = null;

      await act(async () => {
        newCase = await result.current.createCase('新案例', '新案例描述');
      });

      expect(newCase).not.toBeNull();
      expect(newCase?.name).toBe('新案例');
      expect(newCase?.description).toBe('新案例描述');
      expect(result.current.allCases).toHaveLength(1);
      expect(result.current.caseStats.totalCases).toBe(1);
    });
  });

  describe('Error Handling', () => {
    it('应该清除错误', () => {
      const { result } = renderHook(() => useCaseStore());

      act(() => {
        useCaseStore.setState({
          error: '测试错误',
          lastError: { error: '测试错误', error_code: 'TEST_ERROR' }
        });
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
      expect(result.current.lastError).toBeNull();
    });
  });

  describe('Store Reset', () => {
    it('应该重置store状态', () => {
      const { result } = renderHook(() => useCaseStore());

      // 设置一些状态
      act(() => {
        useCaseStore.setState({
          allCases: [{ id: 1, name: 'test' } as Case],
          currentCaseId: 1,
          error: '测试错误',
        });
      });

      act(() => {
        result.current.resetCaseStore();
      });

      expect(result.current.allCases).toEqual([]);
      expect(result.current.currentCaseId).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });
});

describe('Case Store Selectors', () => {
  beforeEach(() => {
    useCaseStore.getState().resetCaseStore();
  });

  describe('Store Exports', () => {
    it('应该导出所有必需的选择器', () => {
      // 验证选择器函数存在
      expect(typeof useCurrentCase).toBe('function');
      expect(typeof useCaseList).toBe('function');
      expect(typeof useCaseOperations).toBe('function');
      expect(typeof useCaseErrors).toBe('function');
    });
  });
});
