// useFileSystemStore.ts - Phase 6: 文件系统状态管理专用Store
// 管理目录状态、文件列表和文件系统操作

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import type { FileEntry } from '@/types/electron';

// ============================================================================
// 类型定义
// ============================================================================

interface FileSystemState {
  // 当前目录状态
  currentDirectory: string | null;
  
  // 文件列表状态
  files: FileEntry[];
  isLoading: boolean;
  error: string | null;
  
  // 过滤状态
  activeFilter: { type: 'fileExtension', value: string } | null;
  
  // 缓存状态
  directoryCache: Map<string, FileEntry[]>;
  lastRefreshTime: number;
}

interface FileSystemActions {
  // 目录操作
  setCurrentDirectory: (path: string | null) => void;
  refreshCurrentDirectory: () => Promise<void>;
  
  // 文件列表操作
  setFiles: (files: FileEntry[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 过滤操作
  setFilter: (filter: { type: string, value: string } | null) => void;
  clearFilter: () => void;
  getFilteredFiles: () => FileEntry[];
  
  // 缓存操作
  getCachedFiles: (directory: string) => FileEntry[] | null;
  setCachedFiles: (directory: string, files: FileEntry[]) => void;
  clearCache: () => void;
  
  // 文件操作
  removeFilesFromList: (filePaths: string[]) => void;
  updateFileInList: (file: FileEntry) => void;
  
  // 重置操作
  resetFileSystem: () => void;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: FileSystemState = {
  currentDirectory: null,
  files: [],
  isLoading: false,
  error: null,
  activeFilter: null,
  directoryCache: new Map(),
  lastRefreshTime: 0,
};

// ============================================================================
// Store实现
// ============================================================================

export const useFileSystemStore = create<FileSystemState & FileSystemActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // ========================================
      // 目录操作实现
      // ========================================
      setCurrentDirectory: (path) =>
        set(() => ({
          currentDirectory: path,
          error: null, // 清除之前的错误
        })),

      refreshCurrentDirectory: async () => {
        const state = get();
        if (!state.currentDirectory) return;

        set({ isLoading: true, error: null });

        try {
          // 检查缓存
          const cached = state.getCachedFiles(state.currentDirectory);
          if (cached && Date.now() - state.lastRefreshTime < 30000) { // 30秒缓存
            set({ files: cached, isLoading: false });
            return;
          }

          // 从文件系统加载
          const result = await window.electronAPI.getDirectoryContents(state.currentDirectory);
          
          if (result.error) {
            set({ error: result.error, isLoading: false });
            return;
          }

          const files = result.contents || [];
          set({ 
            files, 
            isLoading: false, 
            lastRefreshTime: Date.now() 
          });
          
          // 更新缓存
          state.setCachedFiles(state.currentDirectory, files);
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '未知错误', 
            isLoading: false 
          });
        }
      },

      // ========================================
      // 文件列表操作实现
      // ========================================
      setFiles: (files) => set({ files }),

      setLoading: (loading) => set({ isLoading: loading }),

      setError: (error) => set({ error }),

      // ========================================
      // 过滤操作实现
      // ========================================
      setFilter: (filter) => set({ activeFilter: filter }),

      clearFilter: () => set({ activeFilter: null }),

      getFilteredFiles: () => {
        const state = get();
        if (!state.activeFilter) return state.files;

        return state.files.filter(file => {
          if (state.activeFilter?.type === 'fileExtension') {
            const extension = file.name.split('.').pop()?.toUpperCase() || 'NO_EXT';
            return extension === state.activeFilter.value;
          }
          return true;
        });
      },

      // ========================================
      // 缓存操作实现
      // ========================================
      getCachedFiles: (directory) => {
        const state = get();
        return state.directoryCache.get(directory) || null;
      },

      setCachedFiles: (directory, files) =>
        set((state) => {
          const newCache = new Map(state.directoryCache);
          newCache.set(directory, files);
          return { directoryCache: newCache };
        }),

      clearCache: () =>
        set({ directoryCache: new Map() }),

      // ========================================
      // 文件操作实现
      // ========================================
      removeFilesFromList: (filePaths) =>
        set((state) => ({
          files: state.files.filter(file => !filePaths.includes(file.path))
        })),

      updateFileInList: (updatedFile) =>
        set((state) => ({
          files: state.files.map(file => 
            file.path === updatedFile.path ? updatedFile : file
          )
        })),

      // ========================================
      // 重置操作实现
      // ========================================
      resetFileSystem: () => set(initialState),
    }),
    {
      name: 'mizzy-star-filesystem-store',
    }
  )
);

// ============================================================================
// 选择器Hooks（性能优化）
// ============================================================================

export const useCurrentDirectory = () => useFileSystemStore((state) => ({
  currentDirectory: state.currentDirectory,
  setCurrentDirectory: state.setCurrentDirectory,
  refreshCurrentDirectory: state.refreshCurrentDirectory,
}), shallow);

export const useFileList = () => useFileSystemStore((state) => ({
  files: state.files,
  isLoading: state.isLoading,
  error: state.error,
  setFiles: state.setFiles,
  setLoading: state.setLoading,
  setError: state.setError,
}), shallow);

export const useFileFilter = () => useFileSystemStore((state) => ({
  activeFilter: state.activeFilter,
  setFilter: state.setFilter,
  clearFilter: state.clearFilter,
  getFilteredFiles: state.getFilteredFiles,
}), shallow);

// 新增：缓存的过滤文件选择器，避免每次调用都返回新数组
export const useFilteredFiles = () => useFileSystemStore((state) => {
  if (!state.activeFilter) return state.files;

  return state.files.filter(file => {
    if (state.activeFilter?.type === 'fileExtension') {
      const extension = file.name.split('.').pop()?.toUpperCase() || 'NO_EXT';
      return extension === state.activeFilter.value;
    }
    return true;
  });
}, shallow);

export const useFileOperations = () => useFileSystemStore((state) => ({
  removeFilesFromList: state.removeFilesFromList,
  updateFileInList: state.updateFileInList,
}), shallow);

export default useFileSystemStore;
