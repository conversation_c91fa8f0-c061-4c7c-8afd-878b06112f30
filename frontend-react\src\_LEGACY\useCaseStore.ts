// useCaseStore.ts - Phase 7C: 案例状态管理
// 管理工作案例的切换、数据隔离和工作流集成

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import apiClient, { type ApiError } from '@/lib/apiClient';

// ============================================================================
// 类型定义
// ============================================================================

// ✅ 修复：与后端schemas.Case精确匹配的接口定义
export interface Case {
  id: number;
  case_name: string;                    // ✅ 修复：使用后端字段名
  description?: string;
  created_at: string;
  status: 'active' | 'deleted' | 'permanently_deleted';  // ✅ 修复：使用后端枚举值
  deleted_at?: string | null;           // ✅ 新增：后端字段
  deleted_by?: number | null;           // ✅ 新增：后端字段
  files?: any[];                        // ✅ 修复：后端返回files数组而非file_count

  // 封面相关字段 - 与后端完全匹配
  cover_image_url?: string | null;
  cover_type?: 'manual' | 'automatic' | 'placeholder';
  cover_source_file_id?: number | null;
  cover_needs_attention?: boolean;
  cover_updated_at?: string | null;

  // 计算字段（前端计算）
  file_count?: number;                  // ✅ 从files.length计算得出
}

export interface CaseState {
  // 案例数据
  allCases: Case[];
  currentCaseId: number | null;
  currentCase: Case | null;
  
  // 加载状态
  isLoading: boolean;
  isSwitching: boolean;
  isCreating: boolean;
  
  // 错误状态
  error: string | null;
  lastError: ApiError | null;
  
  // 统计信息
  caseStats: {
    totalCases: number;
    activeCases: number;
    lastSwitchTime: number;
    switchCount: number;
  };
}

export interface CaseActions {
  // 案例获取和管理
  fetchCases: () => Promise<void>;
  refreshCurrentCase: () => Promise<void>;
  createCase: (name: string, description?: string) => Promise<Case | null>;
  updateCase: (caseId: number, updates: { case_name?: string; description?: string }) => Promise<Case>;  // ✅ 新增
  deleteCase: (caseId: number) => Promise<void>;

  // 案例切换
  setCurrentCase: (caseId: number) => Promise<void>;
  switchToCase: (caseId: number) => Promise<void>;

  // 状态管理
  clearError: () => void;
  resetCaseStore: () => void;

  // 内部方法
  _updateCurrentCase: (caseData: Case) => void;
  _setCaseSwitching: (switching: boolean) => void;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: CaseState = {
  allCases: [],
  currentCaseId: null,
  currentCase: null,
  isLoading: false,
  isSwitching: false,
  isCreating: false,
  error: null,
  lastError: null,
  caseStats: {
    totalCases: 0,
    activeCases: 0,
    lastSwitchTime: 0,
    switchCount: 0,
  },
};

// ============================================================================
// 常量
// ============================================================================

const CURRENT_CASE_STORAGE_KEY = 'mizzy-star-current-case-id';

// ============================================================================
// Store实现
// ============================================================================

export const useCaseStore = create<CaseState & CaseActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // ========================================
        // 案例获取和管理实现
        // ========================================
        
        fetchCases: async () => {
          const currentState = get();

          // 🔧 FIX: 防止重复调用fetchCases
          if (currentState.isLoading) {
            // console.log('⚠️ [fetchCases] 已在加载中，跳过重复调用'); // [CLEANED]
            return;
          }

          set({ isLoading: true, error: null });

          try {
            // console.log('🏢 [fetchCases] 开始获取案例列表...'); // [CLEANED]
            const cases = await apiClient.getCases();

            const activeCases = cases.filter((c: Case) => c.status === 'ACTIVE');

            set((state) => ({
              allCases: cases,
              isLoading: false,
              caseStats: {
                ...state.caseStats,
                totalCases: cases.length,
                activeCases: activeCases.length,
              },
            }));

            // console.log(`✅ [fetchCases] 获取到 ${cases.length} 个案例`); // [CLEANED]

            // 🔧 FIX: 优化自动选择逻辑，避免无限循环
            const updatedState = get();
            const shouldAutoSelect = !updatedState.currentCaseId &&
                                   activeCases.length > 0 &&
                                   !updatedState.isSwitching;

            if (shouldAutoSelect) {
              // console.log('🎯 [fetchCases] 自动选择第一个案例:', activeCases[0].name); // [CLEANED]
              // 使用setTimeout避免同步调用导致的状态更新冲突
              setTimeout(async () => {
                await get().setCurrentCase(activeCases[0].id);
              }, 0);
            }

          } catch (error) {
            const apiError = error as ApiError;
            console.error('❌ Phase 7C: 获取案例列表失败:', error);
            
            set({
              isLoading: false,
              error: apiError.error || '获取案例列表失败',
              lastError: apiError,
            });
          }
        },

        refreshCurrentCase: async () => {
          const { currentCaseId } = get();
          if (!currentCaseId) return;
          
          try {
            // console.log(`🔄 Phase 7C: 刷新当前案例 ID:${currentCaseId}`); // [CLEANED]
            const caseData = await apiClient.getCase(currentCaseId);
            
            set((state) => ({
              currentCase: caseData,
              allCases: state.allCases.map(c => 
                c.id === currentCaseId ? caseData : c
              ),
            }));
            
            // console.log('✅ Phase 7C: 当前案例刷新成功'); // [CLEANED]
            
          } catch (error) {
            const apiError = error as ApiError;
            console.error('❌ Phase 7C: 刷新当前案例失败:', error);
            
            set({
              error: apiError.error || '刷新案例失败',
              lastError: apiError,
            });
          }
        },

        createCase: async (name: string, description?: string) => {
          set({ isCreating: true, error: null });

          try {
            console.log(`🆕 创建新案例: ${name}`);

            // ✅ 使用真实的API调用，获取后端返回的真实ID
            const newCase = await apiClient.createCase(name, description);

            // ✅ 修复：计算file_count字段
            const processedCase = {
              ...newCase,
              file_count: newCase.files?.length || 0
            };

            // ✅ 修复：创建成功后自动刷新案例列表（功能点4）
            const { fetchCases } = get();
            await fetchCases();

            console.log(`✅ 案例创建成功，真实ID: ${processedCase.id}`);
            return processedCase;
            
          } catch (error) {
            const apiError = error as ApiError;
            console.error('❌ Phase 7C: 创建案例失败:', error);
            
            set({
              isCreating: false,
              error: apiError.error || '创建案例失败',
              lastError: apiError,
            });
            
            return null;
          }
        },

        // ✅ 新增：更新案例信息功能（功能点5）
        updateCase: async (caseId: number, updates: { case_name?: string; description?: string }) => {
          set({ isLoading: true, error: null });

          try {
            console.log(`📝 更新案例 ID:${caseId}`, updates);
            const updatedCase = await apiClient.updateCase(caseId, updates);

            // 更新本地状态中的案例信息
            set((state) => ({
              allCases: state.allCases.map(c =>
                c.id === caseId ? { ...updatedCase, file_count: updatedCase.files?.length || 0 } : c
              ),
              currentCase: state.currentCaseId === caseId
                ? { ...updatedCase, file_count: updatedCase.files?.length || 0 }
                : state.currentCase,
              isLoading: false,
            }));

            console.log(`✅ 案例更新成功 ID:${caseId}`);
            return updatedCase;

          } catch (error) {
            const apiError = error as ApiError;
            console.error('❌ 更新案例失败:', error);

            set({
              isLoading: false,
              error: apiError.error || '更新案例失败',
              lastError: apiError,
            });

            throw error;
          }
        },

        deleteCase: async (caseId: number) => {
          set({ isLoading: true, error: null });

          try {
            console.log(`🗑️ Phase 7C: 删除案例 ID:${caseId}`);
            await apiClient.deleteCase(caseId);

            const { currentCaseId, allCases } = get();

            // 从列表中移除已删除的案例
            const updatedCases = allCases.filter(c => c.id !== caseId);

            // 如果删除的是当前案例，需要切换到其他案例或清空当前案例
            let newCurrentCaseId = currentCaseId;
            let newCurrentCase = null;

            if (currentCaseId === caseId) {
              // 选择第一个可用的案例作为新的当前案例
              const firstAvailableCase = updatedCases.find(c => c.status === 'ACTIVE');
              if (firstAvailableCase) {
                newCurrentCaseId = firstAvailableCase.id;
                newCurrentCase = firstAvailableCase;
                localStorage.setItem(CURRENT_CASE_STORAGE_KEY, firstAvailableCase.id.toString());
              } else {
                newCurrentCaseId = null;
                newCurrentCase = null;
                localStorage.removeItem(CURRENT_CASE_STORAGE_KEY);
              }
            }

            set((state) => ({
              allCases: updatedCases,
              currentCaseId: newCurrentCaseId,
              currentCase: newCurrentCase,
              isLoading: false,
              caseStats: {
                ...state.caseStats,
                totalCases: updatedCases.length,
                activeCases: updatedCases.filter(c => c.status === 'ACTIVE').length,
              },
            }));

            console.log(`✅ Phase 7C: 案例删除成功 ID:${caseId}`);

          } catch (error) {
            const apiError = error as ApiError;
            console.error('❌ Phase 7C: 删除案例失败:', error);

            set({
              isLoading: false,
              error: apiError.error || '删除案例失败',
              lastError: apiError,
            });

            throw error; // 重新抛出错误，让调用者处理
          }
        },

        // ========================================
        // 案例切换实现
        // ========================================
        
        setCurrentCase: async (caseId: number) => {
          const currentState = get();

          // 🔧 FIX: 防止重复设置相同案例
          if (currentState.currentCaseId === caseId) {
            // console.log(`⚠️ [setCurrentCase] 案例 ${caseId} 已是当前案例，跳过设置`); // [CLEANED]
            return;
          }

          const { allCases } = currentState;
          const targetCase = allCases.find(c => c.id === caseId);

          if (!targetCase) {
            console.error(`❌ [setCurrentCase] 案例 ID:${caseId} 不存在`);
            set({ error: `案例 ID:${caseId} 不存在` });
            return;
          }

          // console.log(`🔄 [setCurrentCase] 设置当前案例: ${targetCase.name} (ID: ${caseId})`); // [CLEANED]

          set((state) => ({
            currentCaseId: caseId,
            currentCase: targetCase,
            caseStats: {
              ...state.caseStats,
              lastSwitchTime: Date.now(),
              switchCount: state.caseStats.switchCount + 1,
            },
          }));

          // 持久化到localStorage
          try {
            localStorage.setItem(CURRENT_CASE_STORAGE_KEY, caseId.toString());
          } catch (error) {
            console.warn('⚠️ [setCurrentCase] 保存当前案例到localStorage失败:', error);
          }

          // console.log(`✅ [setCurrentCase] 当前案例已设置为: ${targetCase.name} (ID: ${caseId})`); // [CLEANED]
        },

        switchToCase: async (caseId: number) => {
          set({ isSwitching: true, error: null });
          
          try {
            await get().setCurrentCase(caseId);
            
            // 触发相关系统的更新
            // 这些将在Step 4中实现
            // console.log('🔄 Phase 7C: 触发案例切换相关更新...'); // [CLEANED]
            
            set({ isSwitching: false });
            
          } catch (error) {
            const apiError = error as ApiError;
            console.error('❌ Phase 7C: 案例切换失败:', error);
            
            set({
              isSwitching: false,
              error: apiError.error || '案例切换失败',
              lastError: apiError,
            });
          }
        },

        // ========================================
        // 状态管理实现
        // ========================================
        
        clearError: () => {
          set({ error: null, lastError: null });
        },

        resetCaseStore: () => {
          set(initialState);
          try {
            localStorage.removeItem(CURRENT_CASE_STORAGE_KEY);
          } catch (error) {
            console.warn('⚠️ Phase 7C: 清除localStorage失败:', error);
          }
        },

        // ========================================
        // 内部方法实现
        // ========================================
        
        _updateCurrentCase: (caseData: Case) => {
          set((state) => ({
            currentCase: caseData,
            allCases: state.allCases.map(c => 
              c.id === caseData.id ? caseData : c
            ),
          }));
        },

        _setCaseSwitching: (switching: boolean) => {
          set({ isSwitching: switching });
        },
      }),
      {
        name: 'case-store',
        // 只持久化当前案例ID
        partialize: (state) => ({
          currentCaseId: state.currentCaseId,
          caseStats: state.caseStats,
        }),
      }
    ),
    { name: 'CaseStore' }
  )
);

// ============================================================================
// 选择器钩子
// ============================================================================

export const useCurrentCase = () => {
  return useCaseStore((state) => ({
    currentCase: state.currentCase,
    currentCaseId: state.currentCaseId,
    isSwitching: state.isSwitching,
  }), shallow);
};

export const useCaseList = () => {
  return useCaseStore((state) => ({
    allCases: state.allCases,
    isLoading: state.isLoading,
  }), shallow);
};

// 单独导出fetchCases函数，避免在选择器中包含函数引用
export const useFetchCases = () => useCaseStore(state => state.fetchCases);

// 分离函数引用，避免无限循环
export const useSetCurrentCase = () => useCaseStore(state => state.setCurrentCase);
export const useSwitchToCase = () => useCaseStore(state => state.switchToCase);
export const useCreateCase = () => useCaseStore(state => state.createCase);
export const useDeleteCase = () => useCaseStore(state => state.deleteCase);
export const useRefreshCurrentCase = () => useCaseStore(state => state.refreshCurrentCase);

// 保持向后兼容的组合选择器（仅用于不会导致循环的场景）
export const useCaseOperations = () => {
  return useCaseStore((state) => ({
    setCurrentCase: state.setCurrentCase,
    switchToCase: state.switchToCase,
    createCase: state.createCase,
    refreshCurrentCase: state.refreshCurrentCase,
  }), shallow);
};

export const useCaseErrors = () => {
  return useCaseStore((state) => ({
    error: state.error,
    lastError: state.lastError,
    clearError: state.clearError,
  }), shallow);
};
