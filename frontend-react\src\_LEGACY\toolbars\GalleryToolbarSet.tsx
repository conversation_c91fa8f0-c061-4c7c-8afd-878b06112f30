// src/components/toolbars/GalleryToolbarSet.tsx
import React, { useState } from 'react';
// 根据蓝图导入所有需要的图标
import { ButtonAddIcon } from '../../assets/icons/ButtonAddIcon';
import { RefreshAltIcon } from '../../assets/icons/RefreshAltIcon';
import { MinusIcon } from '../../assets/icons/MinusIcon';
import { PlusAddIcon } from '../../assets/icons/PlusAddIcon';
import { ViewGridIcon } from '../../assets/icons/ViewGridIcon';
// 还需要导入音频波形图标和向上/向下箭头
// import { AudioWaveIcon } from '../../assets/icons/AudioWaveIcon';
// import { ChevronUpIcon } from '../../assets/icons/ChevronUpIcon';
// import { ChevronDownIcon } from '../../assets/icons/ChevronDownIcon';

import { IconButton } from '../ui/IconButton';
import { TextButton } from '../ui/TextButton';
import { SearchInput } from '../ui/SearchInput';
import { ZoomSlider } from '../ui/ZoomSlider';
import { useLayout } from '../../context/LayoutContext';

// 创建一个简单的 Toolbar 组件来统一布局
function Toolbar({ children, justify = 'justify-between' }: { children: React.ReactNode, justify?: string }) {
    // 严格按照蓝图：高度16px，间距4px
    return (
        <div className={`w-full h-[16px] flex items-center ${justify} mb-[4px]`}>
            {children}
        </div>
    );
}

export function GalleryToolbarSet() {
    const [zoomLevel, setZoomLevel] = useState(4);
    const [searchQuery, setSearchQuery] = useState('');
    const { isWorkbenchVisible, toggleWorkbench, openUploadModal } = useLayout();

    return (
        <div className="flex flex-col h-full">
            {/* 画廊工具栏第一行 */}
            <Toolbar>
                {/* 左对齐：当前显示库标题 + 上传按钮 */}
                <div className="flex items-center gap-[4px]">
                    <TextButton>当前显示库标题</TextButton>
                    <IconButton icon={ButtonAddIcon} tooltip="上传" onClick={openUploadModal} />
                </div>
                {/* 右对齐：刷新按钮 */}
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={RefreshAltIcon} tooltip="刷新" />
                </div>
            </Toolbar>

            {/* 画廊工具栏第二行 - 缩放控制居中 */}
            <Toolbar justify="justify-center">
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={MinusIcon} tooltip="缩小" onClick={() => setZoomLevel(Math.max(1, zoomLevel - 1))} />
                    <ZoomSlider
                        value={zoomLevel}
                        min={1}
                        max={8}
                        onChange={setZoomLevel}
                    />
                    <IconButton icon={PlusAddIcon} tooltip="放大" onClick={() => setZoomLevel(Math.min(8, zoomLevel + 1))} />
                </div>
            </Toolbar>

            {/* 画廊工具栏第三行 - 视图和搜索控制 */}
            <Toolbar justify="justify-end">
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={ViewGridIcon} tooltip="视图" />
                    {/* <IconButton icon={AudioWaveIcon} tooltip="排序" /> */}
                    <SearchInput placeholder="图片搜索" value={searchQuery} onChange={setSearchQuery} />
                </div>
            </Toolbar>

            {/* 画廊内容区域 */}
            <div className="flex-1 bg-mizzy-bg-main">
                {/* 画廊内容将在这里 */}
            </div>

            {/* 画廊底部工具栏 - 工作台展开/折叠按钮 */}
            <div className="flex justify-center">
                <Toolbar justify="justify-center">
                    <IconButton
                        icon={isWorkbenchVisible ? MinusIcon : PlusAddIcon}
                        tooltip={isWorkbenchVisible ? "折叠工作台" : "展开工作台"}
                        onClick={() => {
                            console.log('工作台按钮被点击，当前状态:', { isWorkbenchVisible });
                            toggleWorkbench();
                        }}
                        className={isWorkbenchVisible ? "border-mizzy-border-highlight" : ""}
                    />
                </Toolbar>
            </div>
        </div>
    );
}
