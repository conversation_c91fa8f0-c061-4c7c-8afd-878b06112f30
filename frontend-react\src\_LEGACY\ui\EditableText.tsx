import React, { useState, useRef, useEffect } from 'react';

interface EditableTextProps {
  value: string;
  onSave: (newValue: string) => Promise<void>;
  placeholder?: string;
  multiline?: boolean;
  maxLength?: number;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export function EditableText({
  value,
  onSave,
  placeholder = '',
  multiline = false,
  maxLength,
  disabled = false,
  className = '',
  style = {}
}: EditableTextProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      if (inputRef.current instanceof HTMLInputElement) {
        inputRef.current.select();
      } else if (inputRef.current instanceof HTMLTextAreaElement) {
        inputRef.current.setSelectionRange(0, inputRef.current.value.length);
      }
    }
  }, [isEditing]);

  const handleClick = () => {
    if (!disabled && !isEditing) {
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (editValue.trim() === value.trim()) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    try {
      await onSave(editValue.trim());
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to save:', error);
      // 恢复原值
      setEditValue(value);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !multiline) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    } else if (e.key === 'Enter' && multiline && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  const handleBlur = () => {
    if (!isSaving) {
      handleSave();
    }
  };

  const baseStyle: React.CSSProperties = {
    background: 'transparent',
    border: 'none',
    outline: 'none',
    color: 'inherit',
    font: 'inherit',
    width: '100%',
    cursor: disabled ? 'default' : (isEditing ? 'text' : 'pointer'),
    ...style
  };

  if (isEditing) {
    const Component = multiline ? 'textarea' : 'input';
    return (
      <Component
        ref={inputRef as any}
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        placeholder={placeholder}
        maxLength={maxLength}
        disabled={isSaving}
        className={className}
        style={{
          ...baseStyle,
          border: '1px solid var(--color-border-focused)',
          borderRadius: '2px',
          padding: '2px 4px',
          backgroundColor: 'var(--color-surface-input)',
          ...(multiline && { resize: 'none', minHeight: '60px' })
        }}
      />
    );
  }

  return (
    <div
      onClick={handleClick}
      className={className}
      style={{
        ...baseStyle,
        padding: '2px 4px',
        borderRadius: '2px',
        minHeight: multiline ? '60px' : 'auto',
        display: 'flex',
        alignItems: multiline ? 'flex-start' : 'center',
        ...(disabled ? {} : {
          ':hover': {
            backgroundColor: 'var(--color-surface-button)'
          }
        })
      }}
      title={disabled ? '' : '点击编辑'}
    >
      {value || <span style={{ color: '#9F9FA2', fontStyle: 'italic' }}>{placeholder}</span>}
    </div>
  );
}
