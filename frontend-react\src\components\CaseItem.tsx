// 🔥 BEDROCK: 案例项组件 - 骨架核心组件
// 职责：渲染单个案例的基本信息，纯展示组件（无交互）

import React from 'react';
import type { Case } from '@/store/useCaseStore';

interface CaseItemProps {
  case: Case;
}

export function CaseItem({ case: caseItem }: CaseItemProps) {
  console.log(`%c🔥 BEDROCK: Rendering CaseItem (案例项)`, 'color: #FF6B35; font-weight: bold;', caseItem.case_name);

  // 🔥 BEDROCK: 格式化创建时间
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('zh-CN');
    } catch {
      return '未知时间';
    }
  };

  // 🔥 BEDROCK: 计算文件数量
  const fileCount = caseItem.files?.length || caseItem.file_count || 0;

  return (
    <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
      {/* 🔥 BEDROCK: 案例标题 */}
      <h3 className="text-lg font-semibold text-gray-800 mb-2">
        {caseItem.case_name || '未命名案例'}
      </h3>

      {/* 🔥 BEDROCK: 案例描述 */}
      {caseItem.description && (
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {caseItem.description}
        </p>
      )}

      {/* 🔥 BEDROCK: 案例元信息 */}
      <div className="flex justify-between items-center text-xs text-gray-500">
        <span>文件: {fileCount} 个</span>
        <span>创建: {formatDate(caseItem.created_at)}</span>
      </div>

      {/* 🔥 BEDROCK: 状态指示器 */}
      <div className="mt-2">
        <span className={`inline-block px-2 py-1 rounded-full text-xs ${
          caseItem.status === 'active' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-gray-100 text-gray-800'
        }`}>
          {caseItem.status === 'active' ? '活跃' : '非活跃'}
        </span>
      </div>

      {/* 🔥 BEDROCK: 注释掉所有交互元素 - 纯展示组件 */}
      {/* 
      已移除的交互功能:
      - 点击事件处理
      - 编辑按钮
      - 删除按钮
      - 查看详情按钮
      - 切换案例功能
      */}
    </div>
  );
}
