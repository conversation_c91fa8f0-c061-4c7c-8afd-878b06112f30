// NavigationSidebarWrapper - Feature flag controlled component wrapper
// 功能开关控制的导航侧边栏包装器

import React from 'react';
import { useFeatureFlag } from '@/utils/hooks/useFeatureFlags';
import { NavigationSidebar } from './NavigationSidebar';
import { CatalogPanel } from '@/components/panels/CatalogPanel';
import { useUIStore } from '@/store';
import { shallow } from 'zustand/shallow';
// Pre-Phase 5: 移除useApi依赖，使用文件系统状态
// import { useCases } from '@/hooks/useApi';

// ============================================================================
// 接口定义
// ============================================================================

interface NavigationSidebarWrapperProps {
  onToggleSidebars?: () => void;
  onToggleWorkspace?: () => void;
  onSwapGalleryWorkspace?: () => void;
  className?: string;
}

// ============================================================================
// NavigationSidebarWrapper 组件
// ============================================================================

export const NavigationSidebarWrapper: React.FC<NavigationSidebarWrapperProps> = (props) => {
  // 检查功能开关
  const useNewNavigationSidebar = useFeatureFlag('useNewNavigationSidebar');

  // 获取状态和数据 - 使用专用选择器避免无限循环
  const { selectedCaseId, searchQuery, setSearchQuery } = useUIStore((state) => ({
    selectedCaseId: state.selectedCaseId,
    searchQuery: state.searchQuery,
    setSearchQuery: state.setSearchQuery,
  }), shallow);
  const { data: cases } = useCases();

  // 如果启用新导航栏，使用新组件
  if (useNewNavigationSidebar) {
    return <NavigationSidebar {...props} />;
  }

  // 否则使用原有的CatalogPanel
  const currentLibraryName = cases?.find(c => c.id === selectedCaseId)?.case_name || "加载中...";

  return (
    <CatalogPanel
      currentLibraryName={currentLibraryName}
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      tagCategories={[]} // TODO: 将标签数据转换为分类格式
    />
  );
};

// ============================================================================
// 导出
// ============================================================================

export default NavigationSidebarWrapper;
