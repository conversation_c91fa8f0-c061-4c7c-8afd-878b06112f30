/* Workbench.css - Phase 5C: 工作台样式 */

.workbench {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #191012; /* 侧边栏背景 */
  color: #A49F9A; /* 文本颜色 */
}

.workbench-header {
  padding: 16px;
  border-bottom: 1px solid #333;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workbench-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #A49F9A;
}

.selection-info {
  font-size: 12px;
  color: #A49F9A;
  opacity: 0.8;
}

.selection-count {
  background-color: rgba(164, 159, 154, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.workbench-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* ============================================================================
 * 空状态样式
 * ============================================================================ */

.workbench-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 32px 16px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.workbench-empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #A49F9A;
}

.workbench-empty-state p {
  margin: 0;
  font-size: 14px;
  color: #A49F9A;
  opacity: 0.7;
  line-height: 1.5;
}

/* ============================================================================
 * 活跃状态样式
 * ============================================================================ */

.workbench-actions {
  margin-bottom: 24px;
}

.action-group {
  margin-bottom: 20px;
}

.action-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #A49F9A;
  border-bottom: 1px solid #333;
  padding-bottom: 8px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 10px 12px;
  border: 1px solid #333;
  background-color: rgba(164, 159, 154, 0.1);
  color: #A49F9A;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn:hover:not(:disabled) {
  background-color: rgba(164, 159, 154, 0.2);
  border-color: rgba(164, 159, 154, 0.3);
}

.action-btn:active:not(:disabled) {
  transform: translateY(1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-btn:hover:not(:disabled) {
  background-color: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

.clear-btn:hover:not(:disabled) {
  background-color: rgba(164, 159, 154, 0.2);
  border-color: #A49F9A;
}

/* ============================================================================
 * 选中文件预览
 * ============================================================================ */

.selected-files-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #A49F9A;
  border-bottom: 1px solid #333;
  padding-bottom: 8px;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background-color: rgba(164, 159, 154, 0.05);
  border-radius: 4px;
  font-size: 12px;
}

.file-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #A49F9A;
}

.more-files {
  font-style: italic;
  opacity: 0.7;
}

/* ============================================================================
 * 滚动条样式
 * ============================================================================ */

.workbench-content::-webkit-scrollbar,
.file-list::-webkit-scrollbar {
  width: 6px;
}

.workbench-content::-webkit-scrollbar-track,
.file-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.workbench-content::-webkit-scrollbar-thumb,
.file-list::-webkit-scrollbar-thumb {
  background: rgba(164, 159, 154, 0.3);
  border-radius: 3px;
}

.workbench-content::-webkit-scrollbar-thumb:hover,
.file-list::-webkit-scrollbar-thumb:hover {
  background: rgba(164, 159, 154, 0.5);
}
