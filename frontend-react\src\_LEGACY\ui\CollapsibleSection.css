/* CollapsibleSection.css - Phase 5B: 可折叠区域样式 */

.collapsible-section {
  margin-bottom: 16px;
}

.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid #333;
  transition: all 0.2s ease;
}

.collapsible-header:hover {
  background-color: rgba(164, 159, 154, 0.1);
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 4px;
}

.collapsible-title {
  font-size: 14px;
  font-weight: 600;
  color: #A49F9A;
}

.collapsible-arrow {
  font-size: 12px;
  color: #A49F9A;
  transition: transform 0.2s ease;
  transform-origin: center;
}

.collapsible-arrow.expanded {
  transform: rotate(0deg);
}

.collapsible-arrow.collapsed {
  transform: rotate(-90deg);
}

.collapsible-content {
  padding-top: 12px;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
