import React, { useState } from 'react';
import { StatusDisplay } from '@/components/feedback/StatusDisplay';
import { Button } from '@/components/ui/Button';

/**
 * UXShowcase - Phase 9A UI/UX增强演示组件
 * 
 * 展示Operation Velvet Glove的成果：
 * 1. 统一的StatusDisplay组件
 * 2. 流畅的动画系统
 * 3. 标准化的字体和间距
 */
export const UXShowcase: React.FC = () => {
  const [currentDemo, setCurrentDemo] = useState<'loading' | 'error' | 'empty' | 'success'>('loading');
  const [showAnimations, setShowAnimations] = useState(false);

  const mockTags = ['风景摄影', '高质量', '春季', '自然', '户外'];
  const mockFiles = [
    { id: 1, name: '风景照片1.jpg', size: '2.3MB' },
    { id: 2, name: '风景照片2.jpg', size: '1.8MB' },
    { id: 3, name: '风景照片3.jpg', size: '3.1MB' },
  ];

  const renderStatusDemo = () => {
    switch (currentDemo) {
      case 'loading':
        return (
          <StatusDisplay
            status="loading"
            message="正在加载图像文件..."
          />
        );
      case 'error':
        return (
          <StatusDisplay
            status="error"
            message="加载失败"
            errorMessage="网络连接超时，请检查网络设置后重试。详细错误信息：Connection timeout after 30 seconds."
          />
        );
      case 'empty':
        return (
          <StatusDisplay
            status="empty"
            message={
              <div className="space-y-4">
                <div>暂无图像文件</div>
                <p className="text-muted-foreground">
                  拖拽图片到此处上传，或点击下方按钮选择文件
                </p>
                <Button variant="primary">
                  📁 选择图像
                </Button>
              </div>
            }
          />
        );
      case 'success':
        return (
          <StatusDisplay status="idle">
            <div className="p-6 space-y-4">
              <h3 className="text-lg font-medium">文件列表</h3>
              <div className="space-y-2">
                {mockFiles.map((file, index) => (
                  <div 
                    key={file.id}
                    className="flex items-center justify-between p-3 bg-card border rounded-lg animate-slide-up"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">🖼️</div>
                      <div>
                        <div className="font-medium">{file.name}</div>
                        <div className="text-sm text-muted-foreground">{file.size}</div>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      查看
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </StatusDisplay>
        );
    }
  };

  const renderAnimationDemo = () => {
    if (!showAnimations) return null;

    return (
      <div className="space-y-4 animate-fade-in">
        <h3 className="text-lg font-medium">AI标签建议</h3>
        <div className="flex flex-wrap gap-2">
          {mockTags.map((tag, index) => (
            <button
              key={tag}
              className="px-3 py-1 bg-primary/10 border border-primary/30 text-primary rounded-full text-sm hover:bg-primary/20 transition-normal animate-slide-up"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              🤖 {tag}
            </button>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Phase 9A: Operation Velvet Glove</h1>
        <p className="text-muted-foreground">
          UI/UX增强演示 - 统一组件、流畅动画、标准化设计
        </p>
      </div>

      {/* StatusDisplay演示 */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">1. 统一状态显示组件</h2>
        <div className="flex gap-2 flex-wrap">
          <Button 
            variant={currentDemo === 'loading' ? 'primary' : 'outline'}
            onClick={() => setCurrentDemo('loading')}
          >
            加载状态
          </Button>
          <Button 
            variant={currentDemo === 'error' ? 'primary' : 'outline'}
            onClick={() => setCurrentDemo('error')}
          >
            错误状态
          </Button>
          <Button 
            variant={currentDemo === 'empty' ? 'primary' : 'outline'}
            onClick={() => setCurrentDemo('empty')}
          >
            空状态
          </Button>
          <Button 
            variant={currentDemo === 'success' ? 'primary' : 'outline'}
            onClick={() => setCurrentDemo('success')}
          >
            成功状态
          </Button>
        </div>
        <div className="border rounded-lg min-h-[300px]">
          {renderStatusDemo()}
        </div>
      </div>

      {/* 动画系统演示 */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">2. 流畅动画系统</h2>
        <Button 
          variant="primary"
          onClick={() => setShowAnimations(!showAnimations)}
        >
          {showAnimations ? '隐藏动画' : '显示动画'}
        </Button>
        {renderAnimationDemo()}
      </div>

      {/* 设计系统演示 */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">3. 标准化设计系统</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h3 className="text-lg font-medium">字体层级</h3>
            <div className="space-y-2">
              <div style={{ fontSize: 'var(--font-size-xs)' }}>Extra Small (12px)</div>
              <div style={{ fontSize: 'var(--font-size-sm)' }}>Small (14px)</div>
              <div style={{ fontSize: 'var(--font-size-base)' }}>Base (16px)</div>
              <div style={{ fontSize: 'var(--font-size-lg)' }}>Large (18px)</div>
              <div style={{ fontSize: 'var(--font-size-xl)' }}>Extra Large (20px)</div>
            </div>
          </div>
          <div className="space-y-3">
            <h3 className="text-lg font-medium">间距系统</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-primary" style={{ width: 'var(--space-1)' }}></div>
                <span>Space 1 (4px)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-primary" style={{ width: 'var(--space-2)' }}></div>
                <span>Space 2 (8px)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-primary" style={{ width: 'var(--space-4)' }}></div>
                <span>Space 4 (16px)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-primary" style={{ width: 'var(--space-8)' }}></div>
                <span>Space 8 (32px)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 成果总结 */}
      <div className="bg-card p-6 rounded-lg border">
        <h2 className="text-2xl font-semibold mb-4">✅ Phase 9A 成果总结</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl mb-2">🎯</div>
            <h3 className="font-medium">统一组件</h3>
            <p className="text-sm text-muted-foreground">StatusDisplay组件统一了加载、错误、空状态的显示</p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-2">✨</div>
            <h3 className="font-medium">流畅动画</h3>
            <p className="text-sm text-muted-foreground">CSS动画系统提供专业的视觉反馈</p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-2">🎨</div>
            <h3 className="font-medium">设计规范</h3>
            <p className="text-sm text-muted-foreground">统一的字体、间距和颜色系统</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UXShowcase;
