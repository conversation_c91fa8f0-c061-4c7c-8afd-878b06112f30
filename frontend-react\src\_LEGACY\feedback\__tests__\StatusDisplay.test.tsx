import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import StatusDisplay from '../StatusDisplay';

describe('StatusDisplay', () => {
  describe('idle状态', () => {
    it('应该渲染子组件', () => {
      render(
        <StatusDisplay status="idle">
          <div data-testid="child-content">子组件内容</div>
        </StatusDisplay>
      );

      expect(screen.getByTestId('child-content')).toBeInTheDocument();
    });
  });

  describe('loading状态', () => {
    it('应该显示加载动画', () => {
      render(<StatusDisplay status="loading" />);

      expect(screen.getByLabelText('加载中')).toBeInTheDocument();
      expect(document.querySelector('.spinner')).toBeInTheDocument();
    });

    it('应该显示自定义加载消息', () => {
      render(<StatusDisplay status="loading" message="正在处理数据..." />);

      expect(screen.getByText('正在处理数据...')).toBeInTheDocument();
    });
  });

  describe('error状态', () => {
    it('应该显示错误图标和默认消息', () => {
      render(<StatusDisplay status="error" />);

      expect(screen.getByText('发生了错误')).toBeInTheDocument();
      expect(document.querySelector('.status-display__icon--error')).toBeInTheDocument();
    });

    it('应该显示自定义错误消息', () => {
      render(<StatusDisplay status="error" message="网络连接失败" />);

      expect(screen.getByText('网络连接失败')).toBeInTheDocument();
    });

    it('应该支持错误详情展开', () => {
      render(
        <StatusDisplay 
          status="error" 
          message="请求失败" 
          errorMessage="详细错误信息：服务器返回500错误"
        />
      );

      const toggleButton = screen.getByText('显示详情');
      expect(toggleButton).toBeInTheDocument();

      // 点击展开详情
      fireEvent.click(toggleButton);
      expect(screen.getByText('详细错误信息：服务器返回500错误')).toBeInTheDocument();
      expect(screen.getByText('隐藏详情')).toBeInTheDocument();

      // 点击隐藏详情
      fireEvent.click(screen.getByText('隐藏详情'));
      expect(screen.queryByText('详细错误信息：服务器返回500错误')).not.toBeInTheDocument();
      expect(screen.getByText('显示详情')).toBeInTheDocument();
    });
  });

  describe('empty状态', () => {
    it('应该显示空状态图标和默认消息', () => {
      render(<StatusDisplay status="empty" />);

      expect(screen.getByText('暂无内容')).toBeInTheDocument();
      expect(document.querySelector('.status-display__icon--empty')).toBeInTheDocument();
    });

    it('应该显示自定义空状态消息', () => {
      render(<StatusDisplay status="empty" message="没有找到匹配的文件" />);

      expect(screen.getByText('没有找到匹配的文件')).toBeInTheDocument();
    });

    it('应该支持React节点作为消息', () => {
      render(
        <StatusDisplay 
          status="empty" 
          message={<span data-testid="custom-message">自定义<strong>消息</strong></span>}
        />
      );

      expect(screen.getByTestId('custom-message')).toBeInTheDocument();
      expect(screen.getByText('消息')).toBeInTheDocument();
    });
  });

  describe('样式和可访问性', () => {
    it('应该应用自定义CSS类名', () => {
      render(<StatusDisplay status="loading" className="custom-class" />);

      expect(document.querySelector('.status-display.custom-class')).toBeInTheDocument();
    });

    it('应该为错误详情按钮设置正确的aria-expanded属性', () => {
      render(
        <StatusDisplay 
          status="error" 
          errorMessage="错误详情"
        />
      );

      const toggleButton = screen.getByText('显示详情');
      expect(toggleButton).toHaveAttribute('aria-expanded', 'false');

      fireEvent.click(toggleButton);
      expect(toggleButton).toHaveAttribute('aria-expanded', 'true');
    });
  });
});
