// src/components/toolbars/DetailsToolbarSet.tsx
import React from 'react';
import { GridSquaresIcon } from '../../assets/icons/GridSquaresIcon';
import { IconButton } from '../ui/IconButton';
import { TextButton } from '../ui/TextButton';

// 创建一个简单的 Toolbar 组件来统一布局
function Toolbar({ children, justify = 'justify-between' }: { children: React.ReactNode, justify?: string }) {
    // 严格按照蓝图：高度16px，间距4px
    return (
        <div className={`w-full h-[16px] flex items-center ${justify} mb-[4px]`}>
            {children}
        </div>
    );
}

export function DetailsToolbarSet() {
    return (
        <div className="flex flex-col">
            {/* 详情面板工具栏 - 唯一一行 */}
            <Toolbar>
                {/* 左对齐：详情标题 */}
                <div className="flex items-center gap-[4px]">
                    <TextButton>详情</TextButton>
                </div>
                {/* 右对齐：设置按钮 */}
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={GridSquaresIcon} tooltip="设置" />
                </div>
            </Toolbar>
        </div>
    );
}
