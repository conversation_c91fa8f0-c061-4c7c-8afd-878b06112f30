// 🔥 BEDROCK: App.tsx - 骨架应用入口
// 职责：提供最小化的应用容器，只渲染案例列表

import React, { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CaseList } from './components/CaseList';
import { useCaseStore } from './store/useCaseStore';

// 🔥 BEDROCK: 简化的QueryClient配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1, // 减少重试次数
      staleTime: 5 * 60 * 1000, // 5分钟
    },
  },
});

// 🔥 BEDROCK: 数据获取组件
function DataLoader() {
  const fetchCases = useCaseStore(state => state.fetchCases);

  useEffect(() => {
    console.log('%c🔥 BEDROCK: 启动数据加载', 'color: #FF6B35; font-weight: bold;');
    fetchCases();
  }, [fetchCases]);

  return null;
}

// 🔥 BEDROCK: 骨架应用内容
function AppContent() {
  return (
    <div className="min-h-screen bg-gray-50">
      <DataLoader />
      <CaseList />
    </div>
  );
}

// 🔥 BEDROCK: 主应用组件
function App() {
  console.log('%c🔥 BEDROCK: 启动骨架应用', 'color: #FF6B35; font-weight: bold;');

  return (
    <QueryClientProvider client={queryClient}>
      <AppContent />
    </QueryClientProvider>
  );
}

export default App;

