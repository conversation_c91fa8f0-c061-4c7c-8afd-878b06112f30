// useSearchStore.test.ts - Phase 7A: 搜索状态管理测试
// 测试搜索条件管理、状态控制和性能统计

import { renderHook, act } from '@testing-library/react';
import { useSearchStore, SearchCriterion } from '../useSearchStore';

// ============================================================================
// 测试数据
// ============================================================================

const mockCriterion: Omit<SearchCriterion, 'id'> = {
  field: 'fileName',
  operator: 'contains',
  value: 'test',
  caseSensitive: false,
};

const mockCriterion2: Omit<SearchCriterion, 'id'> = {
  field: 'fileSize',
  operator: '>=',
  value: 1024,
};

// ============================================================================
// 测试套件
// ============================================================================

describe('useSearchStore', () => {
  beforeEach(() => {
    // 每个测试前重置store状态
    const { result } = renderHook(() => useSearchStore());
    act(() => {
      result.current.resetSearch();
    });
  });

  describe('搜索条件管理', () => {
    it('应该正确添加搜索条件', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addCriterion(mockCriterion);
      });

      expect(result.current.criteria).toHaveLength(1);
      expect(result.current.criteria[0]).toMatchObject(mockCriterion);
      expect(result.current.criteria[0].id).toBeDefined();
      expect(result.current.isActive).toBe(true);
    });

    it('应该正确移除搜索条件', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addCriterion(mockCriterion);
        result.current.addCriterion(mockCriterion2);
      });

      const firstCriterionId = result.current.criteria[0].id;

      act(() => {
        result.current.removeCriterion(firstCriterionId);
      });

      expect(result.current.criteria).toHaveLength(1);
      expect(result.current.criteria[0]).toMatchObject(mockCriterion2);
    });

    it('应该正确更新搜索条件', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addCriterion(mockCriterion);
      });

      const criterionId = result.current.criteria[0].id;
      const updates = { value: 'updated', caseSensitive: true };

      act(() => {
        result.current.updateCriterion(criterionId, updates);
      });

      expect(result.current.criteria[0].value).toBe('updated');
      expect(result.current.criteria[0].caseSensitive).toBe(true);
    });

    it('应该正确清除所有搜索条件', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addCriterion(mockCriterion);
        result.current.addCriterion(mockCriterion2);
      });

      expect(result.current.criteria).toHaveLength(2);

      act(() => {
        result.current.clearSearch();
      });

      expect(result.current.criteria).toHaveLength(0);
      expect(result.current.isActive).toBe(false);
      expect(result.current.resultCount).toBe(0);
    });
  });

  describe('搜索状态控制', () => {
    it('应该正确切换搜索状态', () => {
      const { result } = renderHook(() => useSearchStore());

      expect(result.current.isActive).toBe(false);

      act(() => {
        result.current.toggleSearch();
      });

      expect(result.current.isActive).toBe(true);

      act(() => {
        result.current.toggleSearch();
      });

      expect(result.current.isActive).toBe(false);
    });

    it('应该正确设置结果计数', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.setResultCount(42);
      });

      expect(result.current.resultCount).toBe(42);
    });

    it('应该正确控制搜索面板展开状态', () => {
      const { result } = renderHook(() => useSearchStore());

      expect(result.current.isSearchPanelExpanded).toBe(false);

      act(() => {
        result.current.toggleSearchPanel();
      });

      expect(result.current.isSearchPanelExpanded).toBe(true);

      act(() => {
        result.current.setSearchPanelExpanded(false);
      });

      expect(result.current.isSearchPanelExpanded).toBe(false);
    });
  });

  describe('性能统计', () => {
    it('应该正确记录搜索性能', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.recordSearchPerformance(50);
      });

      expect(result.current.searchPerformance.lastSearchDuration).toBe(50);
      expect(result.current.searchPerformance.averageSearchDuration).toBe(50);
      expect(result.current.searchPerformance.searchCount).toBe(1);

      act(() => {
        result.current.recordSearchPerformance(100);
      });

      expect(result.current.searchPerformance.lastSearchDuration).toBe(100);
      expect(result.current.searchPerformance.averageSearchDuration).toBe(75); // (50 + 100) / 2
      expect(result.current.searchPerformance.searchCount).toBe(2);
    });
  });

  describe('批量操作', () => {
    it('应该正确设置多个条件', () => {
      const { result } = renderHook(() => useSearchStore());

      const criteria: SearchCriterion[] = [
        { ...mockCriterion, id: 'test-1' },
        { ...mockCriterion2, id: 'test-2' },
      ];

      act(() => {
        result.current.setCriteria(criteria);
      });

      expect(result.current.criteria).toEqual(criteria);
      expect(result.current.isActive).toBe(true);
    });

    it('应该正确复制搜索条件', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addCriterion(mockCriterion);
      });

      const originalId = result.current.criteria[0].id;

      act(() => {
        result.current.duplicateCriterion(originalId);
      });

      expect(result.current.criteria).toHaveLength(2);
      expect(result.current.criteria[0]).toMatchObject(mockCriterion);
      expect(result.current.criteria[1]).toMatchObject(mockCriterion);
      expect(result.current.criteria[0].id).not.toBe(result.current.criteria[1].id);
    });
  });

  describe('预设搜索', () => {
    it('应该正确应用图片预设搜索', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.applyPresetSearch('images');
      });

      expect(result.current.criteria).toHaveLength(2);
      expect(result.current.criteria[0].field).toBe('fileType');
      expect(result.current.criteria[0].value).toBe('jpg');
      expect(result.current.criteria[1].field).toBe('fileType');
      expect(result.current.criteria[1].value).toBe('png');
      expect(result.current.isActive).toBe(true);
    });

    it('应该正确应用大文件预设搜索', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.applyPresetSearch('large-files');
      });

      expect(result.current.criteria).toHaveLength(1);
      expect(result.current.criteria[0].field).toBe('fileSize');
      expect(result.current.criteria[0].operator).toBe('>=');
      expect(result.current.criteria[0].value).toBe(10 * 1024 * 1024);
    });

    it('应该正确应用最近文件预设搜索', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.applyPresetSearch('recent');
      });

      expect(result.current.criteria).toHaveLength(1);
      expect(result.current.criteria[0].field).toBe('modifiedTime');
      expect(result.current.criteria[0].operator).toBe('>=');
      expect(result.current.criteria[0].value).toBeInstanceOf(Date);
    });
  });

  describe('重置功能', () => {
    it('应该正确重置所有搜索状态', () => {
      const { result } = renderHook(() => useSearchStore());

      // 设置一些状态
      act(() => {
        result.current.addCriterion(mockCriterion);
        result.current.setResultCount(100);
        result.current.setSearchPanelExpanded(true);
        result.current.recordSearchPerformance(50);
      });

      // 验证状态已设置
      expect(result.current.criteria).toHaveLength(1);
      expect(result.current.resultCount).toBe(100);
      expect(result.current.isSearchPanelExpanded).toBe(true);

      // 重置
      act(() => {
        result.current.resetSearch();
      });

      // 验证重置结果
      expect(result.current.criteria).toHaveLength(0);
      expect(result.current.isActive).toBe(false);
      expect(result.current.resultCount).toBe(0);
      expect(result.current.isSearchPanelExpanded).toBe(false);
      expect(result.current.searchPerformance.searchCount).toBe(0);
    });
  });

  describe('边界情况', () => {
    it('应该处理不存在的条件ID', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addCriterion(mockCriterion);
      });

      const originalLength = result.current.criteria.length;

      act(() => {
        result.current.removeCriterion('non-existent-id');
      });

      expect(result.current.criteria).toHaveLength(originalLength);
    });

    it('应该处理空条件列表的状态更新', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.updateCriterion('non-existent-id', { value: 'test' });
      });

      expect(result.current.criteria).toHaveLength(0);
    });

    it('应该处理复制不存在的条件', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.duplicateCriterion('non-existent-id');
      });

      expect(result.current.criteria).toHaveLength(0);
    });
  });
});
