// CaseSwitcher.test.tsx - Phase 7C: 案例切换器组件测试

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { CaseSwitcher } from '../CaseSwitcher';
import * as storeHooks from '@/store';
import type { Case } from '@/store/useCaseStore';

// Mock store hooks
vi.mock('@/store', () => ({
  useCurrentCase: vi.fn(),
  useCaseList: vi.fn(),
  useCaseOperations: vi.fn(),
  useCaseErrors: vi.fn(),
}));

describe('CaseSwitcher', () => {
  const mockUseCurrentCase = vi.mocked(storeHooks.useCurrentCase);
  const mockUseCaseList = vi.mocked(storeHooks.useCaseList);
  const mockUseCaseOperations = vi.mocked(storeHooks.useCaseOperations);
  const mockUseCaseErrors = vi.mocked(storeHooks.useCaseErrors);

  const mockCases: Case[] = [
    {
      id: 1,
      name: '测试案例1',
      description: '第一个测试案例',
      created_at: '2024-07-28T10:00:00Z',
      status: 'ACTIVE',
      file_count: 10,
    },
    {
      id: 2,
      name: '测试案例2',
      description: '第二个测试案例',
      created_at: '2024-07-28T11:00:00Z',
      status: 'ACTIVE',
      file_count: 5,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    mockUseCurrentCase.mockReturnValue({
      currentCase: mockCases[0],
      currentCaseId: 1,
      isSwitching: false,
    });

    mockUseCaseList.mockReturnValue({
      allCases: mockCases,
      isLoading: false,
      fetchCases: vi.fn(),
    });

    mockUseCaseOperations.mockReturnValue({
      setCurrentCase: vi.fn(),
      switchToCase: vi.fn(),
      createCase: vi.fn(),
      refreshCurrentCase: vi.fn(),
    });

    mockUseCaseErrors.mockReturnValue({
      error: null,
      lastError: null,
      clearError: vi.fn(),
    });
  });

  describe('Rendering', () => {
    it('应该显示当前案例信息', () => {
      render(<CaseSwitcher />);

      expect(screen.getByText('测试案例1')).toBeInTheDocument();
      expect(screen.getByText('10个文件')).toBeInTheDocument();
    });

    it('应该显示占位符当没有选中案例时', () => {
      mockUseCurrentCase.mockReturnValue({
        currentCase: null,
        currentCaseId: null,
        isSwitching: false,
      });

      render(<CaseSwitcher />);

      expect(screen.getByText('选择案例')).toBeInTheDocument();
    });

    it('应该显示切换状态', () => {
      mockUseCurrentCase.mockReturnValue({
        currentCase: mockCases[0],
        currentCaseId: 1,
        isSwitching: true,
      });

      render(<CaseSwitcher />);

      expect(screen.getByText('切换中...')).toBeInTheDocument();
    });
  });

  describe('Dropdown Interaction', () => {
    it('应该打开和关闭下拉菜单', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');

      // 初始状态下拉菜单应该是关闭的
      expect(screen.queryByPlaceholderText('搜索案例...')).not.toBeInTheDocument();

      // 点击打开下拉菜单
      fireEvent.click(trigger);
      expect(screen.getByPlaceholderText('搜索案例...')).toBeInTheDocument();

      // 再次点击关闭下拉菜单
      fireEvent.click(trigger);
      expect(screen.queryByPlaceholderText('搜索案例...')).not.toBeInTheDocument();
    });

    it('应该显示案例列表', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      // 使用更具体的选择器来避免重复元素问题
      expect(screen.getAllByText('测试案例1')).toHaveLength(2); // 一个在trigger，一个在dropdown
      expect(screen.getAllByText('测试案例2')).toHaveLength(1); // 只在dropdown中
      expect(screen.getByText('第一个测试案例')).toBeInTheDocument();
      expect(screen.getByText('第二个测试案例')).toBeInTheDocument();
    });

    it('应该高亮当前选中的案例', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      // 使用class选择器查找dropdown中的活跃项
      const activeItems = document.querySelectorAll('.case-switcher__item--active');
      expect(activeItems.length).toBeGreaterThan(0);
    });
  });

  describe('Case Selection', () => {
    it('应该切换到选中的案例', async () => {
      const mockSwitchToCase = vi.fn();
      mockUseCaseOperations.mockReturnValue({
        setCurrentCase: vi.fn(),
        switchToCase: mockSwitchToCase,
        createCase: vi.fn(),
        refreshCurrentCase: vi.fn(),
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      // 查找dropdown中的案例2按钮
      const case2Items = screen.getAllByText('测试案例2');
      const case2Button = case2Items[0].closest('button');
      fireEvent.click(case2Button!);

      await waitFor(() => {
        expect(mockSwitchToCase).toHaveBeenCalledWith(2);
      });
    });

    it('应该忽略点击当前案例', async () => {
      const mockSwitchToCase = vi.fn();
      mockUseCaseOperations.mockReturnValue({
        setCurrentCase: vi.fn(),
        switchToCase: mockSwitchToCase,
        createCase: vi.fn(),
        refreshCurrentCase: vi.fn(),
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      // 查找dropdown中的案例1按钮（跳过trigger中的）
      const case1Items = screen.getAllByText('测试案例1');
      const case1Button = case1Items[1]?.closest('button'); // 第二个是dropdown中的
      if (case1Button) {
        fireEvent.click(case1Button);
      }

      expect(mockSwitchToCase).not.toHaveBeenCalled();
    });
  });

  describe('Search Functionality', () => {
    it('应该过滤案例列表', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      const searchInput = screen.getByPlaceholderText('搜索案例...');
      fireEvent.change(searchInput, { target: { value: '案例1' } });

      // 验证过滤结果：测试案例1应该存在，测试案例2应该不存在
      expect(screen.getAllByText('测试案例1').length).toBeGreaterThan(0);
      expect(screen.queryByText('测试案例2')).not.toBeInTheDocument();
    });

    it('应该显示无匹配结果', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      const searchInput = screen.getByPlaceholderText('搜索案例...');
      fireEvent.change(searchInput, { target: { value: '不存在的案例' } });

      expect(screen.getByText('未找到匹配的案例')).toBeInTheDocument();
    });
  });

  describe('Case Creation', () => {
    it('应该显示创建案例表单', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      const createButton = screen.getByText('新建案例');
      fireEvent.click(createButton);

      expect(screen.getByPlaceholderText('输入案例名称...')).toBeInTheDocument();
    });

    it('应该创建新案例', async () => {
      const mockCreateCase = vi.fn().mockResolvedValue({
        id: 3,
        name: '新案例',
        status: 'ACTIVE',
        file_count: 0,
      });
      const mockSwitchToCase = vi.fn();

      mockUseCaseOperations.mockReturnValue({
        setCurrentCase: vi.fn(),
        switchToCase: mockSwitchToCase,
        createCase: mockCreateCase,
        refreshCurrentCase: vi.fn(),
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      const createButton = screen.getByText('新建案例');
      fireEvent.click(createButton);

      const nameInput = screen.getByPlaceholderText('输入案例名称...');
      fireEvent.change(nameInput, { target: { value: '新案例' } });

      // 使用更具体的选择器查找确认按钮
      const confirmButton = document.querySelector('.case-switcher__create-confirm');
      fireEvent.click(confirmButton!);

      await waitFor(() => {
        expect(mockCreateCase).toHaveBeenCalledWith('新案例');
        expect(mockSwitchToCase).toHaveBeenCalledWith(3);
      });
    });

    it('应该取消创建案例', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      const createButton = screen.getByText('新建案例');
      fireEvent.click(createButton);

      const cancelButton = screen.getByText('×');
      fireEvent.click(cancelButton);

      expect(screen.queryByPlaceholderText('输入案例名称...')).not.toBeInTheDocument();
      expect(screen.getByText('新建案例')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('应该显示加载状态', () => {
      // 设置没有当前案例且正在加载的状态
      mockUseCurrentCase.mockReturnValue({
        currentCase: null,
        currentCaseId: null,
        isSwitching: false,
      });

      mockUseCaseList.mockReturnValue({
        allCases: [],
        isLoading: true,
        fetchCases: vi.fn(),
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');

      // 验证按钮在加载时被禁用
      expect(trigger).toBeDisabled();

      // 验证显示选择案例的占位符
      expect(screen.getByText('选择案例')).toBeInTheDocument();
    });

    it('应该显示空状态', () => {
      mockUseCaseList.mockReturnValue({
        allCases: [],
        isLoading: false,
        fetchCases: vi.fn(),
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      expect(screen.getByText('暂无案例')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('应该显示错误信息', () => {
      mockUseCaseErrors.mockReturnValue({
        error: '网络连接失败',
        lastError: null,
        clearError: vi.fn(),
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      expect(screen.getByText('网络连接失败')).toBeInTheDocument();
    });

    it('应该清除错误', () => {
      const mockClearError = vi.fn();
      mockUseCaseErrors.mockReturnValue({
        error: '网络连接失败',
        lastError: null,
        clearError: mockClearError,
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      const closeButton = screen.getByText('×');
      fireEvent.click(closeButton);

      expect(mockClearError).toHaveBeenCalled();
    });
  });

  describe('Keyboard Navigation', () => {
    it('应该支持回车键创建案例', async () => {
      const mockCreateCase = vi.fn().mockResolvedValue({
        id: 3,
        name: '新案例',
        status: 'ACTIVE',
        file_count: 0,
      });

      mockUseCaseOperations.mockReturnValue({
        setCurrentCase: vi.fn(),
        switchToCase: vi.fn(),
        createCase: mockCreateCase,
        refreshCurrentCase: vi.fn(),
      });

      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      const createButton = screen.getByText('新建案例');
      fireEvent.click(createButton);

      const nameInput = screen.getByPlaceholderText('输入案例名称...');
      fireEvent.change(nameInput, { target: { value: '新案例' } });
      fireEvent.keyDown(nameInput, { key: 'Enter' });

      await waitFor(() => {
        expect(mockCreateCase).toHaveBeenCalledWith('新案例');
      });
    });

    it('应该支持ESC键关闭下拉菜单', () => {
      render(<CaseSwitcher />);

      const trigger = screen.getByRole('button');
      fireEvent.click(trigger);

      expect(screen.getByPlaceholderText('搜索案例...')).toBeInTheDocument();

      fireEvent.keyDown(screen.getByPlaceholderText('搜索案例...'), { key: 'Escape' });

      expect(screen.queryByPlaceholderText('搜索案例...')).not.toBeInTheDocument();
    });
  });
});
