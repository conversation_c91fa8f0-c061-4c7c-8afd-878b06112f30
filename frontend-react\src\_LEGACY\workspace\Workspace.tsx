// Workspace - Integrated with existing API and state management
// 集成现有API和状态管理的工作台组件

import React, { useState, useMemo } from 'react';
import {
  ChevronDown,
  ChevronUp,
  Settings,
  X,
  Minimize2,
  Code,
  BarChart3,
  Brain,
  Clipboard,
  Layers,
  Search,
  Download,
  Upload,
  Zap,
  Filter,
  FileText,
  Image,
  Palette,
  Crop,
  RotateCcw,
  Scissors,
  Database,
  Tags,
  Archive,
  Target,
  Workflow,
  History,
  Play,
  Pause,
  CheckSquare,
  RefreshCw,
  Trash2,
  Copy,
  Move,
  Eye,
  Share2,
  Lock,
  Unlock,
  Calendar,
  Clock,
  Gauge
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Hooks and utilities
// Pre-Phase 5: 移除useApi依赖，使用文件系统状态
// import { useFiles } from '@/hooks/useApi';
import { useUIStore } from '@/store';
import { adaptFileItemsToUIImageItems } from '@/adapters/uiDataAdapters';

// ============================================================================
// 接口定义
// ============================================================================

interface WorkspaceProps {
  onToggleWorkspace?: () => void;
  onSwapGalleryWorkspace?: () => void;
  galleryWorkspaceSwapped?: boolean;
  className?: string;
}

interface WorkspaceTab {
  id: string;
  title: string;
  icon: React.ComponentType;
  component: React.ComponentType<any>;
}

// ============================================================================
// 工作台子组件
// ============================================================================

// 剪贴板组件
const ClipboardWorkspace: React.FC = () => {
  const { selectedFileIds, clipboardFiles } = useUIStore();

  return (
    <div className="p-4 h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
      <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>剪贴板</h3>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>
            已选择 {selectedFileIds.length} 个文件
          </span>
          <Button size="sm" variant="outline">
            <Copy className="w-4 h-4 mr-2" />
            复制到剪贴板
          </Button>
        </div>

        <div className="grid grid-cols-2 gap-2">
          {clipboardFiles.slice(0, 6).map((file, index) => (
            <div
              key={file.id}
              className="relative group border rounded p-2"
              style={{ borderColor: 'var(--mizzy-border-ui)' }}
            >
              <div className="aspect-square bg-muted rounded mb-2 flex items-center justify-center">
                <Image className="w-8 h-8" style={{ color: 'var(--mizzy-icon)' }} />
              </div>
              <div className="text-xs truncate">文件 {file.id}</div>
              <button
                className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          ))}
        </div>

        {clipboardFiles.length === 0 && (
          <div className="text-center py-8" style={{ color: 'var(--mizzy-icon)' }}>
            <Clipboard className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>剪贴板为空</p>
            <p className="text-xs">选择文件并添加到剪贴板</p>
          </div>
        )}
      </div>
    </div>
  );
};

// 规则配置组件
const RuleConfiguration: React.FC = () => (
  <div className="p-4 h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>规则配置</h3>
    <div className="space-y-4">
      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>规则名称</label>
        <Input
          placeholder="输入规则名称"
          style={{
            background: 'var(--mizzy-input)',
            borderColor: 'var(--mizzy-border-ui)',
            color: 'var(--mizzy-content)'
          }}
        />
      </div>
      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>规则类型</label>
        <select
          className="w-full p-2 rounded border"
          style={{
            background: 'var(--mizzy-input)',
            borderColor: 'var(--mizzy-border-ui)',
            color: 'var(--mizzy-content)'
          }}
        >
          <option>文件名规则</option>
          <option>标签规则</option>
          <option>元数据规则</option>
        </select>
      </div>
      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>规则条件</label>
        <textarea
          className="w-full p-2 rounded border h-24"
          style={{
            background: 'var(--mizzy-input)',
            borderColor: 'var(--mizzy-border-ui)',
            color: 'var(--mizzy-content)'
          }}
          placeholder="输入规则条件..."
        />
      </div>
      <Button className="w-full">
        <CheckSquare className="w-4 h-4 mr-2" />
        保存规则
      </Button>
    </div>
  </div>
);

// 数据分析组件
const DataAnalysis: React.FC = () => (
  <div className="p-4 h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>数据分析</h3>
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="p-3 rounded" style={{ background: 'var(--mizzy-input)' }}>
          <div className="text-2xl font-bold">1,234</div>
          <div className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>总文件数</div>
        </div>
        <div className="p-3 rounded" style={{ background: 'var(--mizzy-input)' }}>
          <div className="text-2xl font-bold">567</div>
          <div className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>已标记</div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between">
          <span className="text-sm">JPEG</span>
          <span className="text-sm">75%</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div className="bg-primary h-2 rounded-full" style={{ width: '75%' }}></div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between">
          <span className="text-sm">PNG</span>
          <span className="text-sm">20%</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div className="bg-primary h-2 rounded-full" style={{ width: '20%' }}></div>
        </div>
      </div>

      <Button variant="outline" className="w-full">
        <BarChart3 className="w-4 h-4 mr-2" />
        查看详细报告
      </Button>
    </div>
  </div>
);

// AI处理组件
const AIProcessing: React.FC = () => (
  <div className="p-4 h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>AI处理</h3>
    <div className="space-y-4">
      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>处理模式</label>
        <select
          className="w-full p-2 rounded border"
          style={{
            background: 'var(--mizzy-input)',
            borderColor: 'var(--mizzy-border-ui)',
            color: 'var(--mizzy-content)'
          }}
        >
          <option>自动标签</option>
          <option>图像分类</option>
          <option>质量评估</option>
          <option>相似度分析</option>
        </select>
      </div>

      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>处理进度</label>
        <div className="w-full bg-muted rounded-full h-2 mb-2">
          <div className="bg-primary h-2 rounded-full" style={{ width: '45%' }}></div>
        </div>
        <div className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>45% 完成 (123/274 文件)</div>
      </div>

      <div className="flex gap-2">
        <Button className="flex-1">
          <Play className="w-4 h-4 mr-2" />
          开始处理
        </Button>
        <Button variant="outline" className="flex-1">
          <Pause className="w-4 h-4 mr-2" />
          暂停
        </Button>
      </div>

      <div className="space-y-2">
        <div className="text-sm font-medium">最近结果</div>
        <div className="space-y-1">
          <div className="text-xs p-2 rounded" style={{ background: 'var(--mizzy-input)' }}>
            图片_001.jpg → 添加标签: 风景, 自然
          </div>
          <div className="text-xs p-2 rounded" style={{ background: 'var(--mizzy-input)' }}>
            图片_002.jpg → 质量评分: 8.5/10
          </div>
        </div>
      </div>
    </div>
  </div>
);

// ============================================================================
// Workspace 主组件
// ============================================================================

export const Workspace: React.FC<WorkspaceProps> = ({
  onToggleWorkspace,
  onSwapGalleryWorkspace,
  galleryWorkspaceSwapped = false,
  className,
}) => {
  // ========================================
  // 状态管理
  // ========================================
  const [activeTab, setActiveTab] = useState('clipboard');
  const [isMinimized, setIsMinimized] = useState(false);

  // 获取状态
  const { selectedFileIds } = useUIStore();

  // ========================================
  // 工作台标签配置
  // ========================================
  const workspaceTabs: WorkspaceTab[] = [
    {
      id: 'clipboard',
      title: '剪贴板',
      icon: Clipboard,
      component: ClipboardWorkspace,
    },
    {
      id: 'rules',
      title: '规则',
      icon: Code,
      component: RuleConfiguration,
    },
    {
      id: 'analysis',
      title: '分析',
      icon: BarChart3,
      component: DataAnalysis,
    },
    {
      id: 'ai',
      title: 'AI处理',
      icon: Brain,
      component: AIProcessing,
    },
  ];

  // ========================================
  // 事件处理函数
  // ========================================

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // ========================================
  // 渲染
  // ========================================

  return (
    <div
      className={`h-full flex flex-col ${className || ''}`}
      style={{ background: 'var(--mizzy-workspace)' }}
    >
      {/* 工作台头部 */}
      <div
        className="flex items-center justify-between p-3 border-b"
        style={{ borderColor: 'var(--mizzy-border-ui)' }}
      >
        <div className="flex items-center gap-2">
          <Workflow className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          <span className="font-medium" style={{ color: 'var(--mizzy-title)' }}>
            工作台
          </span>
          {selectedFileIds.length > 0 && (
            <span
              className="text-xs px-2 py-1 rounded"
              style={{
                background: 'var(--mizzy-highlight)',
                color: 'white'
              }}
            >
              {selectedFileIds.length} 个文件
            </span>
          )}
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1"
          >
            {isMinimized ? (
              <ChevronUp className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
            ) : (
              <ChevronDown className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onSwapGalleryWorkspace}
            className="p-1"
            title="交换画廊和工作台位置"
          >
            <Move className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleWorkspace}
            className="p-1"
          >
            <X className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          </Button>
        </div>
      </div>

      {/* 工作台内容 */}
      {!isMinimized && (
        <>
          {/* 标签栏 */}
          <div
            className="flex border-b"
            style={{ borderColor: 'var(--mizzy-border-ui)' }}
          >
            {workspaceTabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex items-center gap-2 px-3 py-2 text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-b-2'
                      : 'hover:bg-opacity-10'
                  }`}
                  style={{
                    color: activeTab === tab.id ? 'var(--mizzy-highlight)' : 'var(--mizzy-content)',
                    borderColor: activeTab === tab.id ? 'var(--mizzy-highlight)' : 'transparent',
                    background: activeTab === tab.id ? 'var(--mizzy-button)' : 'transparent'
                  }}
                >
                  <IconComponent className="w-4 h-4" />
                  {tab.title}
                </button>
              );
            })}
          </div>

          {/* 标签内容 */}
          <div className="flex-1 overflow-hidden">
            {workspaceTabs.map((tab) => {
              const Component = tab.component;
              return (
                <div
                  key={tab.id}
                  className={`h-full ${activeTab === tab.id ? 'block' : 'hidden'}`}
                >
                  <Component />
                </div>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};
