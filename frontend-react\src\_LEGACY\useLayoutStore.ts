// useLayoutStore.ts - Phase 6: 布局状态管理专用Store
// 管理面板可见性、尺寸持久化和布局计算

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';

// ============================================================================
// 类型定义
// ============================================================================

interface LayoutState {
  // 面板可见性状态
  isCatalogVisible: boolean;
  isInfoVisible: boolean;
  isWorkbenchVisible: boolean;
  
  // 布局尺寸状态（运行时，不持久化）
  horizontalSizes: number[];
  verticalSizes: number[];
}

interface LayoutActions {
  // 面板可见性控制
  toggleCatalog: () => void;
  toggleInfo: () => void;
  toggleWorkbench: () => void;
  setCatalogVisible: (visible: boolean) => void;
  setInfoVisible: (visible: boolean) => void;
  setWorkbenchVisible: (visible: boolean) => void;
  
  // 布局尺寸控制
  setHorizontalSizes: (sizes: number[]) => void;
  setVerticalSizes: (sizes: number[]) => void;
  
  // 布局计算工具
  getDefaultHorizontalLayout: () => number[];
  getDefaultVerticalLayout: () => number[];
  
  // 重置功能
  resetLayout: () => void;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: LayoutState = {
  isCatalogVisible: true,
  isInfoVisible: true,
  isWorkbenchVisible: true,
  horizontalSizes: [],
  verticalSizes: [],
};

// ============================================================================
// Store实现
// ============================================================================

export const useLayoutStore = create<LayoutState & LayoutActions>()(
  persist(
    devtools(
      (set, get) => ({
        ...initialState,

        // ========================================
        // 面板可见性控制实现
        // ========================================
        toggleCatalog: () =>
          set((state) => ({ isCatalogVisible: !state.isCatalogVisible })),

        toggleInfo: () =>
          set((state) => ({ isInfoVisible: !state.isInfoVisible })),

        toggleWorkbench: () =>
          set((state) => ({ isWorkbenchVisible: !state.isWorkbenchVisible })),

        setCatalogVisible: (visible) =>
          set({ isCatalogVisible: visible }),

        setInfoVisible: (visible) =>
          set({ isInfoVisible: visible }),

        setWorkbenchVisible: (visible) =>
          set({ isWorkbenchVisible: visible }),

        // ========================================
        // 布局尺寸控制实现
        // ========================================
        setHorizontalSizes: (sizes) =>
          set({ horizontalSizes: sizes }),

        setVerticalSizes: (sizes) =>
          set({ verticalSizes: sizes }),

        // ========================================
        // 布局计算工具实现
        // ========================================
        getDefaultHorizontalLayout: () => {
          const state = get();
          const panels = [];
          
          // 根据可见面板分配空间
          if (state.isCatalogVisible) panels.push(20); // Catalog: 20%
          
          // 中央区域（Gallery + Workbench）
          const centerSize = 100 - (state.isCatalogVisible ? 20 : 0) - (state.isInfoVisible ? 15 : 0);
          panels.push(centerSize);
          
          if (state.isInfoVisible) panels.push(15); // Info: 15%

          return panels;
        },

        getDefaultVerticalLayout: () => {
          const state = get();
          return state.isWorkbenchVisible ? [70, 30] : [100]; // Gallery 70%, Workbench 30%
        },

        // ========================================
        // 重置功能实现
        // ========================================
        resetLayout: () => set(initialState),
      }),
      {
        name: 'mizzy-star-layout-store',
      }
    ),
    {
      name: 'mizzy-star-layout-settings', // localStorage键名
      partialize: (state) => ({
        // 只持久化面板可见性状态
        isCatalogVisible: state.isCatalogVisible,
        isInfoVisible: state.isInfoVisible,
        isWorkbenchVisible: state.isWorkbenchVisible,
      }),
    }
  )
);

// ============================================================================
// 选择器Hooks（性能优化）
// ============================================================================

export const usePanelVisibility = () => useLayoutStore((state) => ({
  isCatalogVisible: state.isCatalogVisible,
  isInfoVisible: state.isInfoVisible,
  isWorkbenchVisible: state.isWorkbenchVisible,
  toggleCatalog: state.toggleCatalog,
  toggleInfo: state.toggleInfo,
  toggleWorkbench: state.toggleWorkbench,
}), shallow);

export const useLayoutSizes = () => useLayoutStore((state) => ({
  horizontalSizes: state.horizontalSizes,
  verticalSizes: state.verticalSizes,
  setHorizontalSizes: state.setHorizontalSizes,
  setVerticalSizes: state.setVerticalSizes,
  getDefaultHorizontalLayout: state.getDefaultHorizontalLayout,
  getDefaultVerticalLayout: state.getDefaultVerticalLayout,
}), shallow);

export default useLayoutStore;
