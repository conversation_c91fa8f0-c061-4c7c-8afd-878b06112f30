// src/components/ui/DetailsContent.tsx
import React from 'react';

interface DetailsContentProps {
  selectedImage?: {
    id: string;
    src: string;
    title?: string;
    alt?: string;
    metadata?: {
      fileName?: string;
      fileSize?: string;
      dimensions?: string;
      format?: string;
      dateCreated?: string;
      camera?: string;
      lens?: string;
      settings?: string;
    };
    tags?: string[];
  };
  className?: string;
}

export function DetailsContent({ selectedImage, className }: DetailsContentProps) {
  if (!selectedImage) {
    return (
      <div className={`flex flex-col h-full ${className}`}>
        <div className="flex-1 bg-mizzy-bg-panel">
          {/* 清空所有提示内容 */}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 详情标题栏 */}
      <div className="flex items-center justify-between mb-space-2">
        <h3 className="text-sm font-medium text-mizzy-text-title">图像详情</h3>
        <button className="text-xs text-mizzy-text-content hover:text-mizzy-text-title">
          编辑
        </button>
      </div>

      {/* 图像预览 */}
      <div className="mb-space-4">
        <div className="aspect-square bg-mizzy-bg-input rounded-sm border border-mizzy-border-base overflow-hidden">
          <img
            src={selectedImage.src}
            alt={selectedImage.alt || selectedImage.title}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* 基本信息 */}
      <div className="mb-space-4">
        <h4 className="text-xs font-medium text-mizzy-text-title mb-space-2">基本信息</h4>
        <div className="space-y-space-1">
          {selectedImage.metadata?.fileName && (
            <div className="flex justify-between text-xs">
              <span className="text-mizzy-text-content">文件名</span>
              <span className="text-mizzy-text-content">{selectedImage.metadata.fileName}</span>
            </div>
          )}
          {selectedImage.metadata?.fileSize && (
            <div className="flex justify-between text-xs">
              <span className="text-mizzy-text-content">大小</span>
              <span className="text-mizzy-text-content">{selectedImage.metadata.fileSize}</span>
            </div>
          )}
          {selectedImage.metadata?.dimensions && (
            <div className="flex justify-between text-xs">
              <span className="text-mizzy-text-content">尺寸</span>
              <span className="text-mizzy-text-content">{selectedImage.metadata.dimensions}</span>
            </div>
          )}
          {selectedImage.metadata?.format && (
            <div className="flex justify-between text-xs">
              <span className="text-mizzy-text-content">格式</span>
              <span className="text-mizzy-text-content">{selectedImage.metadata.format}</span>
            </div>
          )}
        </div>
      </div>

      {/* 拍摄信息 */}
      {(selectedImage.metadata?.camera || selectedImage.metadata?.lens || selectedImage.metadata?.settings) && (
        <div className="mb-space-4">
          <h4 className="text-xs font-medium text-mizzy-text-title mb-space-2">拍摄信息</h4>
          <div className="space-y-space-1">
            {selectedImage.metadata.camera && (
              <div className="flex justify-between text-xs">
                <span className="text-mizzy-text-content">相机</span>
                <span className="text-mizzy-text-content">{selectedImage.metadata.camera}</span>
              </div>
            )}
            {selectedImage.metadata.lens && (
              <div className="flex justify-between text-xs">
                <span className="text-mizzy-text-content">镜头</span>
                <span className="text-mizzy-text-content">{selectedImage.metadata.lens}</span>
              </div>
            )}
            {selectedImage.metadata.settings && (
              <div className="flex justify-between text-xs">
                <span className="text-mizzy-text-content">设置</span>
                <span className="text-mizzy-text-content">{selectedImage.metadata.settings}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 标签 */}
      {selectedImage.tags && selectedImage.tags.length > 0 && (
        <div className="mb-space-4">
          <h4 className="text-xs font-medium text-mizzy-text-title mb-space-2">标签</h4>
          <div className="flex flex-wrap gap-space-1">
            {selectedImage.tags.map((tag, index) => (
              <span
                key={index}
                className="px-space-2 py-space-1 text-xs bg-mizzy-bg-button-special border border-mizzy-border-base rounded-sm text-mizzy-text-content"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="mt-auto pt-space-2 border-t border-mizzy-border-base">
        <div className="flex flex-col gap-space-1">
          <button className="w-full px-space-2 py-space-1 text-xs bg-mizzy-bg-button-special border border-mizzy-border-base rounded-sm hover:border-mizzy-border-highlight text-mizzy-text-content">
            添加到工作台
          </button>
          <button className="w-full px-space-2 py-space-1 text-xs bg-mizzy-bg-button-special border border-mizzy-border-base rounded-sm hover:border-mizzy-border-highlight text-mizzy-text-content">
            编辑标签
          </button>
          <button className="w-full px-space-2 py-space-1 text-xs bg-mizzy-bg-button-special border border-mizzy-border-base rounded-sm hover:border-mizzy-border-highlight text-mizzy-text-content">
            导出图像
          </button>
        </div>
      </div>
    </div>
  );
}
