// src/components/ui/TextButton.tsx
import React from 'react';

interface TextButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export function TextButton({ children, onClick, className }: TextButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`
        h-[20px] px-[2px] flex items-center justify-center 
        bg-transparent border border-transparent rounded-none
        hover:border-[#7F2C25] hover:text-[#9D362F]
        text-[#F7F8F8] text-sm
        transition-all shrink-0
        ${className}
      `}
    >
      {children}
    </button>
  );
}
