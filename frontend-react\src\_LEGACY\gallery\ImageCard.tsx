import type { ImageAsset } from '../../services/mockArchiveService';

interface ImageCardProps {
  asset: ImageAsset;
}

export function ImageCard({ asset }: ImageCardProps) {
  return (
    <div 
      style={{ 
        border: '1px solid var(--color-border-primary)',
        borderRadius: 'var(--border-radius-md)',
        overflow: 'hidden',
        backgroundColor: 'var(--color-surface-secondary)',
        cursor: 'pointer',
        transition: 'border-color 0.2s ease'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.borderColor = 'var(--color-border-focused)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.borderColor = 'var(--color-border-primary)';
      }}
    >
      {/* 图片区域 */}
      <img 
        src={asset.url} 
        alt={asset.name}
        style={{ 
          width: '100%', 
          height: '120px', 
          objectFit: 'cover',
          display: 'block'
        }}
        onError={(e) => {
          // 如果图片加载失败，显示占位符
          e.currentTarget.style.backgroundColor = 'var(--color-surface-input)';
          e.currentTarget.alt = '图片加载失败';
        }}
      />
      
      {/* 标题和内容区域 - 黑色背景 */}
      <div style={{ 
        padding: 'var(--spacing-2) var(--spacing-3)',
        backgroundColor: 'var(--color-surface-primary)',
        borderTop: '1px solid var(--color-border-primary)'
      }}>
        <div style={{ 
          fontSize: 'var(--font-size-body)',
          color: 'var(--color-text-body)',
          fontFamily: 'var(--font-family-sans)',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {asset.name}
        </div>
      </div>
    </div>
  );
}
