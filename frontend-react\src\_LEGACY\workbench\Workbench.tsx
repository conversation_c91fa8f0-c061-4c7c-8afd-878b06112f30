// Workbench.tsx - Phase 5C: 工作台组件
// 多选文件的批量操作界面

import React from 'react';
import { useUIStore } from '@/store';
import { shallow } from 'zustand/shallow';
import './Workbench.css';

export interface WorkbenchProps {
  className?: string;
}

export const Workbench: React.FC<WorkbenchProps> = ({ className = '' }) => {
  const { selectedFiles, clearSelection, currentDirectory, setCurrentDirectory } = useUIStore((state) => ({
    selectedFiles: state.selectedFiles,
    clearSelection: state.clearSelection,
    currentDirectory: state.currentDirectory,
    setCurrentDirectory: state.setCurrentDirectory,
  }), shallow);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleClearSelection = () => {
    clearSelection();
    // console.log('🧹 Phase 5C: 清除所有选择'); // [CLEANED]
  };

  const handleDeleteSelected = async () => {
    if (selectedFiles.length === 0 || isDeleting) return;

    const fileCount = selectedFiles.length;
    const fileNames = selectedFiles.slice(0, 3).map(f => f.name).join(', ');
    const displayNames = selectedFiles.length > 3 ? `${fileNames} 等 ${fileCount} 个文件` : fileNames;

    const confirmMessage = `确定要删除以下文件吗？\n\n${displayNames}\n\n文件将被移动到回收站，可以恢复。`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    setIsDeleting(true);

    try {
      // console.log('🗑️ Phase 5C: 开始删除', fileCount, '个文件'); // [CLEANED]

      const filePaths = selectedFiles.map(file => file.path);
      const result = await window.electronAPI.deleteFiles(filePaths);

      // console.log('🗑️ 删除结果:', result); // [CLEANED]

      // 显示结果
      if (result.failedCount === 0) {
        alert(`成功删除 ${result.successCount} 个文件`);
      } else if (result.successCount === 0) {
        alert(`删除失败：\n${result.failed.map(f => `${f.path}: ${f.error}`).join('\n')}`);
      } else {
        alert(`部分删除成功：\n成功: ${result.successCount} 个\n失败: ${result.failedCount} 个\n\n失败详情:\n${result.failed.map(f => `${f.path}: ${f.error}`).join('\n')}`);
      }

      // 清除选择
      clearSelection();

      // 刷新Gallery - 重新设置当前目录以触发文件列表更新
      if (currentDirectory) {
        // console.log('🔄 Phase 5C: 刷新Gallery'); // [CLEANED]
        setCurrentDirectory(currentDirectory);
      }

    } catch (error) {
      console.error('❌ 删除操作异常:', error);
      alert(`删除操作失败: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // 空状态
  if (selectedFiles.length === 0) {
    return (
      <div className={`workbench ${className}`}>
        <div className="workbench-header">
          <h2>Workbench</h2>
        </div>
        <div className="workbench-content">
          <div className="workbench-empty-state">
            <div className="empty-icon">🛠️</div>
            <h3>Select files in Gallery to perform batch operations</h3>
            <p>Use Ctrl+Click or Shift+Click to select multiple files</p>
          </div>
        </div>
      </div>
    );
  }

  // 活跃状态
  return (
    <div className={`workbench ${className}`}>
      <div className="workbench-header">
        <h2>Workbench</h2>
        <div className="selection-info">
          <span className="selection-count">{selectedFiles.length} files selected</span>
        </div>
      </div>
      <div className="workbench-content">
        <div className="workbench-actions">
          <div className="action-group">
            <h4>File Operations</h4>
            <div className="action-buttons">
              <button
                className="action-btn delete-btn"
                onClick={handleDeleteSelected}
                disabled={isDeleting}
                title={isDeleting ? 'Deleting files...' : `Delete ${selectedFiles.length} selected files`}
              >
                {isDeleting ? '⏳ Deleting...' : '🗑️ Delete Selected'}
              </button>
              <button
                className="action-btn copy-btn"
                disabled
                title="Copy operation (coming soon)"
              >
                📋 Copy
              </button>
              <button
                className="action-btn move-btn"
                disabled
                title="Move operation (coming soon)"
              >
                📁 Move
              </button>
            </div>
          </div>

          <div className="action-group">
            <h4>Selection</h4>
            <div className="action-buttons">
              <button
                className="action-btn clear-btn"
                onClick={handleClearSelection}
              >
                ✨ Clear Selection
              </button>
            </div>
          </div>
        </div>

        <div className="selected-files-preview">
          <h4>Selected Files</h4>
          <div className="file-list">
            {selectedFiles.slice(0, 5).map((file, index) => (
              <div key={file.path} className="file-item">
                <span className="file-icon">📄</span>
                <span className="file-name" title={file.name}>
                  {file.name}
                </span>
              </div>
            ))}
            {selectedFiles.length > 5 && (
              <div className="file-item more-files">
                <span className="file-icon">⋯</span>
                <span className="file-name">
                  and {selectedFiles.length - 5} more files
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Workbench;
