import { useLayout } from '../../context/LayoutContext';
import { CarouselIcon } from '../../assets/icons/CarouselIcon';

export function CollapsedSidebarBar() {
  const { toggleSidebars } = useLayout();

  return (
    <div style={{
      position: 'absolute',
      top: '12px', // 全局上边距
      left: '12px', 
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '4px', // 全局间隔
      zIndex: 1000
    }}>
      {/* 侧边栏恢复按钮 */}
      <div
        onClick={toggleSidebars}
        style={{
          width: '32px',
          height: '32px',
          backgroundColor: '#2A2B2E', // mizzy-bg-button-special
          border: '1px solid #353639', // mizzy-border-base
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#353639'; // mizzy-bg-hover
          e.currentTarget.style.borderColor = '#9D362F'; // mizzy-border-highlight
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = '#2A2B2E';
          e.currentTarget.style.borderColor = '#353639';
        }}
      >
        <CarouselIcon 
          style={{ 
            width: '20px', 
            height: '20px',
            color: '#F7F8F8' // mizzy-icon-primary
          }} 
        />
      </div>
    </div>
  );
}
