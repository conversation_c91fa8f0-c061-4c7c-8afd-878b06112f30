# Mizzy Star - 架构蓝图与指令索引 V1.0

## 🔥 基石行动后的官方架构蓝图

**文档状态**: ✅ 官方版本 - 基于基石行动后的骨架结构  
**最后更新**: 2025年8月3日  
**适用版本**: Mizzy Star V0.3 - 基石骨架版本  

---

## 📋 引言

本文档是 **Mizzy Star** 智能图像数据库项目的唯一官方架构蓝图，基于"凤凰计划第二阶段·基石行动"后的最小化可行骨架 (Minimum Viable Skeleton) 创建。

### 🎯 当前系统状态
- **后端状态**: ✅ 运行中 (`http://localhost:8000`)
- **前端状态**: ✅ 运行中 (`http://localhost:5182`)  
- **Electron状态**: ✅ 可启动桌面应用
- **核心功能**: 仅保留案例列表获取功能
- **复杂功能**: 已归档至 `_LEGACY` 目录

### 🏗️ 架构原则
1. **单一真理来源**: 本文档是所有开发决策的唯一依据
2. **骨架优先**: 当前只保留最核心的功能
3. **渐进重建**: 基于骨架逐步恢复功能
4. **文件映射**: 每个功能都有明确的文件路径对应

---

## 🗺️ 核心功能 -> 文件映射表

### 📁 档案库 (Case Management)

#### 功能 1: 读取已有的档案库信息
- **后端API**: `backend/src/routers/cases.py` (函数: `read_cases_endpoint`)
- **后端CRUD**: `backend/src/crud/case_crud.py` (函数: `get_cases`)
- **前端数据层**: `frontend-react/src/store/useCaseStore.ts` (函数: `fetchCases`) ❌ **已清空**
- **前端API服务**: `frontend-react/src/services/apiService.ts` (函数: `getCases`) ❌ **已清空**

#### 功能 2: 提取档案库信息并加载显示  
- **前端UI组件**: `frontend-react/src/components/CaseList.tsx` ✅ **骨架版本**
- **前端案例项**: `frontend-react/src/components/CaseItem.tsx` ✅ **骨架版本**
- **应用入口**: `frontend-react/src/App.tsx` ✅ **骨架版本**

#### 功能 3: 创建新档案库 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/`)
- **后端CRUD**: `backend/src/crud/case_crud.py` (函数: `create_case`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 4: 编辑档案库信息 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `PUT /cases/{case_id}`)
- **后端CRUD**: `backend/src/crud/case_crud.py` (函数: `update_case`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 📄 文件管理 (File Management)

#### 功能 5: 上传文件到档案库 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/{case_id}/files/upload`)
- **后端CRUD**: `backend/src/crud/file_crud.py` (函数: `create_file_for_case`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 6: 导入本地文件 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/{case_id}/files/import-local`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 7: 批量导入文件 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/{case_id}/files/batch-import`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 8: 查看和下载文件 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `GET /cases/{case_id}/files/{file_id}/view`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 🏷️ 标签系统 (Tag System)

#### 功能 9: 自动标签提取 ❌ **已注释 - 非骨架功能**
- **后端服务**: `backend/src/services/rule_engine.py` (函数: `process_file_with_rules`)
- **后端路由**: `backend/src/routers/tags.py` ❌ **已从main.py注释**
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 10: 手动标签管理 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/tags.py` ❌ **已从main.py注释**
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 🔍 搜索与筛选 (Search & Filter)

#### 功能 11: 基于标签的搜索 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `GET /cases/{case_id}/files/`)
- **前端组件**: `frontend-react/src/_LEGACY/search/` (已归档)

#### 功能 12: 高级筛选功能 ❌ **已注释 - 非骨架功能**
- **前端组件**: `frontend-react/src/_LEGACY/search/` (已归档)

### 🎨 质量控制 (Quality Control)

#### 功能 13: 图像质量分析 ❌ **已注释 - 非骨架功能**
- **后端服务**: `backend/src/analysis/image_quality.py`
- **后端路由**: `backend/src/routers/quality.py` ❌ **已从main.py注释**
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 🗂️ 归档管理 (Archive Management)

#### 功能 14: 回收站功能 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/trash.py` ❌ **已从main.py注释**
- **后端CRUD**: `backend/src/crud/trash_crud.py`
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

---

## 🏗️ 当前骨架架构图

```
Mizzy Star V0.3 - 基石骨架版本
├── 🔥 ACTIVE (骨架功能)
│   ├── backend/src/main.py                    # FastAPI应用入口
│   ├── backend/src/routers/cases.py           # 核心案例API (仅GET)
│   ├── backend/src/crud/case_crud.py          # 案例数据操作
│   ├── frontend-react/src/App.tsx             # 前端应用入口
│   ├── frontend-react/src/components/CaseList.tsx  # 案例列表组件
│   └── frontend-react/src/components/CaseItem.tsx  # 案例项组件
│
└── ❌ LEGACY (已归档功能)
    ├── frontend-react/src/_LEGACY/            # 所有复杂前端组件
    ├── backend/src/routers/cases_*_LEGACY.py  # 复杂后端API备份
    └── [47个其他归档组件...]
```

---

## 🎯 重建路线图

### Phase 1: 核心功能恢复 (P0)
1. **案例创建功能** - 恢复 `POST /cases/` API
2. **基本文件上传** - 恢复 `POST /cases/{case_id}/files/upload` API  
3. **简化状态管理** - 重建 `useCaseStore.ts`
4. **基础API服务** - 重建 `apiService.ts`

### Phase 2: 交互功能 (P1)
1. **案例详情查看** - 恢复 `GET /cases/{case_id}` API
2. **文件列表显示** - 恢复文件展示组件
3. **基础导航** - 重建简化的导航系统

### Phase 3: 高级功能 (P2)
1. **搜索筛选** - 恢复搜索功能
2. **标签系统** - 恢复标签管理
3. **质量控制** - 恢复质量分析

---

## 🚨 开发约束

### 强制规则
1. **蓝图即法律**: 所有开发必须基于本文档
2. **骨架优先**: 新功能必须基于当前骨架结构
3. **渐进式**: 禁止一次性恢复大量功能
4. **测试驱动**: 每个恢复的功能都必须有对应测试

### 文件操作规则
1. **禁止直接修改** `_LEGACY` 目录中的文件
2. **必须基于骨架** 重新实现功能
3. **保持简洁** 避免过度复杂的实现

---

## 📞 联系与更新

**维护者**: Augment Agent  
**更新频率**: 每次重大架构变更后更新  
**版本控制**: 遵循语义化版本控制

---

## 🔧 技术栈映射

### 后端技术栈
- **框架**: FastAPI (Python 3.10+)
- **数据库**: SQLite (主库) + SQLite (案例库)
- **ORM**: SQLAlchemy
- **API文档**: OpenAPI/Swagger
- **核心文件**:
  - `backend/src/main.py` - 应用入口
  - `backend/src/models.py` - 数据模型
  - `backend/src/schemas.py` - API模式
  - `backend/src/database.py` - 数据库配置

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Zustand (骨架版本)
- **HTTP客户端**: TanStack Query + Axios
- **样式**: Tailwind CSS
- **桌面**: Electron
- **核心文件**:
  - `frontend-react/src/App.tsx` - 应用入口
  - `frontend-react/src/main.tsx` - 渲染入口
  - `frontend-react/vite.config.ts` - 构建配置

### 数据流架构
```
用户操作 → React组件 → Zustand Store → API Service → FastAPI → SQLAlchemy → SQLite
    ↓
Electron桌面应用 ← React渲染 ← 状态更新 ← HTTP响应 ← JSON数据 ← 数据库查询
```

---

## 📊 当前系统健康状态

### ✅ 正常运行的组件
- **后端服务**: `GET /api/v1/cases/` 正常响应
- **前端应用**: 案例列表正常显示
- **数据库**: 主数据库连接正常
- **Electron**: 桌面应用可正常启动

### ❌ 已知破坏的功能
- **案例管理**: 创建、编辑、删除功能已注释
- **文件管理**: 上传、导入、查看功能已注释
- **标签系统**: 所有标签相关功能已注释
- **搜索功能**: 搜索和筛选功能已归档
- **质量控制**: 图像质量分析功能已注释
- **归档管理**: 回收站功能已注释

### 🔄 待重建的关键文件
- `frontend-react/src/services/apiService.ts` ❌ **已清空**
- `frontend-react/src/store/useCaseStore.ts` ❌ **已清空**
- `frontend-react/src/store/index.ts` ❌ **已清空**

---

## 🎯 下一步行动计划

### 立即执行 (本周)
1. **重建API服务层** - 创建新的 `apiService.ts`
2. **重建状态管理** - 创建新的 `useCaseStore.ts`
3. **恢复案例创建** - 取消注释 `POST /cases/` API

### 短期目标 (本月)
1. **文件上传功能** - 恢复基础文件上传
2. **案例详情页** - 实现案例详情查看
3. **基础导航** - 重建简化导航系统

### 中期目标 (下月)
1. **搜索功能** - 恢复基础搜索
2. **标签系统** - 恢复核心标签功能
3. **质量控制** - 恢复图像质量分析

---

*本文档是 Mizzy Star 项目的官方架构蓝图，任何与此文档冲突的代码实现都应以本文档为准进行修正。*

**最后验证**: 2025年8月3日 - 基石行动执行完毕，系统骨架运行正常 ✅
