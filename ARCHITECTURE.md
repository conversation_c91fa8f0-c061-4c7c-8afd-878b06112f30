# Mizzy Star - 架构蓝图与指令索引 V1.0

## 🔥 基石行动后的官方架构蓝图

**文档状态**: ✅ 官方版本 - 基于基石行动后的骨架结构  
**最后更新**: 2025年8月3日  
**适用版本**: Mizzy Star V0.3 - 基石骨架版本  

---

## 📋 引言

本文档是 **Mizzy Star** 智能图像数据库项目的唯一官方架构蓝图，基于"凤凰计划第二阶段·基石行动"后的最小化可行骨架 (Minimum Viable Skeleton) 创建。

### 🎯 当前系统状态
- **后端状态**: ✅ 运行中 (`http://localhost:8000`)
- **前端状态**: ✅ 运行中 (`http://localhost:5182`)  
- **Electron状态**: ✅ 可启动桌面应用
- **核心功能**: 仅保留案例列表获取功能
- **复杂功能**: 已归档至 `_LEGACY` 目录

### 🏗️ 架构原则
1. **单一真理来源**: 本文档是所有开发决策的唯一依据
2. **骨架优先**: 当前只保留最核心的功能
3. **渐进重建**: 基于骨架逐步恢复功能
4. **文件映射**: 每个功能都有明确的文件路径对应

---

## 🗺️ 核心功能 -> 文件映射表

### 📁 档案库 (Case Management)

#### 功能 1: 读取已有的档案库信息
- **后端API**: `backend/src/routers/cases.py` (函数: `read_cases_endpoint`)
- **后端CRUD**: `backend/src/crud/case_crud.py` (函数: `get_cases`)
- **前端数据层**: `frontend-react/src/store/useCaseStore.ts` (函数: `fetchCases`) ❌ **已清空**
- **前端API服务**: `frontend-react/src/services/apiService.ts` (函数: `getCases`) ❌ **已清空**

#### 功能 2: 提取档案库信息并加载显示  
- **前端UI组件**: `frontend-react/src/components/CaseList.tsx` ✅ **骨架版本**
- **前端案例项**: `frontend-react/src/components/CaseItem.tsx` ✅ **骨架版本**
- **应用入口**: `frontend-react/src/App.tsx` ✅ **骨架版本**

#### 功能 3: 创建新档案库 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/`)
- **后端CRUD**: `backend/src/crud/case_crud.py` (函数: `create_case`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 4: 编辑档案库信息 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `PUT /cases/{case_id}`)
- **后端CRUD**: `backend/src/crud/case_crud.py` (函数: `update_case`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 📄 文件管理 (File Management)

#### 功能 5: 上传文件到档案库 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/{case_id}/files/upload`)
- **后端CRUD**: `backend/src/crud/file_crud.py` (函数: `create_file_for_case`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 6: 导入本地文件 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/{case_id}/files/import-local`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 7: 批量导入文件 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `POST /cases/{case_id}/files/batch-import`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 8: 查看和下载文件 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `GET /cases/{case_id}/files/{file_id}/view`)
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 🏷️ 标签系统 (Tag System)

#### 功能 9: 自动标签提取 ❌ **已注释 - 非骨架功能**
- **后端服务**: `backend/src/services/rule_engine.py` (函数: `process_file_with_rules`)
- **后端路由**: `backend/src/routers/tags.py` ❌ **已从main.py注释**
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

#### 功能 10: 手动标签管理 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/tags.py` ❌ **已从main.py注释**
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 🔍 搜索与筛选 (Search & Filter)

#### 功能 11: 基于标签的搜索 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/cases.py` (注释: `GET /cases/{case_id}/files/`)
- **前端组件**: `frontend-react/src/_LEGACY/search/` (已归档)

#### 功能 12: 高级筛选功能 ❌ **已注释 - 非骨架功能**
- **前端组件**: `frontend-react/src/_LEGACY/search/` (已归档)

### 🎨 质量控制 (Quality Control)

#### 功能 13: 图像质量分析 ❌ **已注释 - 非骨架功能**
- **后端服务**: `backend/src/analysis/image_quality.py`
- **后端路由**: `backend/src/routers/quality.py` ❌ **已从main.py注释**
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

### 🗂️ 归档管理 (Archive Management)

#### 功能 14: 回收站功能 ❌ **已注释 - 非骨架功能**
- **后端API**: `backend/src/routers/trash.py` ❌ **已从main.py注释**
- **后端CRUD**: `backend/src/crud/trash_crud.py`
- **前端组件**: `frontend-react/src/_LEGACY/` (已归档)

---

## 🏗️ 当前骨架架构图

```
Mizzy Star V0.3 - 基石骨架版本
├── 🔥 ACTIVE (骨架功能)
│   ├── backend/src/main.py                    # FastAPI应用入口
│   ├── backend/src/routers/cases.py           # 核心案例API (仅GET)
│   ├── backend/src/crud/case_crud.py          # 案例数据操作
│   ├── frontend-react/src/App.tsx             # 前端应用入口
│   ├── frontend-react/src/components/CaseList.tsx  # 案例列表组件
│   └── frontend-react/src/components/CaseItem.tsx  # 案例项组件
│
└── ❌ LEGACY (已归档功能)
    ├── frontend-react/src/_LEGACY/            # 所有复杂前端组件
    ├── backend/src/routers/cases_*_LEGACY.py  # 复杂后端API备份
    └── [47个其他归档组件...]
```

---

## 🎯 重建路线图

### Phase 1: 核心功能恢复 (P0)
1. **案例创建功能** - 恢复 `POST /cases/` API
2. **基本文件上传** - 恢复 `POST /cases/{case_id}/files/upload` API  
3. **简化状态管理** - 重建 `useCaseStore.ts`
4. **基础API服务** - 重建 `apiService.ts`

### Phase 2: 交互功能 (P1)
1. **案例详情查看** - 恢复 `GET /cases/{case_id}` API
2. **文件列表显示** - 恢复文件展示组件
3. **基础导航** - 重建简化的导航系统

### Phase 3: 高级功能 (P2)
1. **搜索筛选** - 恢复搜索功能
2. **标签系统** - 恢复标签管理
3. **质量控制** - 恢复质量分析

---

## 🚨 开发约束

### 强制规则
1. **蓝图即法律**: 所有开发必须基于本文档
2. **骨架优先**: 新功能必须基于当前骨架结构
3. **渐进式**: 禁止一次性恢复大量功能
4. **测试驱动**: 每个恢复的功能都必须有对应测试

### 文件操作规则
1. **禁止直接修改** `_LEGACY` 目录中的文件
2. **必须基于骨架** 重新实现功能
3. **保持简洁** 避免过度复杂的实现

---

## 📞 联系与更新

**维护者**: Augment Agent  
**更新频率**: 每次重大架构变更后更新  
**版本控制**: 遵循语义化版本控制

---

## 🎯 14个核心功能实现路径

### 📋 功能实现优先级与技术路径

本章节详细描述了Mizzy Star项目的14个核心功能，每个功能都包含具体的实现路径、技术要求和文件映射。

---

### 🔵 Phase 1: 基础数据层 (P0 - 立即实现)

#### ① 读取已有的档案库信息
**状态**: ✅ **已实现 - 骨架版本**
- **后端实现**: `backend/src/routers/cases.py::read_cases_endpoint()`
- **数据库操作**: `backend/src/crud/case_crud.py::get_cases()`
- **API端点**: `GET /api/v1/cases/`
- **数据模型**: `backend/src/models.py::Case`
- **响应格式**: `backend/src/schemas.py::Case`
- **技术要求**: SQLAlchemy查询，分页支持(skip/limit)

#### ② 提取档案库信息并加载显示
**状态**: ✅ **已实现 - 骨架版本**
- **前端组件**: `frontend-react/src/components/CaseList.tsx`
- **案例项组件**: `frontend-react/src/components/CaseItem.tsx`
- **状态管理**: `frontend-react/src/store/useCaseStore.ts` ❌ **需重建**
- **API调用**: `frontend-react/src/services/apiService.ts` ❌ **需重建**
- **技术要求**: React组件渲染，加载/错误/空状态处理

#### ③ 新建档案库并将信息录入数据库
**状态**: ❌ **已注释 - 需恢复**
- **后端API**: `backend/src/routers/cases.py` (需取消注释 `POST /cases/`)
- **数据库操作**: `backend/src/crud/case_crud.py::create_case()`
- **请求模型**: `backend/src/schemas.py::CaseCreate`
- **前端表单**: 需新建案例创建表单组件
- **状态更新**: 创建后自动刷新案例列表
- **技术要求**: 表单验证，数据库事务，错误处理

#### ④ 刷新档案库显示并显示新增的档案库信息
**状态**: ❌ **需实现**
- **前端逻辑**: 创建案例后触发列表刷新
- **状态管理**: 更新 `useCaseStore` 中的案例列表
- **UI反馈**: 显示创建成功提示
- **技术要求**: 状态同步，乐观更新，错误回滚

#### ⑤ 自定义编辑字段（档案库标题和简介）
**状态**: ❌ **已注释 - 需恢复**
- **后端API**: `backend/src/routers/cases.py` (需取消注释 `PUT /cases/{case_id}`)
- **数据库操作**: `backend/src/crud/case_crud.py::update_case()`
- **请求模型**: `backend/src/schemas.py::CaseUpdate`
- **前端组件**: 需新建案例编辑表单组件
- **技术要求**: 内联编辑，字段验证，实时保存

#### ⑥ 通过标记档案库"临时删除"实现档案库的软删除
**状态**: ❌ **已注释 - 需恢复**
- **后端API**: `backend/src/routers/cases.py` (需取消注释 `DELETE /cases/{case_id}`)
- **数据库操作**: `backend/src/crud/case_crud.py::delete_case()`
- **软删除逻辑**: 设置 `deleted_at` 字段而非物理删除
- **回收站功能**: `backend/src/routers/trash.py` ❌ **已从main.py注释**
- **技术要求**: 软删除标记，回收站管理，恢复功能

---

### 🟡 Phase 2: 文件管理层 (P1 - 短期实现)

#### ⑦ 上传图片并记录真实路径到数据库
**状态**: ❌ **已注释 - 需恢复**
- **后端API**: `backend/src/routers/cases.py` (需取消注释 `POST /cases/{case_id}/files/upload`)
- **文件处理**: 多部分表单上传，文件类型验证
- **路径存储**: 存储绝对路径到 `File.file_path` 字段
- **数据库操作**: `backend/src/crud/file_crud.py::create_file_for_case()`
- **前端组件**: 需新建文件上传组件
- **技术要求**: 文件上传进度，类型限制，大小限制

#### ⑧ 提取图片EXIF元数据
**状态**: ❌ **已注释 - 需恢复**
- **后端服务**: `backend/src/services/exif_extractor.py`
- **规则引擎**: `backend/src/services/rule_engine.py::process_file_with_rules()`
- **元数据存储**: 存储到 `File.tags` JSON字段
- **支持字段**: 相机型号、拍摄参数、GPS信息、拍摄时间
- **技术要求**: PIL/Pillow库，EXIF标签解析，错误处理

#### ⑨ 切换档案库并将档案库信息映射到工作区
**状态**: ❌ **需实现**
- **前端路由**: 实现案例详情页路由 `/cases/{case_id}`
- **状态管理**: 当前活跃案例状态管理
- **工作区组件**: 需新建工作区布局组件
- **数据加载**: 切换时加载案例详情和文件列表
- **技术要求**: 路由参数，状态持久化，懒加载

---

### 🟢 Phase 3: 展示层 (P2 - 中期实现)

#### ⑩ 在画廊图片卡片中，从真实路径加载渲染图片
**状态**: ❌ **已注释 - 需恢复**
- **后端API**: `backend/src/routers/cases.py` (需取消注释 `GET /cases/{case_id}/files/{file_id}/view`)
- **缩略图API**: `GET /cases/{case_id}/files/{file_id}/thumbnail`
- **前端组件**: 需新建图片画廊组件
- **图片加载**: 懒加载，错误处理，占位符
- **技术要求**: 图片优化，缓存策略，响应式布局

#### ⑪ 在画廊图片卡片中，从数据库显示图片信息
**状态**: ❌ **已注释 - 需恢复**
- **后端API**: `GET /cases/{case_id}/files/` (需取消注释)
- **文件信息**: 文件名、大小、尺寸、拍摄时间
- **前端组件**: 图片信息覆盖层或侧边栏
- **数据绑定**: 文件元数据到UI组件的映射
- **技术要求**: 数据格式化，国际化，响应式设计

#### ⑫ 将图片信息从数据库映射到标签列表
**状态**: ❌ **已注释 - 需恢复**
- **数据源**: `File.tags` JSON字段中的标签数据
- **标签分类**: metadata(EXIF)、user(用户)、ai(AI生成)
- **前端组件**: 需新建标签列表组件
- **数据处理**: JSON解析，标签分组，搜索过滤
- **技术要求**: 标签渲染，分类显示，搜索功能

#### ⑬ 渲染独立可操作的标签卡片
**状态**: ❌ **已注释 - 需恢复**
- **前端组件**: 需新建标签卡片组件
- **交互功能**: 点击筛选，编辑标签，删除标签
- **标签操作**: 添加用户标签，修改标签值
- **后端支持**: `backend/src/routers/tags.py` ❌ **已从main.py注释**
- **技术要求**: 标签CRUD，实时更新，批量操作

#### ⑭ 将图片信息从数据库映射到详情栏
**状态**: ❌ **需实现**
- **前端组件**: 需新建图片详情面板组件
- **信息展示**: 完整EXIF信息，文件属性，标签详情
- **布局设计**: 可折叠的详情面板，分组显示
- **数据格式**: 友好的数据格式化和单位转换
- **技术要求**: 详情面板，数据格式化，可访问性

---

## 📋 《状态管理白皮书》：React Query 与 Zustand 的黄金准则

为了保证架构的绝对清晰和稳固，我们在此立下两条黄金准则：

### 🥇 服务器状态准则 (The Server State Rule)

任何源自服务器、需要与服务器同步的数据，其整个生命周期（获取、缓存、更新、废止）必须 **100%** 由 **React Query** (我们称之为 "🚀 R.Q.") 管理。

这包括所有 `GET`, `POST`, `PUT`, `PATCH`, `DELETE` 请求。

### 🥈 客户端状态准则 (The Client State Rule)

任何纯粹由用户在界面上产生的、与服务器无直接同步关系的临时状态，应由 **Zustand** (我们称之为 "🐻 Zustand") 管理。

这是"昙花一现"的状态，它描述了"当前UI是什么样"，而不是"数据库里有什么"。

### 📊 14项基本功能的职责划分详解

遵照以上黄金准则，我们可以为您的14项基本功能制定一份清晰的职责划分蓝图。

| 功能序号 & 类别 | 功能描述 | 主导技术 & 职责划分 |
|----------------|----------|-------------------|
| **读取数据 (Queries)** | ① 读取已有的档案库信息<br>② 提取并加载显示 | **🚀 React Query useQuery**<br>职责：这是最经典的应用场景。`useQuery(['archives'], fetchArchives)` 将负责获取档案库列表，并自动管理 `isLoading`, `error`, 缓存和后台刷新。组件只需要消费这个hook返回的数据即可。 |
| **写入数据 (Mutations)** | ③ 新建档案库<br>④ 刷新显示新增信息 | **🚀 React Query useMutation**<br>职责：使用 `useMutation(createArchive)` 来处理新建操作。在其 `onSuccess` 回调中，调用 `queryClient.invalidateQueries(['archives'])` 来自动触发①和②的刷新。整个"创建->刷新"的闭环，由 R.Q. 优雅地完成。 |
| **更新数据 (Mutations)** | ⑤ 自定义编辑字段<br>⑥ 软删除档案库 | **🚀 React Query useMutation**<br>职责：与③和④完全相同，为"编辑"和"软删除"分别创建 `useMutation`。操作成功后，同样通过 `invalidateQueries` 来刷新列表，保证UI与服务器数据最终一致。 |
| **写入数据 (Mutations)** | ⑦ 上传图片... | **🚀 React Query useMutation**<br>职责：上传是一个典型的异步修改操作。`useMutation(uploadImage)` 将处理文件上传，并能精确管理上传中、上传成功、上传失败的状态。 |
| **读取数据 (Queries)** | ⑧ 提取图片exif元数据<br>⑩ 从路径加载图片<br>⑪ 从数据库显示信息 | **🚀 React Query useQuery**<br>职责：当一个档案库被激活后，我们会使用一个带参数的 `useQuery`，如 `useQuery(['images', activeArchiveId], fetchImages)` 来获取该库下所有图片的信息（包括路径、EXIF、描述等）。R.Q. 会为每个档案库的图片列表做独立的缓存。 |
| **读取数据 (Queries)** | ⑫ 映射到标签列表<br>⑭ 映射到详情栏<br>⑬ 渲染标签卡片 | **🚀 React Query useQuery + 🐻 Zustand (协作)**<br>职责：这是两者协作的典范。图片的元数据（包括tag）由 R.Q. 的`useQuery(['images', ...])`获取。当用户在画廊中点击某张图片时，这张卡的 ID (`selectedImageId`) 将被存入 Zustand。`<TagList>` 和 `<DetailPanel>` 等组件则订阅 Zustand 中的 `selectedImageId`，然后从 R.Q. 提供的总数据中，找出并显示这张"已选中"图片的信息。 |
| **核心交互 (协作)** | ⑨ 切换档案库... | **🐻 Zustand + 🚀 React Query useQuery (协作)**<br>职责：这是整个应用的核心交互，也是两者协作的最佳范例。<br>1. `useQuery` 获取所有档案库列表并显示。<br>2. 用户点击 "档案库B"。<br>3. `OnClick`事件调用 Zustand 的 `setActiveArchive('B')`。<br>4. 工作区中的组件（如画廊）正在订阅 Zustand 里的 `activeArchiveId`。<br>5. 当 `activeArchiveId` 变化时，画廊组件的 `useQuery` (例如 `useQuery(['images', 'B'], ...)` 会被自动触发，从而获取并显示"档案库B"的图片。<br><br>Zustand 在此扮演了"指挥官"的角色，它发出"切换目标"的命令；而 React Query 是"前线部队"，根据命令去获取相应的数据。 |

---

## �️ 实现策略与技术细节

### 📊 功能依赖关系图

```
① 读取档案库 → ② 显示档案库列表
                    ↓
③ 创建档案库 → ④ 刷新显示 → ⑤ 编辑档案库 → ⑥ 软删除
                    ↓
⑨ 切换档案库 → ⑦ 上传图片 → ⑧ 提取EXIF
                    ↓
⑩ 画廊渲染 → ⑪ 显示图片信息 → ⑫ 标签映射 → ⑬ 标签卡片 → ⑭ 详情面板
```

### 🔄 数据流设计

#### 案例管理数据流
```
用户操作 → CaseList组件 → useCaseStore → apiService → FastAPI → case_crud → SQLite主库
    ↓
UI更新 ← 状态更新 ← HTTP响应 ← JSON数据 ← 数据库查询结果
```

#### 文件管理数据流
```
文件上传 → FileUpload组件 → FormData → FastAPI → file_crud → SQLite案例库
    ↓                                        ↓
缩略图生成 ← EXIF提取 ← 规则引擎 ← 文件处理 ← 文件存储
    ↓
Gallery组件 ← 图片URL ← 文件服务API
```

### 🎯 关键技术实现点

#### 状态管理架构
```typescript
// useCaseStore.ts 重建结构
interface CaseStore {
  // 基础状态
  allCases: Case[]
  currentCase: Case | null
  isLoading: boolean
  error: string | null

  // 基础操作
  fetchCases: () => Promise<void>
  createCase: (data: CaseCreate) => Promise<Case>
  updateCase: (id: number, data: CaseUpdate) => Promise<Case>
  deleteCase: (id: number) => Promise<void>
  setCurrentCase: (case: Case) => void

  // 文件相关
  uploadFile: (caseId: number, file: File) => Promise<FileInfo>
  fetchFiles: (caseId: number) => Promise<FileInfo[]>
}
```

#### API服务架构
```typescript
// apiService.ts 重建结构
export const apiService = {
  // 案例管理
  cases: {
    getAll: (params?: PaginationParams) => Promise<Case[]>
    getById: (id: number) => Promise<Case>
    create: (data: CaseCreate) => Promise<Case>
    update: (id: number, data: CaseUpdate) => Promise<Case>
    delete: (id: number) => Promise<void>
  },

  // 文件管理
  files: {
    upload: (caseId: number, file: File) => Promise<FileInfo>
    getList: (caseId: number) => Promise<FileInfo[]>
    getById: (caseId: number, fileId: number) => Promise<FileInfo>
    getThumbnail: (caseId: number, fileId: number) => string
    getView: (caseId: number, fileId: number) => string
  }
}
```

### 🔧 组件架构设计

#### 核心组件层次结构
```
App.tsx
├── Router
│   ├── CaseListPage
│   │   ├── CaseList.tsx ✅ (已实现)
│   │   └── CaseItem.tsx ✅ (已实现)
│   │
│   └── CaseDetailPage (需新建)
│       ├── CaseHeader (案例信息)
│       ├── FileUpload (文件上传)
│       ├── Gallery (图片画廊)
│       │   ├── ImageCard (图片卡片)
│       │   └── ImageModal (图片详情)
│       ├── TagPanel (标签面板)
│       │   └── TagCard (标签卡片)
│       └── DetailPanel (详情面板)
```

---

## �🔧 技术栈映射

### 后端技术栈
- **框架**: FastAPI (Python 3.10+)
- **数据库**: SQLite (主库) + SQLite (案例库)
- **ORM**: SQLAlchemy
- **API文档**: OpenAPI/Swagger
- **核心文件**:
  - `backend/src/main.py` - 应用入口
  - `backend/src/models.py` - 数据模型
  - `backend/src/schemas.py` - API模式
  - `backend/src/database.py` - 数据库配置

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Zustand (骨架版本)
- **HTTP客户端**: TanStack Query + Axios
- **样式**: Tailwind CSS
- **桌面**: Electron
- **核心文件**:
  - `frontend-react/src/App.tsx` - 应用入口
  - `frontend-react/src/main.tsx` - 渲染入口
  - `frontend-react/vite.config.ts` - 构建配置

### 数据流架构
```
用户操作 → React组件 → Zustand Store → API Service → FastAPI → SQLAlchemy → SQLite
    ↓
Electron桌面应用 ← React渲染 ← 状态更新 ← HTTP响应 ← JSON数据 ← 数据库查询
```

---

## 📊 当前系统健康状态

### ✅ 正常运行的组件
- **后端服务**: `GET /api/v1/cases/` 正常响应
- **前端应用**: 案例列表正常显示
- **数据库**: 主数据库连接正常
- **Electron**: 桌面应用可正常启动

### ❌ 已知破坏的功能
- **案例管理**: 创建、编辑、删除功能已注释
- **文件管理**: 上传、导入、查看功能已注释
- **标签系统**: 所有标签相关功能已注释
- **搜索功能**: 搜索和筛选功能已归档
- **质量控制**: 图像质量分析功能已注释
- **归档管理**: 回收站功能已注释

### 🔄 待重建的关键文件
- `frontend-react/src/services/apiService.ts` ❌ **已清空**
- `frontend-react/src/store/useCaseStore.ts` ❌ **已清空**
- `frontend-react/src/store/index.ts` ❌ **已清空**

---

## 🎯 下一步行动计划

### 立即执行 (本周)
1. **重建API服务层** - 创建新的 `apiService.ts`
2. **重建状态管理** - 创建新的 `useCaseStore.ts`
3. **恢复案例创建** - 取消注释 `POST /cases/` API

### 短期目标 (本月)
1. **文件上传功能** - 恢复基础文件上传
2. **案例详情页** - 实现案例详情查看
3. **基础导航** - 重建简化导航系统

### 中期目标 (下月)
1. **搜索功能** - 恢复基础搜索
2. **标签系统** - 恢复核心标签功能
3. **质量控制** - 恢复图像质量分析

---

## 📅 14个功能实现时间表

### Week 1: 基础设施重建
- **Day 1-2**: ③ 新建档案库 + ④ 刷新显示
- **Day 3-4**: 重建 `apiService.ts` 和 `useCaseStore.ts`
- **Day 5-7**: ⑤ 编辑档案库 + ⑥ 软删除功能

### Week 2: 文件管理核心
- **Day 1-3**: ⑦ 上传图片功能
- **Day 4-5**: ⑧ EXIF元数据提取
- **Day 6-7**: ⑨ 切换档案库和工作区映射

### Week 3: 展示层实现
- **Day 1-3**: ⑩ 画廊图片渲染
- **Day 4-5**: ⑪ 图片信息显示
- **Day 6-7**: ⑫ 标签列表映射

### Week 4: 交互层完善
- **Day 1-3**: ⑬ 可操作标签卡片
- **Day 4-5**: ⑭ 详情面板实现
- **Day 6-7**: 集成测试和优化

---

## ✅ 功能验收标准

### Phase 1 验收标准 (功能①-⑥)
- [ ] 案例列表正确显示所有案例
- [ ] 可以成功创建新案例
- [ ] 创建后列表自动刷新
- [ ] 可以编辑案例标题和描述
- [ ] 软删除功能正常工作
- [ ] 删除的案例不在主列表显示

### Phase 2 验收标准 (功能⑦-⑨)
- [ ] 支持多种图片格式上传
- [ ] 文件路径正确存储到数据库
- [ ] EXIF数据完整提取并存储
- [ ] 切换案例时工作区正确更新
- [ ] 文件列表按案例正确分组

### Phase 3 验收标准 (功能⑩-⑭)
- [ ] 图片在画廊中正确渲染
- [ ] 缩略图加载性能良好
- [ ] 图片信息完整显示
- [ ] 标签按类型正确分组
- [ ] 标签卡片支持基本操作
- [ ] 详情面板信息完整准确

---

*本文档是 Mizzy Star 项目的官方架构蓝图，任何与此文档冲突的代码实现都应以本文档为准进行修正。*

**最后验证**: 2025年8月3日 - 基石行动执行完毕，系统骨架运行正常 ✅
