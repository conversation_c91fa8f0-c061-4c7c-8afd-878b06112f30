// src/components/toolbars/NavigationToolbarSet.tsx
import React from 'react';
// 根据蓝图导入所有需要的SVG图标
import { GridSquaresIcon } from '../../assets/icons/GridSquaresIcon';
import { PaperFoldedIcon } from '../../assets/icons/PaperFoldedIcon';
import { CarouselIcon } from '../../assets/icons/CarouselIcon';
import { FolderClosedIcon } from '../../assets/icons/FolderClosedIcon';
import { HeartRateIcon } from '../../assets/icons/HeartRateIcon';


import { IconButton } from '../ui/IconButton';
import { TextButton } from '../ui/TextButton';
import { SearchInput } from '../ui/SearchInput';
import { useLayout } from '../../context/LayoutContext';

// 创建一个简单的 Toolbar 组件来统一布局
function Toolbar({ children, justify = 'justify-between' }: { children: React.ReactNode, justify?: string }) {
    // 严格按照蓝图：高度16px，间距4px
    return (
        <div className={`w-full h-[16px] flex items-center ${justify} mb-[4px]`}>
            {children}
        </div>
    );
}

export function NavigationToolbarSet() {
    const { openArchiveModal, toggleSidebars } = useLayout();

    return (
        <div className="flex flex-col">
            {/* 导航栏工具栏第一行 */}
            <Toolbar>
                {/* 左对齐：设置按钮 */}
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={GridSquaresIcon} tooltip="设置" />
                </div>
                {/* 右对齐：切换档案库 + 面板展开/折叠按钮 */}
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={PaperFoldedIcon} tooltip="切换档案库" />
                    <IconButton icon={CarouselIcon} tooltip="面板展开/折叠" onClick={toggleSidebars} />
                </div>
            </Toolbar>

            {/* 导航栏工具栏第二行 */}
            <Toolbar justify="justify-start">
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={FolderClosedIcon} tooltip="管理档案库" onClick={openArchiveModal} />
                    <TextButton>档案库名称</TextButton>
                </div>
            </Toolbar>

            {/* 导航栏工具栏第三行 */}
            <Toolbar justify="justify-end">
                <div className="flex items-center gap-[4px]">
                    <IconButton icon={HeartRateIcon} tooltip="筛选器" />
                    <SearchInput placeholder="搜索标签" />
                </div>
            </Toolbar>



            {/* 标签列表区域 - 暂时注释掉 */}
            <div className="flex-1 overflow-y-auto mt-space-4">
                {/*
                <TagSection
                    title="标签看板"
                    tags={[
                        { name: '重要', count: 12 },
                        { name: '精选', count: 8 },
                        { name: '待处理', count: 5 }
                    ]}
                />

                <TagSection
                    title="规则标签"
                    tags={[
                        { name: '春季拍摄', count: 25 },
                        { name: '人像', count: 18 },
                        { name: '风景', count: 32 }
                    ]}
                />

                <TagSection
                    title="元数据"
                    tags={[
                        { name: 'JPEG', count: 45 },
                        { name: 'RAW', count: 23 },
                        { name: '高分辨率', count: 67 }
                    ]}
                />
                */}
            </div>
        </div>
    );
}
