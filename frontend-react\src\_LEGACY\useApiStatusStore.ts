// useApiStatusStore.ts - Phase 8A: API状态监控和速率限制管理
// 管理AI服务的健康状态、速率限制和错误信息

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import type { AiError, RateLimitInfo } from '@/lib/aiClient';

// ============================================================================
// 类型定义
// ============================================================================

export interface ApiStatusState {
  // 速率限制状态
  suggestionLimitRemaining: number;
  queryLimitRemaining: number;
  limitResetTime: number | null;
  isSuggestionServiceThrottled: boolean;
  isQueryServiceThrottled: boolean;

  // 错误状态
  lastAiError: AiError | null;
  errorCount: number;
  lastErrorTime: number | null;

  // 服务健康状态
  serviceHealth: 'healthy' | 'degraded' | 'unavailable';
  lastHealthCheck: number | null;
  availableServices: string[];

  // 性能统计
  performanceMetrics: {
    avgResponseTime: number;
    successRate: number;
    totalRequests: number;
    failedRequests: number;
  };

  // 用户体验状态
  showRateLimitWarning: boolean;
  showServiceUnavailableMessage: boolean;
}

export interface ApiStatusActions {
  // 速率限制管理
  updateRateLimit: (info: RateLimitInfo) => void;
  checkThrottleStatus: () => { suggestion: boolean; query: boolean };
  getRemainingTime: () => number;

  // 错误管理
  recordAiError: (error: AiError) => void;
  clearLastError: () => void;
  shouldRetryRequest: (errorCode: string) => boolean;

  // 服务健康管理
  updateServiceHealth: (status: 'healthy' | 'degraded' | 'unavailable') => void;
  updateAvailableServices: (services: string[]) => void;
  performHealthCheck: () => Promise<void>;

  // 性能统计
  recordRequest: (success: boolean, responseTime: number) => void;
  getPerformanceReport: () => string;

  // UI状态管理
  dismissRateLimitWarning: () => void;
  dismissServiceMessage: () => void;

  // 重置和清理
  resetApiStatus: () => void;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: ApiStatusState = {
  // 速率限制状态
  suggestionLimitRemaining: 100,
  queryLimitRemaining: 200,
  limitResetTime: null,
  isSuggestionServiceThrottled: false,
  isQueryServiceThrottled: false,

  // 错误状态
  lastAiError: null,
  errorCount: 0,
  lastErrorTime: null,

  // 服务健康状态
  serviceHealth: 'healthy',
  lastHealthCheck: null,
  availableServices: [],

  // 性能统计
  performanceMetrics: {
    avgResponseTime: 0,
    successRate: 1.0,
    totalRequests: 0,
    failedRequests: 0,
  },

  // 用户体验状态
  showRateLimitWarning: false,
  showServiceUnavailableMessage: false,
};

// ============================================================================
// 常量
// ============================================================================

const HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟
const ERROR_THRESHOLD = 5; // 连续错误阈值
const PERFORMANCE_WINDOW = 100; // 性能统计窗口大小

// ============================================================================
// Store实现
// ============================================================================

export const useApiStatusStore = create<ApiStatusState & ApiStatusActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // ========================================
      // 速率限制管理实现
      // ========================================

      updateRateLimit: (info: RateLimitInfo) => {
        // console.log(`📊 Phase 8A: 更新速率限制信息 - ${info.type}: ${info.remaining}/${info.limit}`); // [CLEANED]

        set((state) => {
          const updates: Partial<ApiStatusState> = {
            limitResetTime: info.reset,
          };

          // 根据限制类型更新相应字段
          if (info.type.includes('tag') || info.type.includes('suggestion')) {
            updates.suggestionLimitRemaining = info.remaining;
            updates.isSuggestionServiceThrottled = info.remaining === 0;
          } else if (info.type.includes('query') || info.type.includes('parse')) {
            updates.queryLimitRemaining = info.remaining;
            updates.isQueryServiceThrottled = info.remaining === 0;
          }

          // 显示速率限制警告
          if (info.remaining < 10) {
            updates.showRateLimitWarning = true;
          }

          return { ...state, ...updates };
        });
      },

      checkThrottleStatus: () => {
        const state = get();
        const now = Date.now();

        // 检查重置时间是否已过
        if (state.limitResetTime && now > state.limitResetTime) {
          set({
            isSuggestionServiceThrottled: false,
            isQueryServiceThrottled: false,
            limitResetTime: null,
            showRateLimitWarning: false,
          });

          return { suggestion: false, query: false };
        }

        return {
          suggestion: state.isSuggestionServiceThrottled,
          query: state.isQueryServiceThrottled,
        };
      },

      getRemainingTime: () => {
        const state = get();
        if (!state.limitResetTime) return 0;

        const remaining = Math.max(0, state.limitResetTime - Date.now());
        return Math.ceil(remaining / 1000); // 返回秒数
      },

      // ========================================
      // 错误管理实现
      // ========================================

      recordAiError: (error: AiError) => {
        console.error(`❌ Phase 8A: 记录AI错误 - ${error.error_code}: ${error.message}`);

        set((state) => {
          const newErrorCount = state.errorCount + 1;
          const newFailedRequests = state.performanceMetrics.failedRequests + 1;
          const totalRequests = state.performanceMetrics.totalRequests + 1;

          // 更新服务健康状态 - 修复降级逻辑
          let newServiceHealth = state.serviceHealth;

          // 根据错误数量调整服务健康状态
          if (newErrorCount >= ERROR_THRESHOLD) {
            newServiceHealth = 'unavailable';
          } else if (newErrorCount >= Math.ceil(ERROR_THRESHOLD / 2)) {
            newServiceHealth = 'degraded';
          }

          // 特定错误码的处理会覆盖上述逻辑
          if (error.error_code.startsWith('AI50')) {
            newServiceHealth = 'unavailable';
          }

          return {
            ...state,
            lastAiError: error,
            errorCount: newErrorCount,
            lastErrorTime: Date.now(),
            serviceHealth: newServiceHealth,
            showServiceUnavailableMessage: newServiceHealth === 'unavailable',
            performanceMetrics: {
              ...state.performanceMetrics,
              failedRequests: newFailedRequests,
              totalRequests: totalRequests,
              successRate: (totalRequests - newFailedRequests) / totalRequests,
            },
          };
        });
      },

      clearLastError: () => {
        set({ lastAiError: null });
      },

      shouldRetryRequest: (errorCode: string) => {
        const retryableErrors = ['AI501', 'AI503', 'AI504', 'NET001'];
        return retryableErrors.includes(errorCode);
      },

      // ========================================
      // 服务健康管理实现
      // ========================================

      updateServiceHealth: (status: 'healthy' | 'degraded' | 'unavailable') => {
        // console.log(`🏥 Phase 8A: 更新服务健康状态: ${status}`); // [CLEANED]

        set((state) => ({
          ...state,
          serviceHealth: status,
          lastHealthCheck: Date.now(),
          showServiceUnavailableMessage: status === 'unavailable',
          // 健康状态恢复时重置错误计数
          errorCount: status === 'healthy' ? 0 : state.errorCount,
        }));
      },

      updateAvailableServices: (services: string[]) => {
        set({ availableServices: services });
      },

      performHealthCheck: async () => {
        try {
          // 这里会在实际集成时调用aiClient.healthCheck()
          // 目前先模拟健康检查
          // console.log('🔍 Phase 8A: 执行AI服务健康检查'); // [CLEANED]

          const mockServices = ['tag-suggestion', 'query-parsing'];

          set((state) => ({
            ...state,
            serviceHealth: 'healthy',
            lastHealthCheck: Date.now(),
            availableServices: mockServices,
            showServiceUnavailableMessage: false,
          }));

        } catch (error) {
          console.error('❌ Phase 8A: 健康检查失败:', error);

          set((state) => ({
            ...state,
            serviceHealth: 'unavailable',
            lastHealthCheck: Date.now(),
            showServiceUnavailableMessage: true,
          }));
        }
      },

      // ========================================
      // 性能统计实现
      // ========================================

      recordRequest: (success: boolean, responseTime: number) => {
        set((state) => {
          const totalRequests = state.performanceMetrics.totalRequests + 1;
          const failedRequests = success
            ? state.performanceMetrics.failedRequests
            : state.performanceMetrics.failedRequests + 1;

          // 计算平均响应时间（滑动窗口）
          const currentAvg = state.performanceMetrics.avgResponseTime;
          const newAvg = totalRequests === 1
            ? responseTime
            : (currentAvg * 0.9) + (responseTime * 0.1);

          return {
            ...state,
            performanceMetrics: {
              avgResponseTime: newAvg,
              successRate: (totalRequests - failedRequests) / totalRequests,
              totalRequests,
              failedRequests,
            },
          };
        });
      },

      getPerformanceReport: () => {
        const metrics = get().performanceMetrics;
        return `性能报告: 平均响应时间 ${metrics.avgResponseTime.toFixed(0)}ms, ` +
               `成功率 ${(metrics.successRate * 100).toFixed(1)}%, ` +
               `总请求数 ${metrics.totalRequests}`;
      },

      // ========================================
      // UI状态管理实现
      // ========================================

      dismissRateLimitWarning: () => {
        set({ showRateLimitWarning: false });
      },

      dismissServiceMessage: () => {
        set({ showServiceUnavailableMessage: false });
      },

      // ========================================
      // 重置和清理实现
      // ========================================

      resetApiStatus: () => {
        set(initialState);
      },
    }),
    { name: 'ApiStatusStore' }
  )
);

// ============================================================================
// 选择器钩子 - 简化版本，避免无限循环
// ============================================================================

export const useRateLimitStatus = () => {
  const suggestionRemaining = useApiStatusStore((state) => state.suggestionLimitRemaining);
  const queryRemaining = useApiStatusStore((state) => state.queryLimitRemaining);
  const isSuggestionThrottled = useApiStatusStore((state) => state.isSuggestionServiceThrottled);
  const isQueryThrottled = useApiStatusStore((state) => state.isQueryServiceThrottled);
  const resetTime = useApiStatusStore((state) => state.limitResetTime);
  const showWarning = useApiStatusStore((state) => state.showRateLimitWarning);
  const getRemainingTime = useApiStatusStore((state) => state.getRemainingTime);
  const dismissWarning = useApiStatusStore((state) => state.dismissRateLimitWarning);

  return {
    suggestionRemaining,
    queryRemaining,
    isSuggestionThrottled,
    isQueryThrottled,
    resetTime,
    showWarning,
    getRemainingTime,
    dismissWarning,
  };
};

export const useServiceHealth = () => {
  const health = useApiStatusStore((state) => state.serviceHealth);
  const availableServices = useApiStatusStore((state) => state.availableServices);
  const lastCheck = useApiStatusStore((state) => state.lastHealthCheck);
  const showMessage = useApiStatusStore((state) => state.showServiceUnavailableMessage);
  const performHealthCheck = useApiStatusStore((state) => state.performHealthCheck);
  const dismissMessage = useApiStatusStore((state) => state.dismissServiceMessage);

  return {
    health,
    availableServices,
    lastCheck,
    showMessage,
    performHealthCheck,
    dismissMessage,
  };
};

export const useAiErrors = () => {
  const lastError = useApiStatusStore((state) => state.lastAiError);
  const errorCount = useApiStatusStore((state) => state.errorCount);
  const shouldRetry = useApiStatusStore((state) => state.shouldRetryRequest);
  const clearError = useApiStatusStore((state) => state.clearLastError);

  return {
    lastError,
    errorCount,
    shouldRetry,
    clearError,
  };
};

export const usePerformanceMetrics = () => {
  const metrics = useApiStatusStore((state) => state.performanceMetrics);
  const getReport = useApiStatusStore((state) => state.getPerformanceReport);
  const recordRequest = useApiStatusStore((state) => state.recordRequest);

  return {
    metrics,
    getReport,
    recordRequest,
  };
};
