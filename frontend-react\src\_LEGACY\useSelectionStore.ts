// useSelectionStore.ts - Phase 6: 文件选择状态管理专用Store
// 统一管理所有文件选择逻辑，消除legacy模式

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import type { FileEntry } from '@/types/electron';

// ============================================================================
// 类型定义
// ============================================================================

interface SelectionState {
  // 多选状态（主要）
  selectedFiles: FileEntry[];
  
  // 单选状态（向后兼容，自动从selectedFiles计算）
  selectedFile: FileEntry | null;
}

interface SelectionActions {
  // 主要选择操作
  setSelectedFiles: (files: FileEntry[]) => void;
  addToSelection: (file: FileEntry) => void;
  removeFromSelection: (filePath: string) => void;
  clearSelection: () => void;
  
  // 高级选择操作
  toggleFileSelection: (file: FileEntry) => void;
  selectRange: (files: FileEntry[], startFile: FileEntry, endFile: FileEntry) => void;
  selectAll: (files: FileEntry[]) => void;
  
  // 向后兼容操作
  setSelectedFile: (file: FileEntry | null) => void;
  
  // 查询操作
  isFileSelected: (filePath: string) => boolean;
  getSelectionCount: () => number;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: SelectionState = {
  selectedFiles: [],
  selectedFile: null,
};

// ============================================================================
// Store实现
// ============================================================================

export const useSelectionStore = create<SelectionState & SelectionActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // ========================================
      // 主要选择操作实现
      // ========================================
      setSelectedFiles: (files) =>
        set(() => ({
          selectedFiles: files,
          selectedFile: files.length > 0 ? files[files.length - 1] : null, // 最后选中的文件
        })),

      addToSelection: (file) =>
        set((state) => {
          const isAlreadySelected = state.selectedFiles.some(f => f.path === file.path);
          if (isAlreadySelected) return state; // 避免重复添加
          
          const newSelectedFiles = [...state.selectedFiles, file];
          return {
            selectedFiles: newSelectedFiles,
            selectedFile: file, // 更新单选状态
          };
        }),

      removeFromSelection: (filePath) =>
        set((state) => {
          const newSelectedFiles = state.selectedFiles.filter(f => f.path !== filePath);
          return {
            selectedFiles: newSelectedFiles,
            selectedFile: newSelectedFiles.length > 0 ? newSelectedFiles[newSelectedFiles.length - 1] : null,
          };
        }),

      clearSelection: () =>
        set(() => ({
          selectedFiles: [],
          selectedFile: null,
        })),

      // ========================================
      // 高级选择操作实现
      // ========================================
      toggleFileSelection: (file) =>
        set((state) => {
          const isSelected = state.selectedFiles.some(f => f.path === file.path);
          
          if (isSelected) {
            // 移除选择
            const newSelectedFiles = state.selectedFiles.filter(f => f.path !== file.path);
            return {
              selectedFiles: newSelectedFiles,
              selectedFile: newSelectedFiles.length > 0 ? newSelectedFiles[newSelectedFiles.length - 1] : null,
            };
          } else {
            // 添加选择
            const newSelectedFiles = [...state.selectedFiles, file];
            return {
              selectedFiles: newSelectedFiles,
              selectedFile: file,
            };
          }
        }),

      selectRange: (files, startFile, endFile) => {
        const startIndex = files.findIndex(f => f.path === startFile.path);
        const endIndex = files.findIndex(f => f.path === endFile.path);
        
        if (startIndex === -1 || endIndex === -1) return;
        
        const rangeStart = Math.min(startIndex, endIndex);
        const rangeEnd = Math.max(startIndex, endIndex);
        const rangeFiles = files.slice(rangeStart, rangeEnd + 1);
        
        set(() => ({
          selectedFiles: rangeFiles,
          selectedFile: endFile,
        }));
      },

      selectAll: (files) =>
        set(() => ({
          selectedFiles: [...files],
          selectedFile: files.length > 0 ? files[files.length - 1] : null,
        })),

      // ========================================
      // 向后兼容操作实现
      // ========================================
      setSelectedFile: (file) =>
        set(() => ({
          selectedFiles: file ? [file] : [],
          selectedFile: file,
        })),

      // ========================================
      // 查询操作实现
      // ========================================
      isFileSelected: (filePath) => {
        const state = get();
        return state.selectedFiles.some(f => f.path === filePath);
      },

      getSelectionCount: () => {
        const state = get();
        return state.selectedFiles.length;
      },
    }),
    {
      name: 'mizzy-star-selection-store',
    }
  )
);

// ============================================================================
// 选择器Hooks（性能优化）
// ============================================================================

export const useFileSelection = () => useSelectionStore((state) => ({
  selectedFiles: state.selectedFiles,
  selectedFile: state.selectedFile,
  setSelectedFiles: state.setSelectedFiles,
  addToSelection: state.addToSelection,
  removeFromSelection: state.removeFromSelection,
  clearSelection: state.clearSelection,
  toggleFileSelection: state.toggleFileSelection,
  selectRange: state.selectRange,
  selectAll: state.selectAll,
  isFileSelected: state.isFileSelected,
  getSelectionCount: state.getSelectionCount,
}), shallow);

// 向后兼容选择器
export const useLegacySelection = () => useSelectionStore((state) => ({
  selectedFile: state.selectedFile,
  setSelectedFile: state.setSelectedFile,
}), shallow);

export default useSelectionStore;
