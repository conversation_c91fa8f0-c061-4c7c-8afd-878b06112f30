import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';

interface FirstTimeUserGuideProps {
  onComplete: () => void;
}

/**
 * FirstTimeUserGuide - Phase 9B: 新用户引导组件
 * 
 * 为首次使用的用户提供清晰的操作指引，减少学习曲线
 */
export const FirstTimeUserGuide: React.FC<FirstTimeUserGuideProps> = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: '欢迎使用 Mizzy Star',
      content: (
        <div className="space-y-4">
          <div className="text-6xl text-center">🌟</div>
          <p className="text-lg text-center">
            智能图像数据库管理系统
          </p>
          <p className="text-muted-foreground text-center">
            让我们用30秒时间了解如何开始使用
          </p>
        </div>
      )
    },
    {
      title: '第一步：创建案例',
      content: (
        <div className="space-y-4">
          <div className="text-4xl text-center">📁</div>
          <p>
            <strong>案例</strong>是组织图像的基本单位。比如：
          </p>
          <ul className="list-disc list-inside space-y-2 text-sm">
            <li>"我的旅行照片" - 存放度假照片</li>
            <li>"产品摄影" - 存放商品图片</li>
            <li>"设计素材" - 存放设计资源</li>
          </ul>
          <div className="bg-info/10 border border-info/20 rounded-lg p-3">
            <p className="text-sm">
              💡 <strong>提示</strong>：点击左上角的案例切换器开始创建第一个案例
            </p>
          </div>
        </div>
      )
    },
    {
      title: '第二步：导入图像',
      content: (
        <div className="space-y-4">
          <div className="text-4xl text-center">🖼️</div>
          <p>
            创建案例后，您可以：
          </p>
          <ul className="list-disc list-inside space-y-2 text-sm">
            <li>使用"🧪 真实数据测试"功能选择文件夹</li>
            <li>拖拽图片到画廊区域</li>
            <li>点击上传按钮选择文件</li>
          </ul>
          <div className="bg-success/10 border border-success/20 rounded-lg p-3">
            <p className="text-sm">
              ✨ <strong>AI功能</strong>：导入后，AI会自动分析图像并提供标签建议
            </p>
          </div>
        </div>
      )
    },
    {
      title: '第三步：智能搜索',
      content: (
        <div className="space-y-4">
          <div className="text-4xl text-center">🔍</div>
          <p>
            Mizzy Star提供两种搜索方式：
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-card border rounded-lg p-3">
              <div className="font-medium mb-2">🧠 AI搜索</div>
              <p>用自然语言描述：<br/>"蓝天白云的风景照"</p>
            </div>
            <div className="bg-card border rounded-lg p-3">
              <div className="font-medium mb-2">🔧 高级搜索</div>
              <p>使用标签和条件：<br/>标签="风景" + 大小&gt;2MB</p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '开始使用！',
      content: (
        <div className="space-y-4">
          <div className="text-6xl text-center">🚀</div>
          <p className="text-lg text-center">
            您已经了解了基本操作！
          </p>
          <p className="text-muted-foreground text-center">
            现在可以开始管理您的图像了
          </p>
          <div className="bg-primary/10 border border-primary/20 rounded-lg p-3">
            <p className="text-sm">
              💡 随时点击右上角的"✨ UX演示"查看更多功能展示
            </p>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipGuide = () => {
    onComplete();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background border rounded-lg shadow-lg max-w-md w-full animate-fade-in">
        {/* 头部 */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">{steps[currentStep].title}</h2>
            <button
              onClick={skipGuide}
              className="text-muted-foreground hover:text-foreground text-sm"
            >
              跳过引导
            </button>
          </div>
          <div className="mt-2">
            <div className="flex space-x-1">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`h-1 flex-1 rounded-full ${
                    index <= currentStep ? 'bg-primary' : 'bg-muted'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-6 animate-slide-up">
          {steps[currentStep].content}
        </div>

        {/* 底部按钮 */}
        <div className="p-6 border-t flex justify-between">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            上一步
          </Button>
          <div className="text-sm text-muted-foreground">
            {currentStep + 1} / {steps.length}
          </div>
          <Button
            variant="primary"
            onClick={nextStep}
          >
            {currentStep === steps.length - 1 ? '开始使用' : '下一步'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FirstTimeUserGuide;
