/* Panel.module.css - 面板组件通用样式模块 */

/* ============================================================================
   面板内容容器
   ============================================================================ */

.panelContent {
  box-sizing: border-box;
  padding: var(--panel-padding-y) var(--panel-padding-x);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-row-gap); /* Applies the 4px global row interval */
  border-right: 1px solid var(--color-border-primary);
  border-bottom: 1px solid var(--color-border-primary);
  background-color: var(--color-surface-secondary);
  transition: border-color 0.2s ease-in-out;
}

/* Panel border hover effects */
.panelContent:hover {
  border-color: var(--color-border-focused);
}

/* Remove borders for specific panel positions */
.panelContent.lastHorizontal {
  border-right: none;
}

.panelContent.lastVertical {
  border-bottom: none;
}

/* ============================================================================
   工具栏结构
   ============================================================================ */

.toolbar {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-row-gap); /* Rows within toolbar are also 4px apart */
}

.toolbarRow {
  display: flex;
  align-items: center;
  height: var(--toolbar-row-height);
  gap: var(--spacing-2); /* 8px gap between items in a row */
}

/* ============================================================================
   图标样式
   ============================================================================ */

.icon {
  width: var(--icon-size-base);
  height: var(--icon-size-base);
}

/* ============================================================================
   面板特定样式
   ============================================================================ */

.panelHeader {
  padding: 16px;
  border-bottom: 1px solid var(--color-border-primary);
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.panelTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-title);
}

/* ============================================================================
   滚动条样式
   ============================================================================ */

.panelContent::-webkit-scrollbar {
  width: 6px;
}

.panelContent::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.panelContent::-webkit-scrollbar-thumb {
  background: rgba(164, 159, 154, 0.3);
  border-radius: 3px;
}

.panelContent::-webkit-scrollbar-thumb:hover {
  background: rgba(164, 159, 154, 0.5);
}

/* ============================================================================
   响应式设计
   ============================================================================ */

@media (max-width: 768px) {
  .panelContent {
    padding: 12px;
  }
  
  .panelHeader {
    padding: 12px;
  }
  
  .toolbarRow {
    height: 32px;
    gap: var(--spacing-1);
  }
}

/* ============================================================================
   状态样式
   ============================================================================ */

.loadingState,
.errorState,
.emptyState {
  padding: 16px;
  text-align: center;
  font-size: 14px;
  color: var(--color-text-content);
}

.errorState {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  border-radius: 4px;
}

.emptyState {
  opacity: 0.7;
  font-style: italic;
}

/* ============================================================================
   交互效果
   ============================================================================ */

.interactive {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.interactive:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.interactive:active {
  transform: scale(0.95);
}

/* ============================================================================
   布局辅助类
   ============================================================================ */

.flexRow {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--spacing-2);
}

.flexColumn {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.spaceBetween {
  justify-content: space-between;
}

.spaceAround {
  justify-content: space-around;
}

.center {
  justify-content: center;
  align-items: center;
}

/* ============================================================================
   文本样式
   ============================================================================ */

.textTitle {
  color: var(--color-text-title);
  font-size: var(--font-size-title);
  font-weight: 600;
}

.textBody {
  color: var(--color-text-body);
  font-size: var(--font-size-body);
}

.textContent {
  color: var(--color-text-content);
  font-size: var(--font-size-body);
}

.textSecondary {
  color: var(--color-text-secondary);
  font-size: var(--font-size-small);
}

/* ============================================================================
   边距和间距辅助类
   ============================================================================ */

.marginBottom {
  margin-bottom: var(--spacing-2);
}

.marginTop {
  margin-top: var(--spacing-2);
}

.paddingSmall {
  padding: var(--spacing-1);
}

.paddingMedium {
  padding: var(--spacing-2);
}

.paddingLarge {
  padding: var(--spacing-3);
}

/* ============================================================================
   Panel Resize Handle Styling
   ============================================================================ */

/* Global resize handle styling - targets react-resizable-panels handles */
:global([data-panel-resize-handle-enabled]) {
  background-color: var(--color-border-primary) !important;
  transition: background-color 0.2s ease-in-out !important;
  position: relative !important;
}

:global([data-panel-resize-handle-enabled]:hover) {
  background-color: var(--color-border-focused) !important;
}

:global([data-panel-resize-handle-enabled]:active) {
  background-color: var(--color-accent-primary) !important;
}

/* Horizontal resize handles */
:global([data-panel-resize-handle-enabled][data-panel-resize-handle-direction="horizontal"]) {
  width: 1px !important;
  cursor: col-resize !important;
}

/* Vertical resize handles */
:global([data-panel-resize-handle-enabled][data-panel-resize-handle-direction="vertical"]) {
  height: 1px !important;
  cursor: row-resize !important;
}

/* Add visual indicator on hover */
:global([data-panel-resize-handle-enabled]:hover::after) {
  content: '';
  position: absolute;
  background-color: var(--color-accent-primary);
  opacity: 0.3;
  transition: opacity 0.2s ease-in-out;
}

:global([data-panel-resize-handle-enabled][data-panel-resize-handle-direction="horizontal"]:hover::after) {
  top: 0;
  left: -1px;
  right: -1px;
  bottom: 0;
  width: 3px;
}

:global([data-panel-resize-handle-enabled][data-panel-resize-handle-direction="vertical"]:hover::after) {
  left: 0;
  right: 0;
  top: -1px;
  bottom: -1px;
  height: 3px;
}
