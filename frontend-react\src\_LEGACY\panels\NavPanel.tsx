// 导航面板 (A区) - 负责案例目录和导航功能
import React, { memo } from 'react';
import { RedoIcon } from '../../assets/icons/RedoIcon';
import { HeartRemoveIcon } from '../../assets/icons/HeartRemoveIcon';
import { IconButton } from '../ui/IconButton';

interface NavPanelProps {
  // 待定义的props接口
}

export const NavPanel: React.FC<NavPanelProps> = memo(() => {
  return (
    <div className="flex-1 bg-mizzy-bg-panel overflow-auto flex flex-col">
      {/* 标签内容区域 */}
      <div className="flex-1 p-4 text-mizzy-text-content">
        {/* 标签内容将在后续实现 */}
        <div className="text-sm text-mizzy-text-secondary">
          标签内容区域
        </div>
      </div>

      {/* 导航栏底部工具栏 - 屏蔽器按钮 */}
      <div className="p-4 border-t border-mizzy-border-base">
        <div className="flex justify-end items-center gap-[4px]">
          <IconButton icon={RedoIcon} tooltip="关闭屏蔽器" />
          <IconButton icon={HeartRemoveIcon} tooltip="屏蔽器" />
        </div>
      </div>
    </div>
  );
});

NavPanel.displayName = 'NavPanel';

export default NavPanel;
