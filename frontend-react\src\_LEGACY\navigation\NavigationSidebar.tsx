// NavigationSidebar - Integrated with existing API and state management
// 集成现有API和状态管理的导航侧边栏组件

import React, { useState, useMemo } from 'react';
import {
  Settings,
  Maximize2,
  Database,
  Search,
  ChevronDown,
  ChevronRight,
  Star,
  Code,
  Camera,
  Eye,
  FileText,
  Brain,
  Filter,
  FilterX,
  Plus,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Shield,
  ShieldOff,
  Workflow
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from '@/components/ui/dialog';

// Hooks and utilities
// Pre-Phase 5: 移除useApi依赖，使用文件系统状态
// import { useCases, useTags } from '@/hooks/useApi';
import { useUIStore } from '@/store';
import { generateTagUnitsFromFiles } from '@/adapters/uiDataAdapters';
import type { UITagUnit } from '@/adapters/uiDataAdapters';

// ============================================================================
// 接口定义
// ============================================================================

interface NavigationSidebarProps {
  onToggleSidebars?: () => void;
  onToggleWorkspace?: () => void;
  onSwapGalleryWorkspace?: () => void;
  className?: string;
}

interface TagUnit {
  id: string;
  name: string;
  icon: React.ComponentType;
  tags: string[];
  expanded: boolean;
  editable: boolean;
}

// ============================================================================
// NavigationSidebar 组件
// ============================================================================

export const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  onToggleSidebars,
  onToggleWorkspace,
  onSwapGalleryWorkspace,
  className,
}) => {
  // ========================================
  // 状态管理
  // ========================================
  const {
    selectedCaseId,
    setSelectedCase,
    searchQuery,
    setSearchQuery,
    activeFilters,
    setFilter,
    removeFilter,
    clearFilters,
  } = useUIStore();

  // 本地状态
  const [tagSearch, setTagSearch] = useState('');
  const [isLibraryDialogOpen, setIsLibraryDialogOpen] = useState(false);
  const [newTagInput, setNewTagInput] = useState('');
  const [showNewTagInput, setShowNewTagInput] = useState(false);
  const [blockedTags, setBlockedTags] = useState<string[]>([]);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [sortBy, setSortBy] = useState<'name' | 'recent' | 'count'>('name');
  const [tagUnits, setTagUnits] = useState<TagUnit[]>([]);

  // ========================================
  // API数据获取
  // ========================================
  const { data: cases, isLoading: casesLoading } = useCases();
  const { data: tagsData, isLoading: tagsLoading } = useTags(selectedCaseId || undefined);

  // ========================================
  // 计算属性
  // ========================================

  // 当前选中的案例
  const currentCase = useMemo(() => {
    return cases?.find(c => c.id === selectedCaseId);
  }, [cases, selectedCaseId]);

  // 当前库名称
  const currentLibrary = currentCase?.case_name || '选择案例';

  // 选中的标签（从activeFilters中提取）
  const selectedTags = useMemo(() => {
    const tags: string[] = [];
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (key.startsWith('tag_')) {
        tags.push(value);
      }
    });
    return tags;
  }, [activeFilters]);

  // ========================================
  // 事件处理函数
  // ========================================

  const toggleTagUnit = (unitId: string) => {
    setTagUnits(prev => prev.map(unit =>
      unit.id === unitId ? { ...unit, expanded: !unit.expanded } : unit
    ));
  };

  const handleTagClick = (tag: string) => {
    const tagName = tag.split('(')[0];
    if (blockedTags.includes(tagName)) return;

    const filterKey = `tag_${tagName}`;

    if (selectedTags.includes(tagName)) {
      removeFilter(filterKey);
    } else {
      setFilter(filterKey, tagName);
    }
  };

  const handleSortOrderToggle = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  const handleCaseSelect = (caseId: number) => {
    setSelectedCase(caseId);
    setIsLibraryDialogOpen(false);
  };

  const handleToggleBlockedTag = (tag: string) => {
    const tagName = tag.split('(')[0];
    if (blockedTags.includes(tagName)) {
      setBlockedTags(prev => prev.filter(t => t !== tagName));
    } else {
      setBlockedTags(prev => [...prev, tagName]);
    }
  };

  // ========================================
  // 初始化标签单元（连接真实API数据）
  // ========================================
  React.useEffect(() => {
    if (!selectedCaseId || !tagsData) {
      // 如果没有选中案例或标签数据，使用默认标签单元
      const defaultTagUnits: TagUnit[] = [
        {
          id: 'favorites',
          name: '标签看板',
          icon: Star,
          tags: ['选择案例查看标签'],
          expanded: true,
          editable: true
        }
      ];
      setTagUnits(defaultTagUnits);
      return;
    }

    // 使用真实API数据生成标签单元
    try {
      // TODO: 使用数据适配器转换标签数据
      // const uiTagUnits = generateTagUnitsFromTags(tagsData);

      // 临时使用模拟数据结构，但显示真实案例信息
      const realTagUnits: TagUnit[] = [
        {
          id: 'user',
          name: '用户标签',
          icon: Star,
          tags: tagsData.user_tags?.slice(0, 10).map(tag => `${tag}(${Math.floor(Math.random() * 20) + 1})`) || ['暂无用户标签'],
          expanded: true,
          editable: true
        },
        {
          id: 'ai',
          name: 'AI标签',
          icon: Brain,
          tags: tagsData.ai_tags?.slice(0, 10).map(tag => `${tag}(${Math.floor(Math.random() * 15) + 1})`) || ['暂无AI标签'],
          expanded: false,
          editable: false
        },
        {
          id: 'metadata',
          name: '元数据',
          icon: FileText,
          tags: tagsData.metadata_tags?.slice(0, 10).map(tag => `${tag}(${Math.floor(Math.random() * 30) + 1})`) || ['暂无元数据标签'],
          expanded: false,
          editable: false
        },
        {
          id: 'cv',
          name: '计算机视觉',
          icon: Eye,
          tags: tagsData.cv_tags?.slice(0, 10).map(tag => `${tag}(${Math.floor(Math.random() * 25) + 1})`) || ['暂无CV标签'],
          expanded: false,
          editable: false
        }
      ];

      setTagUnits(realTagUnits);
    } catch (error) {
      console.error('处理标签数据时出错:', error);

      // 出错时使用默认标签单元
      const fallbackTagUnits: TagUnit[] = [
        {
          id: 'error',
          name: '标签加载失败',
          icon: FileText,
          tags: ['请刷新页面重试'],
          expanded: true,
          editable: false
        }
      ];
      setTagUnits(fallbackTagUnits);
    }
  }, [selectedCaseId, tagsData]);

  // ========================================
  // 渲染函数
  // ========================================

  const renderTagUnit = (unit: TagUnit) => {
    const IconComponent = unit.icon;

    return (
      <div key={unit.id} className="mb-4">
        {/* 标签单元头部 */}
        <div
          className="flex items-center justify-between p-2 rounded cursor-pointer hover:bg-[var(--mizzy-button)] transition-colors"
          onClick={() => toggleTagUnit(unit.id)}
        >
          <div className="flex items-center gap-2">
            <IconComponent className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
            <span className="text-sm font-medium" style={{ color: 'var(--mizzy-title)' }}>
              {unit.name}
            </span>
          </div>
          {unit.expanded ? (
            <ChevronDown className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          ) : (
            <ChevronRight className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          )}
        </div>

        {/* 标签列表 */}
        {unit.expanded && (
          <div className="ml-6 space-y-1">
            {unit.tags.map((tag, index) => {
              const tagName = tag.split('(')[0];
              const isSelected = selectedTags.includes(tagName);
              const isBlocked = blockedTags.includes(tagName);

              return (
                <div
                  key={index}
                  className={`flex items-center justify-between p-1 rounded cursor-pointer transition-colors ${
                    isSelected
                      ? 'bg-[var(--mizzy-highlight)] text-white'
                      : isBlocked
                      ? 'opacity-50 line-through'
                      : 'hover:bg-[var(--mizzy-button)]'
                  }`}
                  onClick={() => handleTagClick(tag)}
                >
                  <span className="text-sm" style={{ color: isSelected ? 'white' : 'var(--mizzy-content)' }}>
                    {tag}
                  </span>
                  {unit.editable && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleBlockedTag(tag);
                      }}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      {isBlocked ? (
                        <Shield className="w-3 h-3" style={{ color: 'var(--mizzy-icon)' }} />
                      ) : (
                        <ShieldOff className="w-3 h-3" style={{ color: 'var(--mizzy-icon)' }} />
                      )}
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // ========================================
  // 主渲染
  // ========================================

  return (
    <div
      className={`h-full flex flex-col ${className || ''}`}
      style={{ background: 'var(--mizzy-nav)' }}
    >
      {/* 头部控制区 */}
      <div className="p-4 border-b" style={{ borderColor: 'var(--mizzy-border-ui)' }}>
        {/* 顶部按钮组 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleSidebars}
              className="p-1"
              title="切换侧边栏"
            >
              <Maximize2 className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleWorkspace}
              className="p-1"
              title="切换工作台"
            >
              <Workflow className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
            </Button>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="p-1"
            title="设置"
          >
            <Settings className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          </Button>
        </div>

        {/* 库选择器 */}
        <Dialog open={isLibraryDialogOpen} onOpenChange={setIsLibraryDialogOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between mb-4"
              style={{
                background: 'var(--mizzy-input)',
                borderColor: 'var(--mizzy-border-ui)',
                color: 'var(--mizzy-content)'
              }}
            >
              <div className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                <span className="truncate">{currentLibrary}</span>
              </div>
              <ChevronDown className="w-4 h-4" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>选择案例库</DialogTitle>
            <DialogDescription>
              选择要浏览的案例库
            </DialogDescription>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {casesLoading ? (
                <div className="text-center py-4">加载中...</div>
              ) : (
                cases?.map((caseItem) => (
                  <Button
                    key={caseItem.id}
                    variant={selectedCaseId === caseItem.id ? "default" : "ghost"}
                    className="w-full justify-start"
                    onClick={() => handleCaseSelect(caseItem.id)}
                  >
                    <Database className="w-4 h-4 mr-2" />
                    {caseItem.case_name}
                  </Button>
                ))
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          <Input
            placeholder="搜索文件..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
            style={{
              background: 'var(--mizzy-input)',
              borderColor: 'var(--mizzy-border-ui)',
              color: 'var(--mizzy-content)'
            }}
          />
        </div>
      </div>

      {/* 标签区域 */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* 标签搜索和控制 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium" style={{ color: 'var(--mizzy-title)' }}>
              标签筛选
            </span>
            {selectedTags.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="p-1"
                title="清除筛选"
              >
                <FilterX className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
              </Button>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="p-1">
                <ArrowUpDown className="w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setSortBy('name')}>
                按名称排序
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('count')}>
                按数量排序
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('recent')}>
                按最近使用
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 标签搜索框 */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          <Input
            placeholder="搜索标签..."
            value={tagSearch}
            onChange={(e) => setTagSearch(e.target.value)}
            className="pl-10"
            style={{
              background: 'var(--mizzy-input)',
              borderColor: 'var(--mizzy-border-ui)',
              color: 'var(--mizzy-content)'
            }}
          />
        </div>

        {/* 标签单元列表 */}
        <div className="space-y-2">
          {tagUnits.map(renderTagUnit)}
        </div>
      </div>
    </div>
  );
};
