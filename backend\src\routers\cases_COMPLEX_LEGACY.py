# src/routers/cases.py
import os
import shutil
import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, BackgroundTasks, Query, Request, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

# 导入我们的数据库工具、模型、蓝图和 CRUD 层
from .. import models, schemas
from ..database import get_master_db
from ..crud import (
    create_case,
    get_cases,
    get_case,
    delete_case,
    update_case,
    create_file_for_case,
    get_files_for_case,
    get_file,
    delete_file
)
from ..crud.file_crud import soft_delete_file
from ..services.cover_service import get_cover_service, CoverImageService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/cases",
    tags=["Cases"]
)

# 🔥 BEDROCK: 注释掉创建案例API - 非骨架功能
# @router.post("/", response_model=schemas.Case, status_code=201)
# def create_case_endpoint(
#     case: schemas.CaseCreate,
#     db: Session = Depends(get_master_db),
#     cover_service: CoverImageService = Depends(get_cover_service)
# ):
#     """
#     创建一个新案例。
#     使用 CRUD 层处理业务逻辑，并设置默认封面。
#     """
#     try:
#         # 创建案例
#         new_case = create_case(db, case)
#
#         # 设置默认占位图封面
#         try:
#             cover_service.set_placeholder_cover(db, new_case.id)
#             # 重新获取更新后的案例信息
#             updated_case = get_case(db, new_case.id)
#             return updated_case if updated_case else new_case
#         except Exception as cover_error:
#             logger.warning(f"设置默认封面失败: {cover_error}")
#             return new_case
#
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[schemas.Case])
def read_cases_endpoint(skip: int = 0, limit: int = 100, db: Session = Depends(get_master_db)):
    """
    从主数据库中读取所有案例的基本信息（不包含文件列表）。
    """
    return get_cases(db, skip, limit)

# 🔥 BEDROCK: 注释掉单个案例详情API - 非骨架功能
# @router.get("/{case_id}", response_model=schemas.Case)
# def read_case_endpoint(case_id: int, db: Session = Depends(get_master_db)):
#     """
#     读取单个案例的详细信息，包括其所有文件。
#     """
#     db_case = get_case(db, case_id)
#     if db_case is None:
#         raise HTTPException(status_code=404, detail="案例未找到或其数据库文件不存在")
#     return db_case

# 🔥 BEDROCK: 注释掉更新案例API - 非骨架功能
# @router.put("/{case_id}", response_model=schemas.Case)
# def update_case_endpoint(case_id: int, case_update: schemas.CaseUpdate, db: Session = Depends(get_master_db)):
#     """
#     更新指定案例的信息。
#     """
#     try:
#         updated_case = update_case(db, case_id, case_update)
#         if not updated_case:
#             raise HTTPException(status_code=404, detail="案例未找到")
#         return updated_case
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

# 🔥 BEDROCK: 注释掉删除案例API - 非骨架功能
# @router.delete("/{case_id}", response_model=schemas.Case)
# def delete_case_endpoint(case_id: int, db: Session = Depends(get_master_db)):
#     """
#     删除指定案例。
#     这将删除案例记录以及其对应的数据库文件。
#     """
#     deleted_case = delete_case(db, case_id)
#     if not deleted_case:
#         raise HTTPException(status_code=404, detail="案例未找到")
#     return deleted_case

# 🔥 BEDROCK: 注释掉文件创建API - 非骨架功能
# @router.post("/{case_id}/files", response_model=schemas.File, status_code=201)
# def create_file_for_case_endpoint(case_id: int, file: schemas.FileCreate, db: Session = Depends(get_master_db)):
#     """
#     为指定案例创建一个新的文件记录。
#     使用 CRUD 层处理业务逻辑。
#     """
#     try:
#         db_file = create_file_for_case(db, case_id, file)
#         if db_file is None:
#             raise HTTPException(status_code=404, detail="案例未找到或其数据库未配置")
#         return db_file
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

@router.get("/{case_id}/files/", response_model=schemas.FileListResponse)
@router.get("/{case_id}/files", response_model=schemas.FileListResponse)
def get_case_files_endpoint(
    case_id: int,
    request: Request,
    limit: int = Query(50, ge=1, le=200, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    db: Session = Depends(get_master_db)
):
    """
    根据标签筛选指定案例下的文件

    支持多种标签类型的组合筛选，包括元数据标签、用户标签、AI标签等。
    使用tag_前缀的查询参数进行筛选。

    示例：
    - /cases/123/files?tag_camera=Nikon%20D850&tag_year=2024
    - /cases/123/files?tag_photographer=张三&limit=20
    """
    # 验证案例是否存在
    case = get_case(db, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="案例不存在")

    # 提取tag_前缀的查询参数（优化性能）
    tag_filters = {
        param_name[4:]: param_value
        for param_name, param_value in request.query_params.items()
        if param_name.startswith("tag_") and param_value
    }

    # 获取文件列表
    files = get_files_for_case(db, case_id)

    # 应用标签筛选
    if tag_filters:
        filtered_files = []
        for file in files:
            if _file_matches_tags(file, tag_filters):
                filtered_files.append(file)
        files = filtered_files

    # 应用分页
    total = len(files)
    paginated_files = files[offset:offset + limit]

    return schemas.FileListResponse(
        files=paginated_files,
        total=total,
        limit=limit,
        offset=offset,
        filters=tag_filters if tag_filters else None
    )

@router.get("/{case_id}/files/{file_id}", response_model=schemas.File)
def get_case_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    获取指定案例的特定文件信息
    """
    file = get_file(db, case_id, file_id)
    if not file:
        raise HTTPException(status_code=404, detail="文件未找到")
    return file

@router.delete("/{case_id}/files/{file_id}", response_model=schemas.OperationResponse)
def delete_case_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    软删除指定案例的特定文件
    文件将被移动到案例目录下的trash文件夹中，保留缩略图
    """
    try:
        success = soft_delete_file(db, case_id, file_id)
        if not success:
            raise HTTPException(status_code=404, detail="文件未找到或删除失败")

        return schemas.OperationResponse(
            success=True,
            message="文件已删除并移动到回收站",
            data={"case_id": case_id, "file_id": file_id, "status": "soft_deleted"}
        )
    except Exception as e:
        logger.error(f"删除文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

from fastapi import UploadFile, File
from fastapi.responses import FileResponse
from PIL import Image
import io
import os
from pathlib import Path

def _process_exif_value(tag_name: str, value):
    """处理EXIF值为可读格式"""
    try:
        import fractions
        from datetime import datetime

        # 处理光圈
        if tag_name == 'FNumber':
            if isinstance(value, tuple) and len(value) == 2 and value[1] != 0:
                aperture = value[0] / value[1]
                return f"f/{aperture:.1f}"
            return f"f/{float(value):.1f}"

        # 处理快门速度
        elif tag_name == 'ExposureTime':
            if isinstance(value, tuple) and len(value) == 2 and value[1] != 0:
                exposure = value[0] / value[1]
                if exposure < 1:
                    return f"1/{int(1/exposure)} 秒"
                else:
                    return f"{exposure:.1f} 秒"
            elif isinstance(value, float):
                if value < 1:
                    return f"1/{int(1/value)} 秒"
                else:
                    return f"{value:.1f} 秒"

        # 处理ISO
        elif tag_name == 'ISOSpeedRatings':
            return f"ISO {int(value)}"

        # 处理焦距
        elif tag_name == 'FocalLength':
            if isinstance(value, tuple) and len(value) == 2 and value[1] != 0:
                focal = value[0] / value[1]
                return f"{focal:.1f}mm"
            return f"{float(value):.1f}mm"

        # 处理拍摄日期
        elif tag_name == 'DateTimeOriginal':
            if isinstance(value, str):
                try:
                    dt = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                    return dt.strftime('%Y/%m/%d')
                except:
                    return value

        # 处理色彩空间
        elif tag_name == 'ColorSpace':
            color_spaces = {1: 'sRGB', 2: 'AdobeRGB'}
            return color_spaces.get(int(value), f'Unknown({value})')

        # 处理闪光灯
        elif tag_name == 'Flash':
            flash_value = int(value)
            return "闪光灯开启" if (flash_value & 0x01) else "闪光灯关闭"

        # 处理白平衡
        elif tag_name == 'WhiteBalance':
            return "自动" if int(value) == 0 else "手动"

        # 其他字段
        else:
            return str(value)

    except Exception as e:
        logger.warning(f"处理EXIF值失败 {tag_name}: {e}")
        return str(value)

@router.post("/{case_id}/files/upload", response_model=schemas.File, status_code=201)
def upload_file_endpoint(
    case_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_master_db)
):
    """
    上传文件到指定案例，只存储原始文件路径并生成缩略图
    """
    import tempfile
    import shutil
    import os
    import mimetypes
    from pathlib import Path
    from PIL import Image
    from ..services.rule_engine import process_file_with_rules
    from ..database import create_case_directory

    # 验证案例存在
    from ..crud import get_case
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")

    # 获取原始文件名
    original_filename = file.filename or "unknown"

    # 创建临时文件来处理上传的文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(original_filename).suffix) as temp_file:
        # 将上传的文件内容写入临时文件
        shutil.copyfileobj(file.file, temp_file)
        temp_file_path = temp_file.name

    try:
        # 验证文件类型
        file_type, _ = mimetypes.guess_type(original_filename)
        if not file_type:
            file_type = file.content_type

        # 创建案例目录结构
        case_dir = create_case_directory(case_id)
        thumbnails_dir = case_dir / "thumbnails"
        thumbnails_dir.mkdir(exist_ok=True)

        # 检查是否为图像文件
        is_image = file_type and file_type.startswith("image/")

        # 初始化文件信息
        width, height, taken_at = None, None, None
        thumbnail_path = None
        exif_metadata = {}  # 在正确的作用域中初始化

        if is_image:
            try:
                # 处理图像文件
                with Image.open(temp_file_path) as img:
                    # 获取图像尺寸
                    width, height = img.size

                    # 提取完整EXIF数据（包括拍摄时间和其他元数据）
                    try:
                        from PIL.ExifTags import TAGS
                        from datetime import datetime
                        import fractions

                        exif_data = img.getexif()
                        if exif_data:
                            logger.info(f"📊 发现EXIF数据，共 {len(exif_data)} 个字段")

                            # EXIF字段映射
                            exif_mapping = {
                                'Make': 'camera_make',
                                'Model': 'camera_model',
                                'FNumber': 'aperture',
                                'ExposureTime': 'shutter_speed',
                                'ISOSpeedRatings': 'iso',
                                'FocalLength': 'focal_length',
                                'DateTimeOriginal': 'date_time_original',
                                'ColorSpace': 'color_space',
                                'Flash': 'flash',
                                'WhiteBalance': 'white_balance',
                                'Software': 'software'
                            }

                            for tag_id, value in exif_data.items():
                                tag_name = TAGS.get(tag_id)

                                # 处理拍摄时间（用于taken_at字段）
                                if tag_name == 'DateTimeOriginal':
                                    try:
                                        taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                    except:
                                        pass

                                # 处理所有映射的EXIF字段
                                if tag_name in exif_mapping:
                                    field_name = exif_mapping[tag_name]
                                    processed_value = _process_exif_value(tag_name, value)
                                    if processed_value:
                                        exif_metadata[field_name] = processed_value
                                        logger.info(f"✅ 提取EXIF字段: {field_name} = {processed_value}")

                            logger.info(f"🎉 成功提取 {len(exif_metadata)} 个EXIF字段")
                        else:
                            logger.info("📋 图片没有EXIF数据")

                    except Exception as e:
                        logger.warning(f"无法提取EXIF信息: {e}")

                    # 完整的EXIF元数据已在上传时提取

                    # 生成300px缩略图
                    img_copy = img.copy()

                    # 计算缩略图尺寸（长边为300px）
                    max_size = 300
                    if width > height:
                        new_width = max_size
                        new_height = int((height * max_size) / width)
                    else:
                        new_height = max_size
                        new_width = int((width * max_size) / height)

                    img_copy = img_copy.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 转换为RGB模式（如果需要）
                    if img_copy.mode in ("RGBA", "P"):
                        img_copy = img_copy.convert("RGB")

                    # 保存缩略图
                    thumbnail_filename = f"{Path(original_filename).stem}_thumb.jpg"
                    thumbnail_full_path = thumbnails_dir / thumbnail_filename
                    img_copy.save(thumbnail_full_path, "JPEG", quality=85)

                    thumbnail_path = str(thumbnail_full_path)
                    logger.info(f"✅ 生成缩略图: {thumbnail_filename} ({new_width}x{new_height})")

            except Exception as e:
                logger.error(f"处理图像文件失败: {e}")
                # 即使图像处理失败，也继续创建文件记录

        # 对于Web上传，我们需要保存文件到永久位置
        # 创建案例uploads目录用于存储上传的文件
        uploads_dir = case_dir / "uploads"
        uploads_dir.mkdir(exist_ok=True)

        # 生成永久文件路径
        permanent_file_path = uploads_dir / original_filename

        # 检查文件是否已存在
        counter = 1
        base_name = Path(original_filename).stem
        extension = Path(original_filename).suffix
        while permanent_file_path.exists():
            new_filename = f"{base_name}_{counter}{extension}"
            permanent_file_path = uploads_dir / new_filename
            counter += 1

        # 复制临时文件到永久位置
        shutil.copy2(temp_file_path, permanent_file_path)

        # 创建文件记录（存储永久文件路径）
        file_create = schemas.FileCreate(
            file_name=permanent_file_path.name,
            file_path=str(permanent_file_path.absolute()),  # 存储永久文件路径
            file_type=file_type,
            width=width,
            height=height,
            taken_at=taken_at,
            thumbnail_small_path=thumbnail_path  # 存储缩略图路径
        )

        from ..crud import create_file_for_case
        db_file = create_file_for_case(db, case_id, file_create)
        if db_file is None:
            raise HTTPException(status_code=404, detail="案例未找到")

        logger.info(f"文件处理完成，准备应用规则引擎: {db_file.file_name if db_file else 'None'}")

        # 应用规则引擎处理文件标签，并合并EXIF数据
        try:
            logger.info(f"开始应用规则引擎处理文件: {db_file.file_name}")
            tags_data = process_file_with_rules(db, case_id, db_file)
            logger.info(f"规则引擎生成的标签数据: {tags_data}")

            # 🔧 关键修复：合并EXIF数据到规则引擎结果
            if exif_metadata:
                logger.info(f"🔄 合并EXIF数据到规则引擎结果: {len(exif_metadata)} 个字段")

                # 确保metadata部分存在
                if "tags" not in tags_data:
                    tags_data["tags"] = {}
                if "metadata" not in tags_data["tags"]:
                    tags_data["tags"]["metadata"] = {}

                # 合并EXIF数据，EXIF数据优先级更高
                tags_data["tags"]["metadata"].update(exif_metadata)
                logger.info(f"✅ EXIF数据合并完成，最终metadata字段数: {len(tags_data['tags']['metadata'])}")

            # 更新文件的tags字段
            from ..database import get_case_db_session, get_case_database_path
            case_db_path = get_case_database_path(case_id)
            case_db = get_case_db_session(str(case_db_path))

            # 使用原始的SQL更新方式
            file_obj = case_db.query(models.File).filter(models.File.id == db_file.id).first()
            if file_obj:
                import json
                file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
                case_db.commit()
                case_db.refresh(file_obj)
                case_db.close()
                logger.info(f"🎉 规则引擎处理完成，包含EXIF数据: {db_file.file_name}")
            else:
                logger.error(f"未找到文件对象: {db_file.id}")
                case_db.close()

        except Exception as e:
            logger.error(f"规则引擎处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 规则引擎失败不影响文件上传，只记录错误

        return db_file

    except Exception as e:
        logger.error(f"文件上传处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 清理临时文件
        try:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

@router.post("/{case_id}/files/import-local", response_model=schemas.File, status_code=201)
def import_local_file_endpoint(
    case_id: int,
    file_path: str = Form(...),
    db: Session = Depends(get_master_db)
):
    """
    导入本地文件到指定案例，只存储文件路径并生成缩略图
    """
    import mimetypes
    from pathlib import Path
    from PIL import Image
    from ..services.rule_engine import process_file_with_rules
    from ..database import create_case_directory

    # 验证案例存在
    from ..crud import get_case
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")

    # 验证文件路径存在
    original_path = Path(file_path)
    if not original_path.exists() or not original_path.is_file():
        raise HTTPException(status_code=400, detail=f"文件路径不存在: {file_path}")

    # 获取文件信息
    original_filename = original_path.name
    file_type, _ = mimetypes.guess_type(original_path)

    try:
        # 创建案例目录结构
        case_dir = create_case_directory(case_id)
        thumbnails_dir = case_dir / "thumbnails"
        thumbnails_dir.mkdir(exist_ok=True)

        # 检查是否为图像文件
        is_image = file_type and file_type.startswith("image/")

        # 初始化文件信息
        width, height, taken_at = None, None, None
        thumbnail_path = None

        if is_image:
            try:
                # 处理图像文件
                with Image.open(original_path) as img:
                    # 获取图像尺寸
                    width, height = img.size

                    # 尝试获取EXIF拍摄时间（保持原有逻辑用于taken_at字段）
                    try:
                        from PIL.ExifTags import TAGS
                        exif_data = img.getexif()
                        if exif_data:
                            for tag, value in exif_data.items():
                                tag_name = TAGS.get(tag)
                                if tag_name == 'DateTimeOriginal':
                                    from datetime import datetime
                                    taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                    break
                    except Exception as e:
                        logger.warning(f"无法提取EXIF时间信息: {e}")

                    # 注意：完整的EXIF元数据将在规则引擎处理时提取

                    # 生成300px缩略图
                    img_copy = img.copy()

                    # 计算缩略图尺寸（长边为300px）
                    max_size = 300
                    if width > height:
                        new_width = max_size
                        new_height = int((height * max_size) / width)
                    else:
                        new_height = max_size
                        new_width = int((width * max_size) / height)

                    img_copy = img_copy.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 转换为RGB模式（如果需要）
                    if img_copy.mode in ("RGBA", "P"):
                        img_copy = img_copy.convert("RGB")

                    # 保存缩略图
                    thumbnail_filename = f"{Path(original_filename).stem}_thumb.jpg"
                    thumbnail_full_path = thumbnails_dir / thumbnail_filename
                    img_copy.save(thumbnail_full_path, "JPEG", quality=85)

                    thumbnail_path = str(thumbnail_full_path)
                    logger.info(f"✅ 生成缩略图: {thumbnail_filename} ({new_width}x{new_height})")

            except Exception as e:
                logger.error(f"处理图像文件失败: {e}")
                # 即使图像处理失败，也继续创建文件记录

        # 创建文件记录（存储原始文件路径）
        file_create = schemas.FileCreate(
            file_name=original_filename,
            file_path=str(original_path.absolute()),  # 存储原始文件的绝对路径
            file_type=file_type,
            width=width,
            height=height,
            taken_at=taken_at,
            thumbnail_small_path=thumbnail_path  # 存储缩略图路径
        )

        from ..crud import create_file_for_case
        db_file = create_file_for_case(db, case_id, file_create)
        if db_file is None:
            raise HTTPException(status_code=404, detail="案例未找到")

        logger.info(f"文件导入完成，准备应用规则引擎: {db_file.file_name}")

        # 应用规则引擎处理文件标签
        try:
            logger.info(f"开始应用规则引擎处理文件: {db_file.file_name}")
            tags_data = process_file_with_rules(db, case_id, db_file)
            logger.info(f"规则引擎生成的标签数据: {tags_data}")

            # 更新文件的tags字段
            from ..database import get_case_db_session, get_case_database_path
            case_db_path = get_case_database_path(case_id)
            case_db = get_case_db_session(str(case_db_path))

            # 使用原始的SQL更新方式
            file_obj = case_db.query(models.File).filter(models.File.id == db_file.id).first()
            if file_obj:
                import json
                file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
                case_db.commit()
                case_db.refresh(file_obj)
                case_db.close()
                logger.info(f"规则引擎处理完成: {db_file.file_name}")
            else:
                logger.error(f"未找到文件对象: {db_file.id}")
                case_db.close()

        except Exception as e:
            logger.error(f"规则引擎处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 规则引擎失败不影响文件导入，只记录错误

        return db_file

    except Exception as e:
        logger.error(f"文件导入处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{case_id}/files/{file_id}/download")
def download_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    下载指定案例的特定文件
    """
    file = get_file(db, case_id, file_id)
    if not file:
        raise HTTPException(status_code=404, detail="文件未找到")

    file_path = getattr(file, 'file_path', None)
    if not file_path or not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    file_name = getattr(file, 'file_name', 'unknown')
    file_type = getattr(file, 'file_type', None)

    # 处理文件名编码问题
    try:
        # 确保文件名是有效的UTF-8字符串
        if isinstance(file_name, str):
            file_name.encode('utf-8')
    except UnicodeEncodeError:
        logger.warning(f"文件名编码问题，使用默认名称: {file_name}")
        file_name = 'download_file'

    return FileResponse(
        path=file_path,
        filename=file_name,
        media_type=file_type or 'application/octet-stream'
    )

@router.get("/{case_id}/files/{file_id}/view")
def view_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    在线查看指定案例的特定文件（原始大图）
    """
    try:
        file = get_file(db, case_id, file_id)
        if not file:
            raise HTTPException(status_code=404, detail="文件未找到")

        file_path = getattr(file, 'file_path', None)
        logger.info(f"查看文件路径: {file_path}")

        if not file_path:
            raise HTTPException(status_code=404, detail="文件路径为空")

        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            raise HTTPException(status_code=404, detail="原始文件不存在")

        # 检查是否为图片文件
        file_type = getattr(file, 'file_type', None)
        if not file_type or not file_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="文件不是图片类型")

        logger.info(f"返回文件: {file_path}, 类型: {file_type}")

        # 处理文件名编码问题
        file_name = getattr(file, 'file_name', 'image')
        try:
            # 尝试对文件名进行URL编码以处理中文字符
            from urllib.parse import quote
            encoded_filename = quote(file_name.encode('utf-8'))
            content_disposition = f"inline; filename*=UTF-8''{encoded_filename}"
        except Exception as e:
            logger.warning(f"文件名编码失败: {e}, 使用默认文件名")
            content_disposition = "inline; filename=\"image\""

        return FileResponse(
            path=file_path,
            media_type=file_type,
            headers={
                "Cache-Control": "public, max-age=3600",  # 缓存1小时
                "Content-Disposition": content_disposition
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查看文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"查看文件失败: {str(e)}")

@router.post("/{case_id}/files/batch-import", response_model=dict, status_code=202)
def batch_import_files_endpoint(
    case_id: int,
    directory_path: str,
    recursive: bool = True,
    batch_size: int = 100,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_master_db)
):
    """
    批量导入目录中的图片文件
    
    Args:
        case_id: 案例ID
        directory_path: 要导入的目录路径
        recursive: 是否递归搜索子目录
        batch_size: 批处理大小
        background_tasks: 后台任务
        db: 数据库会话
        
    Returns:
        导入任务信息
    """
    from ..services import get_image_files_from_directory, batch_process_files
    
    # 验证案例存在
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")
    
    # 获取目录中的图像文件
    try:
        image_files = get_image_files_from_directory(directory_path, recursive)
        if not image_files:
            raise HTTPException(status_code=400, detail="目录中没有找到图像文件")
        
        # 如果文件数量较少，直接处理
        if len(image_files) <= 50:
            results = batch_process_files(db, case_id, image_files, batch_size)
            return {
                "status": "completed",
                "message": f"批量导入完成",
                "results": results
            }
        else:
            # 大量文件使用后台任务处理
            background_tasks.add_task(
                batch_process_files, 
                db, case_id, image_files, batch_size
            )
            return {
                "status": "processing", 
                "message": f"已启动后台批量导入任务，共 {len(image_files)} 个文件",
                "file_count": len(image_files),
                "estimated_time_minutes": len(image_files) // 100  # 估算时间：每分钟100张
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量导入失败: {str(e)}")

@router.post("/{case_id}/files/async-batch-import", response_model=dict, status_code=202)
async def async_batch_import_files_endpoint(
    case_id: int,
    directory_path: str,
    recursive: bool = True,
    max_workers: int = 4,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_master_db)
):
    """
    异步批量导入目录中的图片文件（高性能版本）
    
    Args:
        case_id: 案例ID
        directory_path: 要导入的目录路径  
        recursive: 是否递归搜索子目录
        max_workers: 最大工作线程数
        background_tasks: 后台任务
        db: 数据库会话
        
    Returns:
        导入任务信息
    """
    from ..services import get_image_files_from_directory, async_batch_process_files
    
    # 验证案例存在
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")
    
    # 获取目录中的图像文件
    try:
        image_files = get_image_files_from_directory(directory_path, recursive)
        if not image_files:
            raise HTTPException(status_code=400, detail="目录中没有找到图像文件")
        
        # 使用异步处理
        results = await async_batch_process_files(db, case_id, image_files, max_workers)
        
        return {
            "status": "completed",
            "message": f"异步批量导入完成",
            "results": results,
            "performance_note": f"使用了 {max_workers} 个工作线程"
        }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"异步批量导入失败: {str(e)}")

@router.get("/{case_id}/files/{file_id}/thumbnail")
def get_file_thumbnail(case_id: int, file_id: int, size: int = 300, db: Session = Depends(get_master_db)):
    """
    获取文件缩略图，优先使用预生成的缩略图

    Args:
        case_id: 案例ID
        file_id: 文件ID
        size: 缩略图尺寸（默认300px）

    Returns:
        JPEG格式的缩略图
    """
    try:
        # 获取文件信息
        file_info = get_file(db, case_id, file_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="文件未找到")

        # 检查是否为图片文件
        if not file_info.file_type or not file_info.file_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="文件不是图片类型")

        # 优先使用预生成的缩略图
        if file_info.thumbnail_small_path and Path(file_info.thumbnail_small_path).exists():
            thumbnail_path = Path(file_info.thumbnail_small_path)

            # 如果请求的尺寸与预生成的缩略图尺寸接近（300px），直接返回
            if size <= 350:  # 允许一定的尺寸容差
                try:
                    from fastapi.responses import FileResponse
                    return FileResponse(
                        path=thumbnail_path,
                        media_type="image/jpeg",
                        headers={
                            "Cache-Control": "public, max-age=3600",  # 缓存1小时
                            "Content-Disposition": f"inline; filename=thumbnail_{file_id}.jpg"
                        }
                    )
                except Exception as e:
                    logger.warning(f"返回预生成缩略图失败: {e}")

        # 如果没有预生成缩略图或需要不同尺寸，则动态生成
        # 检查原始文件是否存在
        original_path = Path(file_info.file_path)
        if not original_path.exists():
            raise HTTPException(status_code=404, detail="原始文件不存在")

        # 动态生成缩略图
        try:
            with Image.open(original_path) as img:
                # 创建副本以避免修改原图
                img_copy = img.copy()

                # 生成缩略图
                img_copy.thumbnail((size, size), Image.Resampling.LANCZOS)

                # 转换为RGB模式（如果需要）
                if img_copy.mode in ("RGBA", "P"):
                    img_copy = img_copy.convert("RGB")

                # 保存到内存中的字节流
                img_bytes = io.BytesIO()
                img_copy.save(img_bytes, format='JPEG', quality=85)
                img_bytes.seek(0)

                # 返回图片响应
                from fastapi.responses import StreamingResponse
                return StreamingResponse(
                    io.BytesIO(img_bytes.read()),
                    media_type="image/jpeg",
                    headers={
                        "Cache-Control": "public, max-age=3600",  # 缓存1小时
                        "Content-Disposition": f"inline; filename=thumbnail_{file_id}.jpg"
                    }
                )

        except Exception as e:
            logger.error(f"生成缩略图失败 {original_path}: {e}")
            raise HTTPException(status_code=500, detail="缩略图生成失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取缩略图失败: {e}")
        raise HTTPException(status_code=500, detail="获取缩略图失败")


def _file_matches_tags(file: schemas.File, tag_filters: dict) -> bool:
    """
    检查文件是否匹配标签筛选条件

    Args:
        file: 文件对象
        tag_filters: 标签筛选条件字典

    Returns:
        是否匹配所有筛选条件
    """
    if not file.tags:
        return False

    for tag_name, tag_value in tag_filters.items():
        if not _file_has_tag_value(file, tag_name, tag_value):
            return False

    return True


def _file_has_tag_value(file: schemas.File, tag_name: str, tag_value: str) -> bool:
    """
    检查文件是否包含指定的标签值

    Args:
        file: 文件对象
        tag_name: 标签名称
        tag_value: 标签值

    Returns:
        是否包含该标签值
    """
    if not file.tags:
        return False

    # 解析标签数据（可能是JSON字符串）
    import json
    tags_data = file.tags
    if isinstance(tags_data, str):
        try:
            tags_data = json.loads(tags_data)
        except (json.JSONDecodeError, TypeError):
            return False

    # 如果tags_data不是字典，尝试获取其属性
    if not isinstance(tags_data, dict):
        if hasattr(tags_data, 'dict'):
            tags_data = tags_data.dict()
        elif hasattr(tags_data, '__dict__'):
            tags_data = tags_data.__dict__
        else:
            return False

    # 在properties中查找
    if 'properties' in tags_data and tags_data['properties']:
        properties_dict = tags_data['properties']
        if isinstance(properties_dict, dict):
            for key, value in properties_dict.items():
                if key.lower() == tag_name.lower() and str(value).lower() == tag_value.lower():
                    return True

    # 在tags中查找
    if 'tags' in tags_data and tags_data['tags']:
        tags_dict = tags_data['tags']
        if isinstance(tags_dict, dict):
            # 在metadata中查找
            if 'metadata' in tags_dict and isinstance(tags_dict['metadata'], dict):
                metadata = tags_dict['metadata']
                for key, value in metadata.items():
                    if key.lower() == tag_name.lower() and str(value).lower() == tag_value.lower():
                        return True

            # 在cv中查找
            if 'cv' in tags_dict and isinstance(tags_dict['cv'], dict):
                cv = tags_dict['cv']
                for key, value in cv.items():
                    if key.lower() == tag_name.lower():
                        if isinstance(value, list):
                            return tag_value.lower() in [str(v).lower() for v in value]
                        else:
                            return str(value).lower() == tag_value.lower()

            # 在user标签中查找
            if 'user' in tags_dict and isinstance(tags_dict['user'], list):
                if tag_name.lower() == 'user':
                    return tag_value.lower() in [str(tag).lower() for tag in tags_dict['user']]

            # 在ai标签中查找
            if 'ai' in tags_dict and isinstance(tags_dict['ai'], list):
                if tag_name.lower() == 'ai':
                    return tag_value.lower() in [str(tag).lower() for tag in tags_dict['ai']]

    return False


@router.post("/{case_id}/files/{file_id}/reprocess", response_model=schemas.File)
def reprocess_file_metadata(
    case_id: int,
    file_id: int,
    db: Session = Depends(get_master_db)
):
    """
    重新处理文件元数据，提取完整的EXIF数据
    """
    from ..services.rule_engine import process_file_with_rules

    # 验证案例存在
    case = get_case(db, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="案例不存在")

    # 获取文件
    file = get_file(db, case_id, file_id)
    if not file:
        raise HTTPException(status_code=404, detail="文件不存在")

    try:
        # 重新处理文件，提取完整元数据
        logger.info(f"开始重新处理文件元数据: {file.file_name}")

        # 使用规则引擎重新处理文件
        tags_data = process_file_with_rules(db, case_id, file)

        # 更新文件的标签数据 - 使用案例数据库
        from ..database import get_case_db_session, get_case_database_path
        case_db_path = get_case_database_path(case_id)
        case_db = get_case_db_session(str(case_db_path))

        try:
            # 在案例数据库中查找文件
            file_obj = case_db.query(models.File).filter(models.File.id == file.id).first()
            if file_obj:
                import json
                file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
                case_db.commit()
                case_db.refresh(file_obj)
                logger.info(f"✅ 文件元数据重新处理完成: {file.file_name}")

                # 返回更新后的文件对象
                result = file_obj
            else:
                logger.error(f"在案例数据库中未找到文件: {file.id}")
                raise HTTPException(status_code=404, detail="在案例数据库中未找到文件")

        finally:
            case_db.close()

        return result

    except Exception as e:
        logger.error(f"重新处理文件元数据失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"重新处理失败: {str(e)}")


@router.post("/{case_id}/files/reprocess-all")
def reprocess_all_files_metadata(
    case_id: int,
    db: Session = Depends(get_master_db)
):
    """
    重新处理案例中所有文件的元数据
    """
    from ..services.rule_engine import process_file_with_rules

    # 验证案例存在
    case = get_case(db, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="案例不存在")

    try:
        # 获取案例中的所有文件
        files = get_files_for_case(db, case_id)

        processed_count = 0
        error_count = 0

        logger.info(f"开始重新处理案例 {case_id} 中的 {len(files)} 个文件")

        # 使用案例数据库会话
        from ..database import get_case_db_session, get_case_database_path
        case_db_path = get_case_database_path(case_id)
        case_db = get_case_db_session(str(case_db_path))

        try:
            for file in files:
                try:
                    # 重新处理文件
                    tags_data = process_file_with_rules(db, case_id, file)

                    # 在案例数据库中查找并更新文件
                    file_obj = case_db.query(models.File).filter(models.File.id == file.id).first()
                    if file_obj:
                        import json
                        file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
                        processed_count += 1
                        logger.info(f"✅ 已处理文件: {file.file_name}")
                    else:
                        logger.warning(f"在案例数据库中未找到文件: {file.file_name}")

                except Exception as e:
                    error_count += 1
                    logger.error(f"处理文件失败 {file.file_name}: {e}")

            # 提交所有更改
            case_db.commit()

        finally:
            case_db.close()

        result = {
            "message": f"批量重新处理完成",
            "total_files": len(files),
            "processed_count": processed_count,
            "error_count": error_count
        }

        logger.info(f"批量重新处理完成: {result}")
        return result

    except Exception as e:
        logger.error(f"批量重新处理失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量重新处理失败: {str(e)}")
