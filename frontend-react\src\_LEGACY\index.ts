// store/index.ts - Phase 6: 统一Store导出
// 提供所有store的统一访问点

// ============================================================================
// Store导出
// ============================================================================

// 核心UI状态管理
export { default as useUIStore } from './useUIStore';
export * from './useUIStore';

// 布局状态管理
export { default as useLayoutStore } from './useLayoutStore';
export * from './useLayoutStore';

// 选择状态管理
export { default as useSelectionStore } from './useSelectionStore';
export * from './useSelectionStore';

// 文件系统状态管理
export { default as useFileSystemStore } from './useFileSystemStore';
export * from './useFileSystemStore';

// 搜索状态管理 - Phase 7A
export { default as useSearchStore } from './useSearchStore';
export * from './useSearchStore';

// ============================================================================
// 向后兼容导出（Phase 6过渡期）
// ============================================================================

// 重新导出常用的选择器，保持向后兼容
export { usePanelVisibility, useLayoutSizes } from './useLayoutStore';
export { useFileSelection, useLegacySelection } from './useSelectionStore';
export { useCurrentDirectory, useFileList, useFileFilter } from './useFileSystemStore';
export { useLoadingState, useModalState, useNotifications, useTheme, useGallerySettings, useUISearchState } from './useUIStore';
export { useSearchCriteria, useSearchState, useSearchPerformance } from './useSearchStore';

// Phase 7B: 新增后端集成相关的选择器
export {
  useFileSyncStatus,
  useFileIdMapping,
  useCaseManagement
} from './useFileSyncStore';
export {
  useCurrentFileTags,
  useTagOperations,
  useMetadataErrors
} from './useMetadataStore';

// Phase 7C: 新增案例管理相关的选择器
export {
  useCurrentCase,
  useCaseList,
  useFetchCases,
  useCaseOperations,
  useCaseErrors
} from './useCaseStore';

// Phase 8A: 新增AI服务状态管理相关的选择器
export {
  useRateLimitStatus,
  useServiceHealth,
  useAiErrors,
  usePerformanceMetrics
} from './useApiStatusStore';

// ============================================================================
// 复合选择器（跨store组合）
// ============================================================================

import { useLayoutStore } from './useLayoutStore';
import { useSelectionStore } from './useSelectionStore';
import { useFileSystemStore } from './useFileSystemStore';
import { useUIStore } from './useUIStore';
import { shallow } from 'zustand/shallow';

// 文件系统状态选择器（向后兼容）- ✅ 添加shallow比较防止不必要重渲染
export const useFileSystemState = () => {
  const { currentDirectory, setCurrentDirectory } = useFileSystemStore(
    (state) => ({
      currentDirectory: state.currentDirectory,
      setCurrentDirectory: state.setCurrentDirectory,
    }),
    shallow
  );
  const { selectedFile, setSelectedFile } = useSelectionStore(
    (state) => ({
      selectedFile: state.selectedFile,
      setSelectedFile: state.setSelectedFile,
    }),
    shallow
  );

  return {
    currentDirectory,
    selectedFile,
    setCurrentDirectory,
    setSelectedFile,
  };
};
