import React, { useState } from 'react';
import { apiClient } from '@/lib/apiClient';
import { BaseModal, modalButtonStyles, modalInputStyles } from '../ui/BaseModal';

interface CreateCaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export function CreateCaseModal({ isOpen, onClose, onSuccess }: CreateCaseModalProps) {
  const [caseName, setCaseName] = useState('');
  const [caseDescription, setCaseDescription] = useState('');
  const [isNameFocused, setIsNameFocused] = useState(false);
  const [isDescriptionFocused, setIsDescriptionFocused] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleCreate = async () => {
    if (!caseName.trim()) return;

    setIsCreating(true);
    setError(null);

    try {
      await apiClient.createCase({
        case_name: caseName.trim(),
        description: caseDescription.trim() || undefined
      });

      // 成功创建后清空表单并关闭模态框
      setCaseName('');
      setCaseDescription('');
      onSuccess?.();
      onClose();
    } catch (err: any) {
      console.error('Failed to create case:', err);
      setError(err.error || '创建档案库失败，请重试');
    } finally {
      setIsCreating(false);
    }
  };

  const handleReturn = () => {
    setCaseName('');
    setCaseDescription('');
    onClose();
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleReturn}
      title="新建档案库"
      size="create-case"
      closeOnOverlayClick={!isCreating}
      closeOnEscape={!isCreating}
    >
      <div className="p-6 h-full flex flex-col">

        {/* 错误提示 */}
        {error && (
          <div className="mb-4 p-3 bg-red-500 bg-opacity-20 border border-red-500 rounded text-red-400 text-sm">
            {error}
          </div>
        )}

        {/* 表单内容 - 居中布局 */}
        <div className="flex-1 flex flex-col justify-center items-center space-y-6">
          <div className="w-full max-w-md space-y-4">
            {/* 档案库名称输入框 */}
            <div>
              <label className="block text-sm font-medium text-mizzy-text-title mb-2">
                档案库名称 *
              </label>
              <input
                type="text"
                value={caseName}
                onChange={(e) => setCaseName(e.target.value)}
                placeholder="请输入档案库名称"
                className={modalInputStyles.base}
                disabled={isCreating}
              />
            </div>

            {/* 档案库简介输入框 */}
            <div>
              <label className="block text-sm font-medium text-mizzy-text-title mb-2">
                描述 (可选)
              </label>
              <textarea
                value={caseDescription}
                onChange={(e) => setCaseDescription(e.target.value)}
                placeholder="请输入档案库描述"
                rows={4}
                className={modalInputStyles.textarea}
                disabled={isCreating}
              />
            </div>
          </div>
        </div>

        {/* 底部按钮区域 */}
        <div className="border-t border-mizzy-border-base pt-4 mt-6">
          <div className="flex justify-between items-center">
            {/* 左侧：上传按钮 */}
            <button
              onClick={() => {
                // TODO: 实现上传逻辑
                console.log('上传文件按钮点击');
              }}
              className={modalButtonStyles.secondary}
            >
              📁 上传文件
            </button>

            {/* 右侧：取消和创建按钮 */}
            <div className="flex gap-3">
              <button
                onClick={handleReturn}
                disabled={isCreating}
                className={modalButtonStyles.secondary}
              >
                取消
              </button>
              <button
                onClick={handleCreate}
                disabled={!caseName.trim() || isCreating}
                className={modalButtonStyles.primary}
              >
                {isCreating ? '创建中...' : '创建档案库'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </BaseModal>
  );
}
