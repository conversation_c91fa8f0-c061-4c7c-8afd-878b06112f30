/* MainLayout.css - Four-Column Layout Styles */
/* Phase 5A: 四列布局基础样式 */

/* ============================================================================
 * 主布局容器
 * ============================================================================ */

.main-layout {
  display: grid;
  height: 100vh;
  width: 100vw;
  overflow: hidden;

  /* 四列网格定义 */
  grid-template-columns:
    250px      /* Catalog: 固定宽度 */
    2fr        /* Gallery: 弹性宽度 (2/3) */
    1fr        /* Workbench: 弹性宽度 (1/3) */
    300px;     /* Info: 固定宽度 */

  grid-template-areas: "catalog gallery workbench info";

  /* 颜色方案 */
  background-color: #040709; /* 中央区域背景 */
}

/* 当Workbench隐藏时，Gallery占据全部中央空间 */
.main-layout:not(:has(.workbench-column)) {
  grid-template-columns:
    250px      /* Catalog: 固定宽度 */
    1fr        /* Gallery: 占据全部中央空间 */
    300px;     /* Info: 固定宽度 */

  grid-template-areas: "catalog gallery info";
}

/* 当Catalog隐藏时 */
.main-layout:not(:has(.catalog-column)) {
  grid-template-columns:
    2fr        /* Gallery: 弹性宽度 (2/3) */
    1fr        /* Workbench: 弹性宽度 (1/3) */
    300px;     /* Info: 固定宽度 */

  grid-template-areas: "gallery workbench info";
}

/* 当Info隐藏时 */
.main-layout:not(:has(.info-column)) {
  grid-template-columns:
    250px      /* Catalog: 固定宽度 */
    2fr        /* Gallery: 弹性宽度 (2/3) */
    1fr;       /* Workbench: 弹性宽度 (1/3) */

  grid-template-areas: "catalog gallery workbench";
}

/* ============================================================================
 * 列布局
 * ============================================================================ */

.layout-column {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.catalog-column {
  grid-area: catalog;
  background-color: #191012; /* 侧边栏背景 */
  border-right: 1px solid #333;
}

.gallery-column {
  grid-area: gallery;
  background-color: #040709; /* 中央区域背景 */
}

.workbench-column {
  grid-area: workbench;
  background-color: #040709; /* 中央区域背景 */
  border-left: 1px solid #333;
}

.info-column {
  grid-area: info;
  background-color: #191012; /* 侧边栏背景 */
  border-left: 1px solid #333;
}

/* ============================================================================
 * 面板通用样式
 * ============================================================================ */

.catalog-panel,
.gallery-panel,
.workbench-panel,
.info-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #333;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Phase 7C: 案例切换器容器样式 */
.case-switcher-container {
  width: 100%;
}

.panel-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #A49F9A; /* 文本颜色 */
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  color: #A49F9A; /* 文本颜色 */
}

.panel-content p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* ============================================================================
 * 响应式调整
 * ============================================================================ */

/* 小屏幕适配 */
@media (max-width: 1200px) {
  .main-layout {
    grid-template-columns:
      200px      /* Catalog: 缩小宽度 */
      2fr        /* Gallery: 弹性宽度 */
      1fr        /* Workbench: 弹性宽度 */
      250px;     /* Info: 缩小宽度 */
  }
}

@media (max-width: 900px) {
  .main-layout {
    grid-template-columns:
      150px      /* Catalog: 进一步缩小 */
      1fr        /* Gallery: 占据更多空间 */
      200px;     /* Info: 进一步缩小 */

    grid-template-areas: "catalog gallery info";
  }

  /* 在小屏幕上隐藏Workbench */
  .workbench-column {
    display: none;
  }
}

/* ============================================================================
 * 滚动条样式
 * ============================================================================ */

.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(164, 159, 154, 0.3);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(164, 159, 154, 0.5);
}

/* ============================================================================
 * Phase 5B: Catalog面板特定样式
 * ============================================================================ */

.directory-selection {
  margin-bottom: 24px;
}

.open-directory-btn {
  width: 100%;
  padding: 12px 16px;
  background-color: #040709;
  border: 1px solid #A49F9A;
  color: #A49F9A;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.open-directory-btn:hover {
  background-color: #A49F9A;
  color: #040709;
  border-color: #A49F9A;
}

.open-directory-btn:active {
  transform: translateY(1px);
}

.tag-filters {
  border-top: 1px solid #333;
  padding-top: 16px;
}

.current-directory {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(164, 159, 154, 0.1);
  border-radius: 4px;
  font-size: 12px;
  color: #A49F9A;
  word-break: break-all;
}

.file-type-tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-type-tag {
  width: 100%;
}

.loading-state,
.error-state,
.empty-state {
  padding: 16px;
  text-align: center;
  font-size: 14px;
  color: #A49F9A;
}

.error-state {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  border-radius: 4px;
}

.empty-state {
  opacity: 0.7;
  font-style: italic;
}

/* ============================================================================
 * Phase 5D: 可调整面板样式
 * ============================================================================ */

.panel-group-horizontal,
.panel-group-vertical {
  height: 100%;
  width: 100%;
}

.panel-catalog,
.panel-center,
.panel-gallery,
.panel-workbench,
.panel-info {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 调整手柄样式 */
.panel-resize-handle {
  background-color: #333;
  transition: background-color 0.2s ease;
  position: relative;
}

.panel-resize-handle:hover {
  background-color: #A49F9A;
}

.panel-resize-handle:active {
  background-color: #A49F9A;
}

/* 水平调整手柄 */
.panel-resize-handle:not(.vertical) {
  width: 2px;
  cursor: col-resize;
}

.panel-resize-handle:not(.vertical):hover {
  width: 4px;
}

/* 垂直调整手柄 */
.panel-resize-handle.vertical {
  height: 2px;
  cursor: row-resize;
}

.panel-resize-handle.vertical:hover {
  height: 4px;
}

/* 调整手柄的视觉指示器 */
.panel-resize-handle::before {
  content: '';
  position: absolute;
  background-color: rgba(164, 159, 154, 0.3);
  transition: all 0.2s ease;
  opacity: 0;
}

.panel-resize-handle:hover::before {
  opacity: 1;
}

/* 水平调整手柄的指示器 */
.panel-resize-handle:not(.vertical)::before {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 20px;
}

/* 垂直调整手柄的指示器 */
.panel-resize-handle.vertical::before {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 1px;
}

/* 确保面板内容正确填充 */
.panel-catalog > *,
.panel-gallery > *,
.panel-workbench > *,
.panel-info > * {
  height: 100%;
  width: 100%;
}
