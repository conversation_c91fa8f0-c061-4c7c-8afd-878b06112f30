// 6.4.1 图像上传弹窗组件 - 纯本地路径导入版本
import React, { useState } from 'react';
import type { FilePathUploadResult } from '../../services/imageUpload';
import { filePathUploadService } from '../../services/imageUpload';
import { useCaseStore } from '../../store/useCaseStore';
import { BaseModal, modalButtonStyles } from '../ui/BaseModal';
import { Icon } from '../ui/Icon';

interface ImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadComplete?: (result: FilePathUploadResult) => void;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
}

export function ImageUploadModal({ isOpen, onClose, onUploadComplete }: ImageUploadModalProps) {
  // ✅ 修复无限循环：直接选择单个值，避免对象解构
  const currentCaseId = useCaseStore(state => state.currentCaseId);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [overallProgress, setOverallProgress] = useState(0);
  const [selectedFilePaths, setSelectedFilePaths] = useState<string[]>([]);
  const [uploadResult, setUploadResult] = useState<FilePathUploadResult | null>(null);

  if (!isOpen) return null;

  const handleSelectFiles = async () => {
    try {
      console.log('📁 用户点击选择文件按钮');
      const filePaths = await filePathUploadService.selectImagePaths();
      console.log('📁 选择的文件路径:', filePaths);

      setSelectedFilePaths(filePaths);
      setUploadResult(null);

      // 初始化进度状态
      const initialProgress = filePaths.map(path => ({
        fileName: path.split(/[/\\]/).pop() || path,
        progress: 0,
        status: 'pending' as const
      }));
      setUploadProgress(initialProgress);
    } catch (error) {
      console.error('❌ 选择文件失败:', error);
      alert(`选择文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const handleUpload = async () => {
    if (!currentCaseId || selectedFilePaths.length === 0) return;

    setIsUploading(true);
    setOverallProgress(0);

    try {
      console.log('📁 开始上传文件到档案库:', currentCaseId);
      const result = await filePathUploadService.importImagesByPath({
        caseId: currentCaseId,
        filePaths: selectedFilePaths,
        onProgress: (progress) => {
          setOverallProgress(progress);
        },
        onFileProgress: (fileName, progress) => {
          setUploadProgress(prev => prev.map(item => 
            item.fileName === fileName 
              ? { 
                  ...item, 
                  progress, 
                  status: progress === 100 ? 'completed' : 'uploading' 
                }
              : item
          ));
        }
      });

      setUploadResult(result);
      onUploadComplete?.(result);
      
      if (result.success) {
        console.log('✅ 文件上传成功');
        // 成功后清理状态
        setTimeout(() => {
          handleClose();
        }, 2000);
      }
    } catch (error) {
      console.error('❌ 上传失败:', error);
      setUploadResult({
        success: false,
        uploadedFiles: [],
        failedFiles: selectedFilePaths.map(path => ({
          fileName: path.split(/[/\\]/).pop() || path,
          error: error instanceof Error ? error.message : '上传失败'
        })),
        totalFiles: selectedFilePaths.length,
        message: '上传过程中发生错误'
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    if (!isUploading) {
      setSelectedFilePaths([]);
      setUploadProgress([]);
      setOverallProgress(0);
      setUploadResult(null);
      onClose();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title="图像上传"
      size="upload"
      closeOnOverlayClick={!isUploading}
      closeOnEscape={!isUploading}
    >
      <div className="p-6 space-y-4">

        {/* 文件选择区域 */}
        {selectedFilePaths.length === 0 && (
          <div style={{
            border: '2px dashed var(--color-border-primary)',
            borderRadius: 'var(--border-radius-md)',
            padding: '48px 24px',
            textAlign: 'center',
            backgroundColor: 'var(--color-surface-secondary)'
          }}>
            <Icon name="upload" size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
            <p style={{
              fontSize: 'var(--font-size-body)',
              color: 'var(--color-text-secondary)',
              margin: '0 0 16px 0'
            }}>
              选择要上传的图像文件
            </p>
            <p style={{
              fontSize: '12px',
              color: 'var(--color-text-disabled)',
              margin: '0 0 24px 0'
            }}>
              支持格式：JPG, PNG, TIFF
            </p>
            <button
              onClick={handleSelectFiles}
              disabled={isUploading}
              className={modalButtonStyles.primary}
            >
              选择文件
            </button>
          </div>
        )}

        {/* 文件列表 */}
        {selectedFilePaths.length > 0 && (
          <div style={{
            maxHeight: '300px',
            overflowY: 'auto',
            border: '1px solid var(--color-border-primary)',
            borderRadius: 'var(--border-radius-md)',
            padding: '16px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '16px'
            }}>
              <span style={{
                fontSize: 'var(--font-size-body)',
                color: 'var(--color-text-title)',
                fontWeight: 'var(--font-weight-medium)'
              }}>
                已选择 {selectedFilePaths.length} 个文件
              </span>
              <button
                onClick={handleSelectFiles}
                disabled={isUploading}
                className={modalButtonStyles.secondary}
              >
                重新选择
              </button>
            </div>

            {selectedFilePaths.map((filePath, index) => {
              const fileName = filePath.split(/[/\\]/).pop() || filePath;
              const progress = uploadProgress.find(p => p.fileName === fileName);
              return (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '8px 0',
                  borderBottom: index < selectedFilePaths.length - 1 ? '1px solid var(--color-border-primary)' : 'none'
                }}>
                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontSize: '12px',
                      color: 'var(--color-text-body)',
                      marginBottom: '4px'
                    }}>
                      {fileName}
                    </div>
                    <div style={{
                      fontSize: '10px',
                      color: 'var(--color-text-secondary)'
                    }}>
                      {filePath}
                    </div>
                    {progress && progress.progress > 0 && (
                      <div style={{
                        width: '100%',
                        height: '4px',
                        backgroundColor: 'var(--color-surface-button)',
                        borderRadius: '2px',
                        marginTop: '4px',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          width: `${progress.progress}%`,
                          height: '100%',
                          backgroundColor: progress.status === 'completed' ? '#4CAF50' : 
                                         progress.status === 'failed' ? '#F44336' : 
                                         'var(--color-accent-primary)',
                          transition: 'width 0.3s ease'
                        }} />
                      </div>
                    )}
                  </div>
                  <div style={{
                    fontSize: '10px',
                    color: progress?.status === 'completed' ? '#4CAF50' :
                           progress?.status === 'failed' ? '#F44336' :
                           progress?.status === 'uploading' ? 'var(--color-accent-primary)' :
                           'var(--color-text-secondary)',
                    marginLeft: '12px'
                  }}>
                    {progress?.status === 'completed' ? '完成' :
                     progress?.status === 'failed' ? '失败' :
                     progress?.status === 'uploading' ? `${progress.progress}%` :
                     '等待'}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 上传结果 */}
        {uploadResult && (
          <div style={{
            padding: '16px',
            backgroundColor: uploadResult.success ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
            border: `1px solid ${uploadResult.success ? '#4CAF50' : '#F44336'}`,
            borderRadius: 'var(--border-radius-md)'
          }}>
            <div style={{
              fontSize: '12px',
              color: uploadResult.success ? '#4CAF50' : '#F44336',
              fontWeight: 'var(--font-weight-medium)',
              marginBottom: '8px'
            }}>
              {uploadResult.success ? '上传完成' : '上传失败'}
            </div>
            <div style={{
              fontSize: '10px',
              color: 'var(--color-text-secondary)'
            }}>
              成功: {uploadResult.uploadedFiles.length} 个，失败: {uploadResult.failedFiles.length} 个
            </div>
            {uploadResult.failedFiles.length > 0 && (
              <div style={{
                fontSize: '10px',
                color: '#F44336',
                marginTop: '8px'
              }}>
                {uploadResult.failedFiles.map(f => f.error).join('; ')}
              </div>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px',
          paddingTop: '16px',
          borderTop: '1px solid var(--color-border-primary)'
        }}>
          <button
            onClick={handleClose}
            disabled={isUploading}
            className={modalButtonStyles.secondary}
          >
            {isUploading ? '上传中...' : '取消'}
          </button>
          {selectedFilePaths.length > 0 && !uploadResult?.success && (
            <button
              onClick={handleUpload}
              disabled={isUploading || !currentCaseId}
              className={modalButtonStyles.primary}
            >
              {isUploading ? `上传中 ${Math.round(overallProgress)}%` : '开始上传'}
            </button>
          )}
        </div>
      </div>
    </BaseModal>
  );
}
