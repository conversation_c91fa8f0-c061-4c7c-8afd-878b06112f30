// SearchPanel.tsx - Phase 7A: 高级搜索面板组件
// 集成到CatalogPanel中的搜索界面

import React, { useState } from 'react';
import {
  useSearchCriteria,
  useSearchState,
  FIELD_CONFIG,
  OPERATOR_LABELS
} from '@/store/useSearchStore';
import type { SearchCriterion } from '@/store/useSearchStore';
import { SearchCriterionList } from './SearchCriterionPill';
// Phase 8A: 导入自然语言搜索
import { NaturalLanguageSearch } from './NaturalLanguageSearch';
import type { StructuredQuery } from '@/lib/aiClient';
import './SearchPanel.css';

// ============================================================================
// 类型定义
// ============================================================================

interface SearchPanelProps {
  className?: string;
  showPresets?: boolean;
  maxCriteriaDisplay?: number;
}

// Phase 8A: 搜索模式类型
type SearchMode = 'advanced' | 'natural';

type FieldType = keyof typeof FIELD_CONFIG;
type OperatorType = SearchCriterion['operator'];

// ============================================================================
// 搜索条件构建器组件
// ============================================================================

const SearchCriterionBuilder: React.FC<{
  onAdd: (criterion: Omit<SearchCriterion, 'id'>) => void;
}> = ({ onAdd }) => {
  const [field, setField] = React.useState<FieldType>('fileName');
  const [operator, setOperator] = React.useState<OperatorType>('contains');
  const [value, setValue] = React.useState<string>('');
  const [caseSensitive, setCaseSensitive] = React.useState(false);

  // 根据字段类型更新可用操作符
  React.useEffect(() => {
    const availableOperators = FIELD_CONFIG[field].operators;
    if (!availableOperators.includes(operator)) {
      setOperator(availableOperators[0]);
    }
  }, [field, operator]);

  const handleAdd = () => {
    if (!value.trim()) return;

    let processedValue: string | number | Date = value.trim();

    // 根据字段类型处理值
    switch (field) {
      case 'fileSize':
        const numValue = parseFloat(value);
        if (isNaN(numValue)) return;
        processedValue = numValue;
        break;

      case 'modifiedTime':
        const dateValue = new Date(value);
        if (isNaN(dateValue.getTime())) return;
        processedValue = dateValue;
        break;

      case 'fileName':
      case 'fileType':
      default:
        processedValue = value.trim();
        break;
    }

    const criterion: Omit<SearchCriterion, 'id'> = {
      field,
      operator,
      value: processedValue,
      ...(FIELD_CONFIG[field].type === 'text' && { caseSensitive }),
    };

    onAdd(criterion);
    setValue('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAdd();
    }
  };

  const fieldConfig = FIELD_CONFIG[field];
  const availableOperators = fieldConfig.operators;

  return (
    <div className="search-criterion-builder">
      <div className="builder-row">
        <select
          value={field}
          onChange={(e) => setField(e.target.value as FieldType)}
          className="field-select"
          aria-label="Select field to search"
        >
          {Object.entries(FIELD_CONFIG).map(([key, config]) => (
            <option key={key} value={key}>
              {config.icon} {config.label}
            </option>
          ))}
        </select>

        <select
          value={operator}
          onChange={(e) => setOperator(e.target.value as OperatorType)}
          className="operator-select"
          aria-label="Select search operator"
        >
          {availableOperators.map((op) => (
            <option key={op} value={op}>
              {OPERATOR_LABELS[op]}
            </option>
          ))}
        </select>

        <div className="value-input-container">
          {fieldConfig.type === 'date' ? (
            <input
              type="datetime-local"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              className="value-input date-input"
              aria-label="Enter search value"
            />
          ) : (
            <input
              type={fieldConfig.type === 'number' ? 'number' : 'text'}
              value={value}
              onChange={(e) => setValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={fieldConfig.placeholder}
              className="value-input"
              aria-label="Enter search value"
            />
          )}

          {fieldConfig.type === 'text' && (
            <label className="case-sensitive-toggle">
              <input
                type="checkbox"
                checked={caseSensitive}
                onChange={(e) => setCaseSensitive(e.target.checked)}
              />
              <span className="toggle-label">Aa</span>
            </label>
          )}
        </div>

        <button
          onClick={handleAdd}
          disabled={!value.trim()}
          className="add-button"
          title="Add search criterion"
          aria-label="Add search criterion"
        >
          ➕
        </button>
      </div>
    </div>
  );
};

// ============================================================================
// 预设搜索组件
// ============================================================================

const SearchPresets: React.FC<{
  onApplyPreset: (preset: 'images' | 'large-files' | 'recent' | 'old') => void;
}> = ({ onApplyPreset }) => {
  const presets = [
    { id: 'images' as const, label: '🖼️ Images', description: 'JPG, PNG files' },
    { id: 'large-files' as const, label: '📦 Large Files', description: '> 10MB' },
    { id: 'recent' as const, label: '🕒 Recent', description: 'Last 7 days' },
    { id: 'old' as const, label: '📅 Old Files', description: '> 30 days' },
  ];

  return (
    <div className="search-presets">
      <h4 className="presets-title">Quick Searches</h4>
      <div className="presets-grid">
        {presets.map((preset) => (
          <button
            key={preset.id}
            onClick={() => onApplyPreset(preset.id)}
            className="preset-button"
            title={preset.description}
          >
            <span className="preset-label">{preset.label}</span>
            <span className="preset-description">{preset.description}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

// ============================================================================
// 主搜索面板组件
// ============================================================================

export const SearchPanel: React.FC<SearchPanelProps> = ({
  className = '',
  showPresets = true,
  maxCriteriaDisplay = 5,
}) => {
  const { criteria, addCriterion, removeCriterion, clearSearch } = useSearchCriteria();
  const { isActive, resultCount, isSearchPanelExpanded, toggleSearchPanel } = useSearchState();

  // Phase 8A: 搜索模式状态
  const [searchMode, setSearchMode] = useState<SearchMode>('advanced');

  const handleApplyPreset = (preset: 'images' | 'large-files' | 'recent' | 'old') => {
    // 这里应该调用useSearchStore的applyPresetSearch方法
    // 但为了简化，我们直接添加相应的条件
    clearSearch(); // 先清除现有条件

    switch (preset) {
      case 'images':
        addCriterion({ field: 'fileType', operator: 'contains', value: 'jpg' });
        break;
      case 'large-files':
        addCriterion({ field: 'fileSize', operator: '>=', value: 10 * 1024 * 1024 });
        break;
      case 'recent':
        addCriterion({
          field: 'modifiedTime',
          operator: '>=',
          value: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        });
        break;
      case 'old':
        addCriterion({
          field: 'modifiedTime',
          operator: '<=',
          value: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        });
        break;
    }
  };

  // Phase 8A: 自然语言搜索处理
  const handleNaturalLanguageSearch = (structuredQuery: StructuredQuery) => {
    // console.log('🔍 Phase 8A: 执行自然语言搜索:', structuredQuery); // [CLEANED]

    // 清除现有条件
    clearSearch();

    // 将结构化查询转换为搜索条件
    if (structuredQuery.filters.metadata_filters) {
      Object.entries(structuredQuery.filters.metadata_filters).forEach(([key, value]) => {
        addCriterion({
          field: 'fileName', // 简化处理，实际应该根据key映射到正确的字段
          operator: 'contains',
          value: String(value)
        });
      });
    }

    if (structuredQuery.filters.quality_range) {
      const { min, max } = structuredQuery.filters.quality_range;
      if (min !== undefined) {
        addCriterion({
          field: 'fileSize', // 简化处理，实际应该有质量字段
          operator: '>=',
          value: min
        });
      }
    }

    // console.log('✅ Phase 8A: 自然语言搜索条件已应用'); // [CLEANED]
  };

  return (
    <div className={`search-panel ${className}`}>
      <div className="search-panel-header">
        <button
          onClick={toggleSearchPanel}
          className="panel-toggle"
          aria-expanded={isSearchPanelExpanded}
          aria-label="Toggle search panel"
        >
          <span className="toggle-icon">
            {isSearchPanelExpanded ? '🔽' : '▶️'}
          </span>
          <span className="panel-title">
            {searchMode === 'natural' ? 'AI Search' : 'Advanced Search'}
          </span>
          {isActive && (
            <span className="active-indicator" title={`${criteria.length} active criteria`}>
              ({criteria.length})
            </span>
          )}
        </button>

        {/* Phase 8A: 搜索模式切换 */}
        <div className="search-mode-toggle">
          <button
            className={`mode-button ${searchMode === 'advanced' ? 'mode-button--active' : ''}`}
            onClick={() => setSearchMode('advanced')}
            title="高级搜索"
          >
            🔧
          </button>
          <button
            className={`mode-button ${searchMode === 'natural' ? 'mode-button--active' : ''}`}
            onClick={() => setSearchMode('natural')}
            title="AI搜索"
          >
            🧠
          </button>
        </div>

        {isActive && (
          <div className="result-count">
            {resultCount} results
          </div>
        )}
      </div>

      {isSearchPanelExpanded && (
        <div className="search-panel-content">
          {searchMode === 'natural' ? (
            /* Phase 8A: 自然语言搜索界面 */
            <NaturalLanguageSearch
              onSearchExecute={handleNaturalLanguageSearch}
              className="natural-search-integration"
              placeholder="用自然语言描述你要找的内容..."
            />
          ) : (
            /* 传统高级搜索界面 */
            <>
              <SearchCriterionBuilder onAdd={addCriterion} />

              <SearchCriterionList
                criteria={criteria}
                onRemove={removeCriterion}
                onClearAll={clearSearch}
                maxDisplayCount={maxCriteriaDisplay}
                className="active-criteria"
              />

              {showPresets && (
                <SearchPresets onApplyPreset={handleApplyPreset} />
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchPanel;
