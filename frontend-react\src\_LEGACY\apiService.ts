// apiService.ts - 新架构API服务层
// 专门用于档案库管理和文件导入功能

import { apiClient } from '../lib/apiClient';

// ============================================================================
// 类型定义 - 基于后端技术侦察报告
// ============================================================================

export interface ICase {
  id: number;
  case_name: string;
  description: string | null;
  created_at: string; // ISO 8601 格式
  status: 'active' | 'deleted' | 'permanently_deleted';
  deleted_at: string | null;
  files: IFile[]; // 关联的文件列表
  
  // 封面相关字段
  cover_image_url: string | null;
  cover_type: 'manual' | 'automatic' | 'placeholder';
  cover_source_file_id: number | null;
  cover_needs_attention: boolean;
  cover_updated_at: string | null;
}

export interface IFile {
  id: number;
  case_id: number;
  file_name: string;
  file_type: string | null; // MIME类型，如 "image/jpeg"
  file_path: string;
  thumbnail_small_path: string | null;
  width: number | null;
  height: number | null;
  created_at: string; // ISO 8601 格式
  taken_at: string | null; // 拍摄时间
  
  // 图像质量分析字段
  quality_score: number | null;
  sharpness: number | null;
  brightness: number | null;
  dynamic_range: number | null;
  num_faces: number | null;
  face_sharpness: number | null;
  face_quality: number | null;
  cluster_id: number | null;
  phash: string | null; // 感知哈希值
  group_id: string | null;
  frame_number: number | null;
  isCurrentCoverSource: boolean;
  
  // 标签系统字段
  tags: IFileTags | null;
  
  // AI向量字段
  vector_image: number[] | null;
  vector_text: number[] | null;
}

export interface IFileTags {
  properties?: {
    filename?: string;
    qualityScore?: number;
    hash?: string;
    fileSize?: number;
  };
  tags?: {
    metadata?: {
      camera?: string;
      lens?: string;
      project?: string;
      photographer?: string;
      captureDate?: string;
    };
    cv?: {
      objects?: string[];
      faces?: number;
      scene?: string;
    };
    user?: string[]; // 用户自定义标签
    ai?: string[];   // AI生成的标签
  };
}

export interface ICreateCasePayload {
  case_name: string;
  description?: string | null;
}

export interface ICaseStatistics {
  id: number;
  case_name: string;
  description: string | null;
  created_at: string;
  file_count: number;
  cover_image_url: string | null;
  cover_type: 'manual' | 'automatic' | 'placeholder';
  last_updated: number; // timestamp
  status: 'active' | 'deleted' | 'permanently_deleted';
}

export interface IArchiveManagerState {
  currentCaseId: number | null;
  availableCases: ICaseStatistics[];
  isLoading: boolean;
  error: string | null;
}

// ============================================================================
// API服务函数
// ============================================================================

/**
 * 获取所有档案库列表
 */
export const getAllCases = async (): Promise<ICase[]> => {
  try {
    console.log('🔍 开始获取档案库列表...');
    const cases = await apiClient.getCases();
    console.log('✅ 档案库列表获取成功:', cases);
    return cases as ICase[];
  } catch (error) {
    console.error('❌ 获取档案库列表失败:', error);
    throw error;
  }
};

/**
 * 获取档案库的详细统计信息
 * 包括：封面图、档案库名称、文件数量、创建时间、简介
 */
export const getCaseStatistics = async (caseId: number): Promise<ICaseStatistics> => {
  try {
    console.log(`📊 API服务: 获取档案库统计信息 ${caseId}`);
    const caseDetails = await apiClient.getCase(caseId);

    // 计算统计信息
    const statistics: ICaseStatistics = {
      id: caseDetails.id,
      case_name: caseDetails.case_name,
      description: caseDetails.description,
      created_at: caseDetails.created_at,
      file_count: caseDetails.files?.length || 0,
      cover_image_url: caseDetails.cover_image_url,
      cover_type: caseDetails.cover_type || 'placeholder',
      last_updated: caseDetails.files?.length > 0
        ? Math.max(...caseDetails.files.map(f => new Date(f.created_at).getTime()))
        : new Date(caseDetails.created_at).getTime(),
      status: caseDetails.status
    };

    return statistics;
  } catch (error) {
    console.error(`❌ 获取档案库统计信息失败 ${caseId}:`, error);
    throw error;
  }
};

/**
 * 获取指定档案库的详细信息（包含文件列表）
 */
export const getCaseDetails = async (caseId: number): Promise<ICase> => {
  try {
    // console.log(`📋 API服务: 获取档案库详情 ${caseId}`); // [CLEANED]
    const caseData = await apiClient.getCase(caseId);
    return caseData as ICase;
  } catch (error) {
    console.error(`❌ 获取档案库详情失败 ${caseId}:`, error);
    throw error;
  }
};

/**
 * 创建新档案库
 */
export const createNewCase = async (payload: ICreateCasePayload): Promise<ICase> => {
  try {
    // console.log('📋 API服务: 创建新档案库', payload); // [CLEANED]
    const newCase = await apiClient.createCase(payload.case_name, payload.description);
    return newCase as ICase;
  } catch (error) {
    console.error('❌ 创建档案库失败:', error);
    throw error;
  }
};

/**
 * 切换当前活动档案库
 * 这个函数会更新应用状态，将指定档案库设为当前工作档案库
 */
export const switchToCase = async (caseId: number): Promise<ICase> => {
  try {
    console.log(`🔄 API服务: 切换到档案库 ${caseId}`);

    // 获取档案库详细信息
    const caseDetails = await getCaseDetails(caseId);

    // 这里可以添加额外的切换逻辑，比如：
    // - 更新本地存储的当前档案库ID
    // - 清理之前档案库的缓存数据
    // - 预加载新档案库的基本数据

    localStorage.setItem('currentCaseId', caseId.toString());
    console.log(`✅ 档案库切换成功: ${caseDetails.case_name}`);

    return caseDetails;
  } catch (error) {
    console.error(`❌ 切换档案库失败 ${caseId}:`, error);
    throw error;
  }
};

/**
 * 获取当前活动档案库ID
 */
export const getCurrentCaseId = (): number | null => {
  const stored = localStorage.getItem('currentCaseId');
  return stored ? parseInt(stored, 10) : null;
};

/**
 * 通过文件路径将图像导入到指定档案库
 * @param caseId 档案库ID
 * @param filePaths 要导入的本地文件路径数组
 * @returns 成功导入的文件信息对象数组
 */
export const importFilesByPath = async (caseId: number, filePaths: string[]): Promise<IFile[]> => {
  try {
    // console.log(`📁 API服务: 批量导入文件到档案库 ${caseId}`, filePaths); // [CLEANED]
    const importedFiles = await apiClient.importFilesByPath(caseId, filePaths);
    return importedFiles as IFile[];
  } catch (error) {
    console.error(`❌ 批量导入文件失败 ${caseId}:`, error);
    throw error;
  }
};

/**
 * 获取指定档案库的文件列表
 */
export const getCaseFiles = async (caseId: number): Promise<IFile[]> => {
  try {
    // console.log(`📁 API服务: 获取档案库文件列表 ${caseId}`); // [CLEANED]
    const caseData = await getCaseDetails(caseId);
    return caseData.files || [];
  } catch (error) {
    console.error(`❌ 获取档案库文件列表失败 ${caseId}:`, error);
    throw error;
  }
};

/**
 * 生成缩略图URL
 */
export const getThumbnailUrl = (caseId: number, fileId: number): string => {
  return `http://localhost:8000/api/v1/cases/${caseId}/files/${fileId}/thumbnail`;
};

/**
 * 生成原图查看URL
 */
export const getImageViewUrl = (caseId: number, fileId: number): string => {
  return `http://localhost:8000/api/v1/cases/${caseId}/files/${fileId}/view`;
};

/**
 * 软删除档案库
 */
export const softDeleteCase = async (caseId: number, deletedBy?: number): Promise<void> => {
  try {
    console.log(`🗑️ 开始软删除档案库: ID=${caseId}, 删除者=${deletedBy}`);

    const url = deletedBy
      ? `http://localhost:8000/api/v1/cases/${caseId}?deleted_by=${deletedBy}`
      : `http://localhost:8000/api/v1/cases/${caseId}`;

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`软删除失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`✅ 档案库软删除成功: ${result.message}`);
  } catch (error) {
    console.error(`❌ 软删除档案库失败 ${caseId}:`, error);
    throw error;
  }
};

/**
 * 恢复已删除的档案库
 */
export const restoreCase = async (caseId: number): Promise<void> => {
  try {
    console.log(`🔄 开始恢复档案库: ID=${caseId}`);

    const response = await fetch(`http://localhost:8000/api/v1/cases/${caseId}/restore`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`恢复失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`✅ 档案库恢复成功: ${result.message}`);
  } catch (error) {
    console.error(`❌ 恢复档案库失败 ${caseId}:`, error);
    throw error;
  }
};
