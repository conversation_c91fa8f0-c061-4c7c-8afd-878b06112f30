interface WorkbenchToggleIconProps {
  isUp?: boolean;
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export function WorkbenchToggleIcon({ isUp = false, className = '', onClick, style }: WorkbenchToggleIconProps) {
  return (
    <svg
      className={`icon ${className}`}
      onClick={onClick}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ cursor: 'pointer', ...style }}
    >
      <path
        d={isUp ? "M4 10l4-4 4 4" : "M4 6l4 4 4-4"}
        stroke="var(--color-text-title)"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
