// SearchCriterionPill.tsx - Phase 7A: 搜索条件标签组件
// 显示单个搜索条件，支持删除和编辑

import React from 'react';
import type { SearchCriterion } from '@/store/useSearchStore';
import { FIELD_CONFIG, OPERATOR_LABELS } from '@/store/useSearchStore';
import './SearchCriterionPill.css';

// ============================================================================
// 类型定义
// ============================================================================

interface SearchCriterionPillProps {
  criterion: SearchCriterion;
  onRemove: (id: string) => void;
  onEdit?: (id: string) => void;
  className?: string;
  showEditButton?: boolean;
}

// ============================================================================
// 辅助函数
// ============================================================================

const formatValue = (criterion: SearchCriterion): string => {
  const { field, value } = criterion;
  
  switch (field) {
    case 'fileSize':
      if (typeof value === 'number') {
        const formatter = FIELD_CONFIG.fileSize.formatter;
        return formatter ? formatter(value) : value.toString();
      }
      return value.toString();
      
    case 'modifiedTime':
      if (value instanceof Date) {
        return value.toLocaleDateString();
      }
      if (typeof value === 'string') {
        return new Date(value).toLocaleDateString();
      }
      return value.toString();
      
    case 'fileName':
    case 'fileType':
    default:
      return value.toString();
  }
};

const getFieldIcon = (field: SearchCriterion['field']): string => {
  return FIELD_CONFIG[field]?.icon || '🔍';
};

const getFieldLabel = (field: SearchCriterion['field']): string => {
  return FIELD_CONFIG[field]?.label || field;
};

const getOperatorLabel = (operator: SearchCriterion['operator']): string => {
  return OPERATOR_LABELS[operator] || operator;
};

// ============================================================================
// 主组件
// ============================================================================

export const SearchCriterionPill: React.FC<SearchCriterionPillProps> = ({
  criterion,
  onRemove,
  onEdit,
  className = '',
  showEditButton = false,
}) => {
  const [isHovered, setIsHovered] = React.useState(false);

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRemove(criterion.id);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(criterion.id);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Delete' || e.key === 'Backspace') {
      e.preventDefault();
      onRemove(criterion.id);
    } else if (e.key === 'Enter' && onEdit) {
      e.preventDefault();
      onEdit(criterion.id);
    }
  };

  const fieldIcon = getFieldIcon(criterion.field);
  const fieldLabel = getFieldLabel(criterion.field);
  const operatorLabel = getOperatorLabel(criterion.operator);
  const formattedValue = formatValue(criterion);

  return (
    <div
      className={`search-criterion-pill ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Search criterion: ${fieldLabel} ${operatorLabel} ${formattedValue}. Press Delete to remove.`}
    >
      <div className="pill-content">
        <span className="field-icon" aria-hidden="true">
          {fieldIcon}
        </span>
        
        <span className="field-label">
          {fieldLabel}
        </span>
        
        <span className="operator-label">
          {operatorLabel}
        </span>
        
        <span className="value-label">
          {formattedValue}
        </span>
        
        {criterion.caseSensitive && (
          <span className="case-sensitive-indicator" title="Case sensitive">
            Aa
          </span>
        )}
      </div>

      <div className="pill-actions">
        {showEditButton && onEdit && (
          <button
            className="edit-button"
            onClick={handleEdit}
            title="Edit criterion"
            aria-label="Edit this search criterion"
          >
            ✏️
          </button>
        )}
        
        <button
          className="remove-button"
          onClick={handleRemove}
          title="Remove criterion"
          aria-label="Remove this search criterion"
        >
          ✕
        </button>
      </div>

      {/* 悬停效果指示器 */}
      {isHovered && (
        <div className="hover-indicator" aria-hidden="true" />
      )}
    </div>
  );
};

// ============================================================================
// 批量操作组件
// ============================================================================

interface SearchCriterionListProps {
  criteria: SearchCriterion[];
  onRemove: (id: string) => void;
  onEdit?: (id: string) => void;
  onClearAll?: () => void;
  className?: string;
  showEditButtons?: boolean;
  maxDisplayCount?: number;
}

export const SearchCriterionList: React.FC<SearchCriterionListProps> = ({
  criteria,
  onRemove,
  onEdit,
  onClearAll,
  className = '',
  showEditButtons = false,
  maxDisplayCount,
}) => {
  const displayCriteria = maxDisplayCount 
    ? criteria.slice(0, maxDisplayCount)
    : criteria;
  
  const hiddenCount = maxDisplayCount && criteria.length > maxDisplayCount
    ? criteria.length - maxDisplayCount
    : 0;

  if (criteria.length === 0) {
    return (
      <div className={`search-criterion-list empty ${className}`}>
        <span className="empty-message">No search criteria</span>
      </div>
    );
  }

  return (
    <div className={`search-criterion-list ${className}`}>
      <div className="criteria-container">
        {displayCriteria.map((criterion) => (
          <SearchCriterionPill
            key={criterion.id}
            criterion={criterion}
            onRemove={onRemove}
            onEdit={onEdit}
            showEditButton={showEditButtons}
          />
        ))}
        
        {hiddenCount > 0 && (
          <div className="hidden-count-indicator">
            +{hiddenCount} more
          </div>
        )}
      </div>

      {criteria.length > 0 && onClearAll && (
        <button
          className="clear-all-button"
          onClick={onClearAll}
          title="Clear all search criteria"
          aria-label="Clear all search criteria"
        >
          Clear All
        </button>
      )}
    </div>
  );
};

export default SearchCriterionPill;
