/* NaturalLanguageSearch.css - Phase 8A: 自然语言搜索样式 */
/* 遵循项目既定的颜色方案和设计模式 */

/* ============================================================================
   主容器样式
   ============================================================================ */

.nl-search {
  background-color: #040709;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  color: #A49F9A;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ============================================================================
   服务状态样式
   ============================================================================ */

.nl-search__status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  margin-bottom: 12px;
}

.nl-search__status--error {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.nl-search__status--warning {
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.nl-search__status--throttled {
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.nl-search__status-icon {
  font-size: 14px;
  flex-shrink: 0;
}

/* ============================================================================
   搜索表单样式
   ============================================================================ */

.nl-search__form {
  margin-bottom: 16px;
}

.nl-search__input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #191012;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 4px;
  transition: border-color 0.2s ease;
}

.nl-search__input-group:focus-within {
  border-color: #666;
  box-shadow: 0 0 0 2px rgba(164, 159, 154, 0.1);
}

.nl-search__input {
  flex: 1;
  background: none;
  border: none;
  color: #A49F9A;
  font-size: 14px;
  padding: 10px 12px;
  outline: none;
}

.nl-search__input::placeholder {
  color: #666;
  font-style: italic;
}

.nl-search__input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.nl-search__clear {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.nl-search__clear:hover:not(:disabled) {
  color: #A49F9A;
  background-color: rgba(164, 159, 154, 0.1);
}

.nl-search__clear:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nl-search__submit {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #A49F9A;
  color: #040709;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nl-search__submit:hover:not(:disabled) {
  background-color: #B5B0AB;
  transform: translateY(-1px);
}

.nl-search__submit:active:not(:disabled) {
  transform: translateY(0);
}

.nl-search__submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.nl-search__submit-icon {
  font-size: 16px;
}

.nl-search__spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============================================================================
   解析结果样式
   ============================================================================ */

.nl-search__result {
  background-color: rgba(164, 159, 154, 0.05);
  border: 1px solid rgba(164, 159, 154, 0.1);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.nl-search__result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
}

.nl-search__result-icon {
  font-size: 16px;
}

.nl-search__result-title {
  font-weight: 600;
  color: #A49F9A;
}

.nl-search__result-confidence {
  margin-left: auto;
  font-size: 11px;
  color: #666;
  background-color: rgba(164, 159, 154, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
}

.nl-search__result-interpretation {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.nl-search__entity {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.nl-search__result-stats {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

/* ============================================================================
   错误显示样式
   ============================================================================ */

.nl-search__error {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  padding: 10px 12px;
  color: #ef4444;
  font-size: 13px;
  margin-bottom: 12px;
}

.nl-search__error-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.nl-search__error-text {
  flex: 1;
}

.nl-search__error-retry {
  background: none;
  border: 1px solid #ef4444;
  color: #ef4444;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nl-search__error-retry:hover:not(:disabled) {
  background-color: rgba(239, 68, 68, 0.1);
}

.nl-search__error-retry:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ============================================================================
   查询建议样式
   ============================================================================ */

.nl-search__suggestions {
  background-color: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: 6px;
  padding: 12px;
}

.nl-search__suggestions-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 13px;
  font-weight: 600;
  color: #f59e0b;
}

.nl-search__suggestions-icon {
  font-size: 14px;
}

.nl-search__suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.nl-search__suggestion {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #A49F9A;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.nl-search__suggestion:hover {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.5);
  transform: translateX(4px);
}

.nl-search__suggestion-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.nl-search__suggestion-text {
  flex: 1;
}
