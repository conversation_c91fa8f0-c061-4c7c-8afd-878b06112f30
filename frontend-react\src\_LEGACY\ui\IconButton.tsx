// src/components/ui/IconButton.tsx
import React from 'react';

interface IconButtonProps {
  // 传入导入的SVG组件
  icon: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  // 鼠标悬停时显示的提示文字
  tooltip: string;
  onClick?: () => void;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

export function IconButton({
  icon: Icon,
  tooltip,
  onClick,
  className,
  size = 'medium',
  disabled = false
}: IconButtonProps) {
  const sizeClasses = {
    small: 'w-[16px] h-[16px]',
    medium: 'w-[20px] h-[20px]',
    large: 'w-[24px] h-[24px]'
  };

  const iconSizeClasses = {
    small: 'w-[16px] h-[16px]',
    medium: 'w-[20px] h-[20px]',
    large: 'w-[24px] h-[24px]'
  };

  return (
    <button
      title={tooltip}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      // 严格按照规格：20x20px边框，无底色，透明描边，悬停时描边7F2C25
      className={`
        ${sizeClasses[size]} flex items-center justify-center
        bg-transparent border border-transparent rounded-none
        hover:border-[#7F2C25] disabled:opacity-50 disabled:cursor-not-allowed
        transition-all shrink-0
        ${className}
      `}
      style={{
        // F7F8F8色号的CSS滤镜
        filter: disabled
          ? 'brightness(0) saturate(100%) invert(50%)'
          : 'brightness(0) saturate(100%) invert(97%) sepia(3%) saturate(372%) hue-rotate(169deg) brightness(100%) contrast(96%)'
      }}
      onMouseEnter={(e) => {
        if (!disabled) {
          // 悬停时变为9D362F色号的CSS滤镜
          e.currentTarget.style.filter = 'brightness(0) saturate(100%) invert(18%) sepia(89%) saturate(1729%) hue-rotate(347deg) brightness(95%) contrast(88%)';
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled) {
          // 恢复F7F8F8色号
          e.currentTarget.style.filter = 'brightness(0) saturate(100%) invert(97%) sepia(3%) saturate(372%) hue-rotate(169deg) brightness(100%) contrast(96%)';
        }
      }}
    >
      <Icon className={iconSizeClasses[size]} />
    </button>
  );
}
