// DetailsSidebar - Integrated with existing API and state management
// 集成现有API和状态管理的详情侧边栏组件

import React, { useState, useMemo } from 'react';
import {
  Download,
  ExternalLink,
  Tag,
  Calendar,
  Camera,
  Palette,
  FileText,
  Image,
  BarChart3,
  Share2,
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  FolderO<PERSON>,
  Plus,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Hooks and utilities
// Pre-Phase 5: 移除useApi依赖，使用文件系统状态
// import { useFiles } from '@/hooks/useApi';
import { useUIStore } from '@/store';
import { adaptFileItemToUIImageItem } from '@/adapters/uiDataAdapters';
import type { UIImageItem } from '@/adapters/uiDataAdapters';

// ============================================================================
// 接口定义
// ============================================================================

interface DetailsSidebarProps {
  className?: string;
}

// ============================================================================
// DetailsSidebar 组件
// ============================================================================

export const DetailsSidebar: React.FC<DetailsSidebarProps> = ({
  className,
}) => {
  // ========================================
  // 状态管理
  // ========================================
  const {
    selectedCaseId,
    selectedFileIds,
    activeFilters,
  } = useUIStore();

  // 本地状态
  const [newTag, setNewTag] = useState('');
  const [isAddingTag, setIsAddingTag] = useState(false);

  // ========================================
  // API数据获取
  // ========================================
  const { data: filesData } = useFiles(selectedCaseId || undefined);

  // ========================================
  // 计算属性
  // ========================================

  // 选中的文件
  const selectedFiles = useMemo(() => {
    if (!filesData?.files || selectedFileIds.length === 0) return [];

    return filesData.files
      .filter(file => selectedFileIds.includes(file.id))
      .map(file => adaptFileItemToUIImageItem(file));
  }, [filesData, selectedFileIds]);

  // 选中的标签（从activeFilters中提取）
  const selectedTags = useMemo(() => {
    const tags: string[] = [];
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (key.startsWith('tag_')) {
        tags.push(value);
      }
    });
    return tags;
  }, [activeFilters]);

  // ========================================
  // 事件处理函数
  // ========================================

  const handleAddTag = async () => {
    if (!newTag.trim() || selectedFiles.length === 0) return;

    try {
      // TODO: 实现添加标签到选中文件的API调用
      // console.log('Adding tag:', newTag, 'to files:', selectedFileIds); // [CLEANED]

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 成功后清空输入
      setNewTag('');
      setIsAddingTag(false);

      // TODO: 刷新文件数据以显示新标签
      alert(`成功为 ${selectedFiles.length} 个文件添加标签: ${newTag}`);
    } catch (error) {
      console.error('添加标签失败:', error);
      alert('添加标签失败，请重试');
    }
  };

  const handleRemoveTag = async (tag: string) => {
    if (!confirm(`确定要从 ${selectedFiles.length} 个文件中移除标签 "${tag}" 吗？`)) {
      return;
    }

    try {
      // TODO: 实现从选中文件移除标签的API调用
      // console.log('Removing tag:', tag, 'from files:', selectedFileIds); // [CLEANED]

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // TODO: 刷新文件数据以移除标签
      alert(`成功从 ${selectedFiles.length} 个文件中移除标签: ${tag}`);
    } catch (error) {
      console.error('移除标签失败:', error);
      alert('移除标签失败，请重试');
    }
  };

  const handleDownload = () => {
    // TODO: 实现文件下载功能
    // console.log('Downloading files:', selectedFileIds); // [CLEANED]
  };

  const handleDelete = () => {
    // TODO: 实现文件删除功能
    // console.log('Deleting files:', selectedFileIds); // [CLEANED]
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // ========================================
  // 渲染函数
  // ========================================

  // 情景1：选择单张图片
  if (selectedFiles.length === 1) {
    const image = selectedFiles[0];

    return (
      <div
        className={`h-full overflow-auto ${className || ''}`}
        style={{ color: 'var(--mizzy-content)' }}
      >
        {/* 图片预览 */}
        <div className="p-4">
          <div className="relative group">
            <img
              src={image.src}
              alt={image.filename}
              className="w-full h-48 object-cover rounded-lg"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
              <Button variant="ghost" size="sm" className="text-white">
                <ExternalLink className="w-4 h-4 mr-2" />
                查看原图
              </Button>
            </div>
          </div>
        </div>

        {/* 文件信息 */}
        <div className="px-4 pb-4">
          <h3 className="font-medium mb-2" style={{ color: 'var(--mizzy-title)' }}>
            {image.filename}
          </h3>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span style={{ color: 'var(--mizzy-icon)' }}>类型:</span>
              <span>{image.type}</span>
            </div>
            <div className="flex justify-between">
              <span style={{ color: 'var(--mizzy-icon)' }}>大小:</span>
              <span>{image.size}</span>
            </div>
            {image.width && image.height && (
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>尺寸:</span>
                <span>{image.width}×{image.height}</span>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* 标签管理 */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium" style={{ color: 'var(--mizzy-title)' }}>
              标签
            </h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsAddingTag(true)}
              className="p-1"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>

          {/* 添加标签输入框 */}
          {isAddingTag && (
            <div className="flex gap-2 mb-3">
              <Input
                placeholder="输入标签名称"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                className="text-sm"
                autoFocus
              />
              <Button size="sm" onClick={handleAddTag}>
                添加
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsAddingTag(false);
                  setNewTag('');
                }}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}

          {/* 标签列表 */}
          <div className="flex flex-wrap gap-2">
            {image.tags.map((tag, index) => (
              <div
                key={index}
                className="flex items-center gap-1 px-2 py-1 rounded text-xs"
                style={{
                  background: 'var(--mizzy-input)',
                  color: 'var(--mizzy-content)'
                }}
              >
                <span>{tag}</span>
                <button
                  onClick={() => handleRemoveTag(tag)}
                  className="hover:text-red-500 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* 操作按钮 */}
        <div className="p-4 space-y-2">
          <Button variant="outline" className="w-full justify-start" onClick={handleDownload}>
            <Download className="w-4 h-4 mr-2" />
            下载文件
          </Button>
          <Button variant="outline" className="w-full justify-start">
            <Copy className="w-4 h-4 mr-2" />
            复制路径
          </Button>
          <Button variant="outline" className="w-full justify-start">
            <FolderOpen className="w-4 h-4 mr-2" />
            在文件夹中显示
          </Button>
          <Button variant="destructive" className="w-full justify-start" onClick={handleDelete}>
            <Trash2 className="w-4 h-4 mr-2" />
            删除文件
          </Button>
        </div>
      </div>
    );
  }

  // 情景2：选择多张图片
  if (selectedFiles.length > 1) {
    const totalSize = selectedFiles.reduce((sum, file) => {
      const sizeMatch = file.size.match(/(\d+\.?\d*)\s*(B|KB|MB|GB)/);
      if (sizeMatch) {
        const value = parseFloat(sizeMatch[1]);
        const unit = sizeMatch[2];
        const multipliers = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
        return sum + value * (multipliers[unit as keyof typeof multipliers] || 1);
      }
      return sum;
    }, 0);

    return (
      <div
        className={`h-full overflow-auto ${className || ''}`}
        style={{ color: 'var(--mizzy-content)' }}
      >
        {/* 多选概览 */}
        <div className="p-4">
          <h3 className="font-medium mb-4" style={{ color: 'var(--mizzy-title)' }}>
            已选择 {selectedFiles.length} 个文件
          </h3>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span style={{ color: 'var(--mizzy-icon)' }}>总大小:</span>
              <span>{formatFileSize(totalSize)}</span>
            </div>
          </div>
        </div>

        <Separator />

        {/* 批量标签管理 */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium" style={{ color: 'var(--mizzy-title)' }}>
              批量标签操作
            </h4>
          </div>

          {/* 添加标签到所有选中文件 */}
          {isAddingTag && (
            <div className="flex gap-2 mb-3">
              <Input
                placeholder="为所有文件添加标签"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                className="text-sm"
                autoFocus
              />
              <Button size="sm" onClick={handleAddTag}>
                添加
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsAddingTag(false);
                  setNewTag('');
                }}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}

          <Button
            variant="outline"
            className="w-full justify-start mb-3"
            onClick={() => setIsAddingTag(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            批量添加标签
          </Button>
        </div>

        <Separator />

        {/* 批量操作 */}
        <div className="p-4 space-y-2">
          <Button variant="outline" className="w-full justify-start" onClick={handleDownload}>
            <Download className="w-4 h-4 mr-2" />
            批量下载
          </Button>
          <Button variant="destructive" className="w-full justify-start" onClick={handleDelete}>
            <Trash2 className="w-4 h-4 mr-2" />
            批量删除
          </Button>
        </div>
      </div>
    );
  }

  // 情景3：选择标签
  if (selectedTags.length > 0) {
    return (
      <div
        className={`h-full overflow-auto ${className || ''}`}
        style={{ color: 'var(--mizzy-content)' }}
      >
        <div className="p-4">
          <h3 className="font-medium mb-4" style={{ color: 'var(--mizzy-title)' }}>
            标签详情
          </h3>

          <div className="space-y-4">
            {selectedTags.map((tag, index) => (
              <div key={index} className="space-y-2">
                <h4 className="font-medium">{tag}</h4>
                <p className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>
                  标签管理功能开发中...
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // 情景4：未选择任何内容
  return (
    <div className={`h-full flex items-center justify-center ${className || ''}`}>
      <div className="text-center space-y-4">
        <div className="text-4xl">📄</div>
        <h3 className="text-lg font-medium" style={{ color: 'var(--mizzy-title)' }}>
          未选择文件
        </h3>
        <p className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>
          在画廊中选择文件查看详细信息
        </p>
      </div>
    </div>
  );
};
