// Phase 4: FileCard组件单元测试
// 验证测试框架正常工作并测试核心组件功能

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { FileCard } from '../FileCard';

// 测试用的文件数据
const mockImageFile = {
  id: 1,
  fileName: 'test-image.jpg',
  filePath: '/test/path/test-image.jpg',
  fileType: 'image/jpeg',
  fileSize: 1024000, // 1MB
  width: 1920,
  height: 1080,
  thumbnailPath: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
};

const mockDocumentFile = {
  id: 2,
  fileName: 'document.pdf',
  filePath: '/test/path/document.pdf',
  fileType: 'application/pdf',
  fileSize: 512000, // 512KB
  thumbnailPath: undefined
};

describe('FileCard Component', () => {
  it('renders file name correctly', () => {
    render(
      <FileCard
        file={mockImageFile}
        showFileName={true}
      />
    );

    expect(screen.getByText('test-image.jpg')).toBeInTheDocument();
  });

  it('displays thumbnail for image files', () => {
    render(
      <FileCard
        file={mockImageFile}
        showFileName={true}
      />
    );

    const thumbnail = screen.getByRole('img');
    expect(thumbnail).toBeInTheDocument();
    expect(thumbnail).toHaveAttribute('src', mockImageFile.thumbnailPath);
    expect(thumbnail).toHaveAttribute('alt', mockImageFile.fileName);
  });

  it('shows file type icon for non-image files', () => {
    render(
      <FileCard
        file={mockDocumentFile}
        showFileName={true}
      />
    );

    // 应该显示PDF文件的图标
    expect(screen.getByText('📄')).toBeInTheDocument();
  });

  it('calls onSelect when clicked', () => {
    const mockOnSelect = vi.fn();

    render(
      <FileCard
        file={mockImageFile}
        onSelect={mockOnSelect}
        showFileName={true}
      />
    );

    const fileCard = screen.getByRole('button');
    fireEvent.click(fileCard);

    // Phase 5C: onSelect现在包含event参数
    expect(mockOnSelect).toHaveBeenCalledWith(mockImageFile.id, true, expect.any(Object));
  });

  it('calls onDoubleClick when double clicked', () => {
    const mockOnDoubleClick = vi.fn();

    render(
      <FileCard
        file={mockImageFile}
        onDoubleClick={mockOnDoubleClick}
        showFileName={true}
      />
    );

    const fileCard = screen.getByRole('button');
    fireEvent.doubleClick(fileCard);

    expect(mockOnDoubleClick).toHaveBeenCalledWith(mockImageFile);
  });

  it('shows selected state correctly', () => {
    render(
      <FileCard
        file={mockImageFile}
        selected={true}
        showFileName={true}
      />
    );

    const fileCard = screen.getByRole('button');
    expect(fileCard).toHaveAttribute('aria-pressed', 'true');
  });

  it('displays file info when enabled', () => {
    render(
      <FileCard
        file={mockImageFile}
        showFileName={true}
        showFileInfo={true}
      />
    );

    // 应该显示文件大小 (根据实际输出调整期望值)
    expect(screen.getByText('1000 KB')).toBeInTheDocument();
    // 应该显示图片尺寸
    expect(screen.getByText(/1920.*×.*1080/)).toBeInTheDocument();
  });
});
