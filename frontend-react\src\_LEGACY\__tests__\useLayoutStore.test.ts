// useLayoutStore.test.ts - Phase 6: 布局状态管理测试
// 测试布局持久化、面板可见性和布局计算

import { renderHook, act } from '@testing-library/react';
import { useLayoutStore } from '../useLayoutStore';

// ============================================================================
// Mock localStorage
// ============================================================================

const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// ============================================================================
// 测试套件
// ============================================================================

describe('useLayoutStore', () => {
  beforeEach(() => {
    // 每个测试前清除localStorage和重置store
    localStorageMock.clear();
    const { result } = renderHook(() => useLayoutStore());
    act(() => {
      result.current.resetLayout();
    });
  });

  describe('面板可见性控制', () => {
    it('应该正确切换Catalog面板可见性', () => {
      const { result } = renderHook(() => useLayoutStore());

      expect(result.current.isCatalogVisible).toBe(true); // 默认可见

      act(() => {
        result.current.toggleCatalog();
      });

      expect(result.current.isCatalogVisible).toBe(false);

      act(() => {
        result.current.toggleCatalog();
      });

      expect(result.current.isCatalogVisible).toBe(true);
    });

    it('应该正确设置Info面板可见性', () => {
      const { result } = renderHook(() => useLayoutStore());

      act(() => {
        result.current.setInfoVisible(false);
      });

      expect(result.current.isInfoVisible).toBe(false);

      act(() => {
        result.current.setInfoVisible(true);
      });

      expect(result.current.isInfoVisible).toBe(true);
    });

    it('应该正确切换Workbench面板可见性', () => {
      const { result } = renderHook(() => useLayoutStore());

      expect(result.current.isWorkbenchVisible).toBe(true); // 默认可见

      act(() => {
        result.current.toggleWorkbench();
      });

      expect(result.current.isWorkbenchVisible).toBe(false);
    });
  });

  describe('布局尺寸控制', () => {
    it('应该正确设置水平布局尺寸', () => {
      const { result } = renderHook(() => useLayoutStore());
      const testSizes = [25, 50, 25];

      act(() => {
        result.current.setHorizontalSizes(testSizes);
      });

      expect(result.current.horizontalSizes).toEqual(testSizes);
    });

    it('应该正确设置垂直布局尺寸', () => {
      const { result } = renderHook(() => useLayoutStore());
      const testSizes = [60, 40];

      act(() => {
        result.current.setVerticalSizes(testSizes);
      });

      expect(result.current.verticalSizes).toEqual(testSizes);
    });
  });

  describe('布局计算工具', () => {
    it('应该正确计算默认水平布局（所有面板可见）', () => {
      const { result } = renderHook(() => useLayoutStore());

      const layout = result.current.getDefaultHorizontalLayout();

      expect(layout).toHaveLength(3); // Catalog, Center, Info
      expect(layout[0]).toBe(20); // Catalog
      expect(layout[1]).toBe(65); // Center (100 - 20 - 15)
      expect(layout[2]).toBe(15); // Info
    });

    it('应该正确计算默认水平布局（隐藏Catalog）', () => {
      const { result } = renderHook(() => useLayoutStore());

      act(() => {
        result.current.setCatalogVisible(false);
      });

      const layout = result.current.getDefaultHorizontalLayout();

      expect(layout).toHaveLength(2); // Center, Info
      expect(layout[0]).toBe(85); // Center (100 - 15)
      expect(layout[1]).toBe(15); // Info
    });

    it('应该正确计算默认水平布局（隐藏Info）', () => {
      const { result } = renderHook(() => useLayoutStore());

      act(() => {
        result.current.setInfoVisible(false);
      });

      const layout = result.current.getDefaultHorizontalLayout();

      expect(layout).toHaveLength(2); // Catalog, Center
      expect(layout[0]).toBe(20); // Catalog
      expect(layout[1]).toBe(80); // Center (100 - 20)
    });

    it('应该正确计算默认垂直布局（Workbench可见）', () => {
      const { result } = renderHook(() => useLayoutStore());

      const layout = result.current.getDefaultVerticalLayout();

      expect(layout).toEqual([70, 30]); // Gallery 70%, Workbench 30%
    });

    it('应该正确计算默认垂直布局（Workbench隐藏）', () => {
      const { result } = renderHook(() => useLayoutStore());

      act(() => {
        result.current.setWorkbenchVisible(false);
      });

      const layout = result.current.getDefaultVerticalLayout();

      expect(layout).toEqual([100]); // Gallery 100%
    });
  });

  describe('重置功能', () => {
    it('应该正确重置所有布局状态', () => {
      const { result } = renderHook(() => useLayoutStore());

      // 修改一些状态
      act(() => {
        result.current.setCatalogVisible(false);
        result.current.setInfoVisible(false);
        result.current.setHorizontalSizes([30, 70]);
        result.current.setVerticalSizes([80, 20]);
      });

      // 重置
      act(() => {
        result.current.resetLayout();
      });

      // 验证重置结果
      expect(result.current.isCatalogVisible).toBe(true);
      expect(result.current.isInfoVisible).toBe(true);
      expect(result.current.isWorkbenchVisible).toBe(true);
      expect(result.current.horizontalSizes).toEqual([]);
      expect(result.current.verticalSizes).toEqual([]);
    });
  });

  describe('持久化功能', () => {
    it('应该持久化面板可见性状态', () => {
      const { result: result1 } = renderHook(() => useLayoutStore());

      // 修改可见性状态
      act(() => {
        result1.current.setCatalogVisible(false);
        result1.current.setInfoVisible(false);
      });

      // 创建新的store实例（模拟应用重启）
      const { result: result2 } = renderHook(() => useLayoutStore());

      // 验证状态是否被恢复
      expect(result2.current.isCatalogVisible).toBe(false);
      expect(result2.current.isInfoVisible).toBe(false);
      expect(result2.current.isWorkbenchVisible).toBe(true); // 未修改的状态
    });

    it('应该不持久化运行时状态', () => {
      const { result: result1 } = renderHook(() => useLayoutStore());

      // 修改运行时状态
      act(() => {
        result1.current.setHorizontalSizes([30, 70]);
        result1.current.setVerticalSizes([80, 20]);
      });

      // 验证运行时状态存在于当前实例
      expect(result1.current.horizontalSizes).toEqual([30, 70]);
      expect(result1.current.verticalSizes).toEqual([80, 20]);

      // 重置store以模拟应用重启
      act(() => {
        result1.current.resetLayout();
      });

      // 验证运行时状态被重置
      expect(result1.current.horizontalSizes).toEqual([]);
      expect(result1.current.verticalSizes).toEqual([]);
    });
  });

  describe('边界情况', () => {
    it('应该处理所有面板都隐藏的情况', () => {
      const { result } = renderHook(() => useLayoutStore());

      act(() => {
        result.current.setCatalogVisible(false);
        result.current.setInfoVisible(false);
        result.current.setWorkbenchVisible(false);
      });

      const horizontalLayout = result.current.getDefaultHorizontalLayout();
      const verticalLayout = result.current.getDefaultVerticalLayout();

      expect(horizontalLayout).toHaveLength(1); // 只有Center
      expect(horizontalLayout[0]).toBe(100); // Center占100%
      expect(verticalLayout).toEqual([100]); // Gallery占100%
    });

    it('应该处理空尺寸数组', () => {
      const { result } = renderHook(() => useLayoutStore());

      act(() => {
        result.current.setHorizontalSizes([]);
        result.current.setVerticalSizes([]);
      });

      expect(result.current.horizontalSizes).toEqual([]);
      expect(result.current.verticalSizes).toEqual([]);
    });
  });
});
