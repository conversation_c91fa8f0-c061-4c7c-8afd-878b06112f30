import React from 'react';
import { cn } from '@/utils/cn';

// ============================================================================
// Icon Component Interface
// ============================================================================

export interface IconProps extends React.SVGProps<SVGSVGElement> {
  /**
   * 图标名称 - 对应 assets/icons/ 目录下的SVG文件名
   */
  name: string;

  /**
   * 图标大小
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;

  /**
   * 图标颜色
   */
  color?: 'primary' | 'secondary' | 'accent' | 'muted' | 'destructive' | string;

  /**
   * 是否可交互（鼠标悬停效果）
   */
  interactive?: boolean;

  /**
   * 点击事件处理
   */
  onClick?: (event: React.MouseEvent<SVGSVGElement>) => void;

  className?: string;
}

// ============================================================================
// 图标尺寸映射
// ============================================================================

const sizeMap = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
} as const;

// ============================================================================
// 图标颜色映射
// ============================================================================

const colorMap = {
  primary: 'var(--color-icon-default)',
  secondary: 'var(--color-text-secondary)',
  accent: 'var(--color-accent-primary)',
  muted: 'var(--color-text-content)',
  destructive: 'var(--color-destructive)',
} as const;

// ============================================================================
// Icon Component Implementation
// ============================================================================

/**
 * Icon 组件 - 统一图标系统
 *
 * 特性：
 * 1. 动态SVG加载 - 根据name属性加载对应的SVG文件
 * 2. 统一尺寸系统 - 支持预设尺寸和自定义数值
 * 3. 主题色彩集成 - 自动适配Mizzy Star配色方案
 * 4. 交互效果 - 可选的鼠标悬停和点击效果
 * 5. 完全可定制 - 支持所有SVG属性
 *
 * 使用示例：
 * <Icon name="archive" size="md" color="primary" interactive onClick={handleClick} />
 */
export const Icon: React.FC<IconProps> = ({
  name,
  size = 'md',
  color = 'primary',
  interactive = false,
  onClick,
  className,
  style,
  ...props
}) => {
  // 计算实际尺寸
  const actualSize = typeof size === 'number' ? size : sizeMap[size];
  
  // 计算实际颜色
  const actualColor = color in colorMap ? colorMap[color as keyof typeof colorMap] : color;

  // 构建样式
  const iconStyle: React.CSSProperties = {
    width: actualSize,
    height: actualSize,
    fill: 'currentColor',
    color: actualColor,
    cursor: interactive || onClick ? 'pointer' : 'default',
    transition: interactive ? 'all 0.2s ease-in-out' : undefined,
    // CSS custom properties for hover effects
    '--icon-hover-color': 'var(--color-icon-hover)',
    '--icon-hover-scale': interactive ? '1.1' : '1.0',
    ...style,
  } as React.CSSProperties;

  // 构建类名
  const iconClassName = cn(
    'icon',
    interactive && 'icon-interactive',
    onClick && 'cursor-pointer',
    className
  );

  // 动态导入SVG内容（这里先用占位符，实际实现需要动态加载）
  const renderIconContent = () => {
    // TODO: 实现动态SVG加载逻辑
    // 这里先返回一个通用的占位符图标
    switch (name) {
      case 'archive':
        return (
          <path d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM4 8h16v8a2 2 0 01-2 2H6a2 2 0 01-2-2V8zm4 4a1 1 0 011-1h6a1 1 0 110 2H9a1 1 0 01-1-1z" />
        );
      case 'settings':
        return (
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
        );
      case 'import':
        return (
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
        );
      case 'expand':
        return (
          <path d="M10,21V19H6.41L10.91,14.5L9.5,13.09L5,17.59V14H3V21H10M14.5,10.91L19,6.41V10H21V3H14V5H17.59L13.09,9.5L14.5,10.91Z" />
        );
      case 'folder-closed':
        return (
          <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
        );
      case 'carousel':
        return (
          <path d="M2,6H6V18H2V6M7,19H9V5H7V19M10,6H14V18H10V6M15,5V19H17V5H15M18,6H22V18H18V6Z" />
        );
      case 'sidebar-toggle':
        return (
          <path d="M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z" />
        );
      case 'upward-triangle':
        return (
          <path d="M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z" />
        );
      case 'downward-triangle':
        return (
          <path d="M7.41,8.59L12,13.17L16.59,8.59L18,10L12,16L6,10L7.41,8.59Z" />
        );
      default:
        // 默认图标 - 问号
        return (
          <path d="M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2ZM13,19H11V17H13V19ZM15.07,11.25L14.17,12.17C13.45,12.9 13,13.5 13,15H11V14.5C11,13.4 11.45,12.4 12.17,11.67L13.41,10.41C13.78,10.05 14,9.55 14,9C14,7.9 13.1,7 12,7C10.9,7 10,7.9 10,9H8C8,6.79 9.79,5 12,5C14.21,5 16,6.79 16,9C16,9.88 15.64,10.68 15.07,11.25Z" />
        );
    }
  };

  return (
    <svg
      viewBox="0 0 24 24"
      style={iconStyle}
      className={iconClassName}
      onClick={onClick}
      {...props}
    >
      {renderIconContent()}
    </svg>
  );
};

export default Icon;
