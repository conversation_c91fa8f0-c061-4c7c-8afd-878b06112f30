// Project Novak - UI Components Export Index
// 统一导出所有原子组件

// 基础组件导出
export { Button, buttonVariants } from './Button';
export type { ButtonProps } from './Button';

export { Input, inputVariants } from './Input';
export type { InputProps } from './Input';

export { Icon } from './Icon';
export type { IconProps } from './Icon';

export { FileCard, fileCardVariants } from './FileCard';
export type { FileCardProps } from './FileCard';

// Additional UI components
export { Separator } from './separator';
export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogTrigger,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from './dialog';
export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from './dropdown-menu';

// Re-export lowercase versions for compatibility
export { But<PERSON> as button } from './Button';
export { Input as input } from './Input';
