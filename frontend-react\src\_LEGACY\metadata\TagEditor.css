/* TagEditor.css - Phase 7B: 标签编辑器样式 */
/* 遵循项目既定的颜色方案和设计模式 */

/* ============================================================================
   主容器样式
   ============================================================================ */

.tag-editor {
  background-color: #040709;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  color: #A49F9A;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-height: 500px;
  overflow-y: auto;
}

.tag-editor--empty,
.tag-editor--loading,
.tag-editor--error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* ============================================================================
   头部区域
   ============================================================================ */

.tag-editor__header {
  margin-bottom: 16px;
  border-bottom: 1px solid #333;
  padding-bottom: 12px;
}

.tag-editor__title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #A49F9A;
}

.tag-editor__file-path {
  margin: 0;
  font-size: 12px;
  color: #666;
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ============================================================================
   标签添加区域
   ============================================================================ */

.tag-editor__add-section {
  margin-bottom: 20px;
}

.tag-editor__input-group {
  display: flex;
  gap: 8px;
}

.tag-editor__input {
  flex: 1;
  padding: 8px 12px;
  background-color: #191012;
  border: 1px solid #444;
  border-radius: 4px;
  color: #A49F9A;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.tag-editor__input:focus {
  border-color: #666;
  box-shadow: 0 0 0 2px rgba(164, 159, 154, 0.1);
}

.tag-editor__input::placeholder {
  color: #666;
}

.tag-editor__input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tag-editor__add-button {
  padding: 8px 16px;
  background-color: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #A49F9A;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tag-editor__add-button:hover:not(:disabled) {
  background-color: #444;
  border-color: #666;
}

.tag-editor__add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ============================================================================
   标签显示区域
   ============================================================================ */

.tag-editor__tags {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tag-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tag-section__title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #A49F9A;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-section__pills {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* ============================================================================
   标签药丸样式
   ============================================================================ */

.tag-pill {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.2s ease;
}

.tag-pill__icon {
  font-size: 12px;
}

.tag-pill__name {
  white-space: nowrap;
}

.tag-pill__remove {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  margin-left: 4px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.tag-pill__remove:hover {
  opacity: 1;
}

/* 不同类型标签的颜色方案 */
.tag-pill--user {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: #93c5fd;
}

.tag-pill--ai {
  background-color: rgba(168, 85, 247, 0.1);
  border-color: rgba(168, 85, 247, 0.3);
  color: #c4b5fd;
}

.tag-pill--metadata {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: #86efac;
}

.tag-pill--cv {
  background-color: rgba(249, 115, 22, 0.1);
  border-color: rgba(249, 115, 22, 0.3);
  color: #fdba74;
}

/* ============================================================================
   空状态样式
   ============================================================================ */

.tag-editor__placeholder,
.tag-editor__empty-state {
  text-align: center;
  color: #666;
}

.tag-editor__placeholder-icon,
.tag-editor__empty-icon {
  font-size: 32px;
  display: block;
  margin-bottom: 12px;
}

.tag-editor__placeholder-text,
.tag-editor__empty-state p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.tag-editor__empty-hint {
  font-size: 12px !important;
  color: #555 !important;
}

/* ============================================================================
   加载状态样式
   ============================================================================ */

.tag-editor__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #666;
}

.tag-editor__spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #333;
  border-top: 2px solid #A49F9A;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.tag-editor__spinner--small {
  width: 16px;
  height: 16px;
  border-width: 1px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============================================================================
   错误状态样式
   ============================================================================ */

.tag-editor__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #ef4444;
  text-align: center;
}

.tag-editor__error-icon {
  font-size: 32px;
}

.tag-editor__error-message {
  margin: 0;
  font-size: 14px;
}

.tag-editor__error-retry {
  padding: 6px 12px;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 4px;
  color: #ef4444;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-editor__error-retry:hover {
  background-color: rgba(239, 68, 68, 0.2);
}

/* ============================================================================
   状态指示器
   ============================================================================ */

.tag-editor__status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px;
  background-color: rgba(164, 159, 154, 0.05);
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

/* ============================================================================
   响应式设计
   ============================================================================ */

@media (max-width: 768px) {
  .tag-editor {
    padding: 12px;
  }

  .tag-editor__input-group {
    flex-direction: column;
  }

  .tag-editor__add-button {
    align-self: flex-start;
  }

  .tag-section__pills {
    gap: 4px;
  }

  .tag-pill {
    font-size: 11px;
    padding: 3px 6px;
  }
}

/* ============================================================================
   Phase 8A: AI建议区域样式
   ============================================================================ */

.tag-editor__ai-section {
  margin: 16px 0;
  padding: 12px;
  background-color: rgba(164, 159, 154, 0.05);
  border: 1px solid rgba(164, 159, 154, 0.1);
  border-radius: 6px;
}

.tag-editor__ai-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #A49F9A;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tag-editor__service-status {
  font-size: 11px;
  color: #666;
  font-weight: normal;
}

.tag-editor__ai-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 13px;
  padding: 8px 0;
}

.tag-editor__ai-error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 13px;
  padding: 8px 0;
}

.tag-editor__retry-button {
  background: none;
  border: 1px solid #ef4444;
  color: #ef4444;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-editor__retry-button:hover:not(:disabled) {
  background-color: rgba(239, 68, 68, 0.1);
}

.tag-editor__retry-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tag-editor__ai-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.tag-editor__throttle-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f59e0b;
  font-size: 12px;
  padding: 8px 0;
}

.tag-editor__warning-icon {
  font-size: 14px;
}

/* AI建议药丸样式 */
.ai-suggestion {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border: 1px solid;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: none;
  color: inherit;
  font-family: inherit;
}

.ai-suggestion:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ai-suggestion:active:not(:disabled) {
  transform: translateY(0);
}

.ai-suggestion--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 置信度颜色 */
.ai-suggestion--high {
  border-color: #22c55e;
  color: #22c55e;
}

.ai-suggestion--high:hover:not(:disabled) {
  background-color: rgba(34, 197, 94, 0.1);
}

.ai-suggestion--medium {
  border-color: #3b82f6;
  color: #3b82f6;
}

.ai-suggestion--medium:hover:not(:disabled) {
  background-color: rgba(59, 130, 246, 0.1);
}

.ai-suggestion--low {
  border-color: #6b7280;
  color: #6b7280;
}

.ai-suggestion--low:hover:not(:disabled) {
  background-color: rgba(107, 114, 128, 0.1);
}

.ai-suggestion__icon {
  font-size: 14px;
  flex-shrink: 0;
}

.ai-suggestion__text {
  flex: 1;
  white-space: nowrap;
}

.ai-suggestion__confidence {
  font-size: 10px;
  opacity: 0.8;
  font-weight: 600;
}

/* ============================================================================
   滚动条样式
   ============================================================================ */

.tag-editor::-webkit-scrollbar {
  width: 6px;
}

.tag-editor::-webkit-scrollbar-track {
  background: #191012;
}

.tag-editor::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 3px;
}

.tag-editor::-webkit-scrollbar-thumb:hover {
  background: #444;
}
