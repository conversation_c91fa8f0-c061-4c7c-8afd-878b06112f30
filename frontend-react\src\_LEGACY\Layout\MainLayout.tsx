import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
export function MainLayout() {
  return (
    <PanelGroup direction="horizontal" className="w-full h-full">
      <Panel defaultSize={20} minSize={15}>
        <div className="w-full h-full p-4 bg-navAndControlBg text-iconAndTitleText">导航栏 (A区)</div>
      </Panel>
      <PanelResizeHandle className="w-[1px] bg-borderDefault data-[resizing]:bg-borderHighlight transition-colors" />
      <Panel defaultSize={60} minSize={30}>
        <PanelGroup direction="vertical">
          <Panel defaultSize={70} minSize={20}>
            <div className="w-full h-full p-4 bg-galleryAndWorkbenchBg">画廊 (B区)</div>
          </Panel>
          <PanelResizeHandle className="h-[1px] bg-borderDefault data-[resizing]:bg-borderHighlight transition-colors" />
          <Panel defaultSize={30} minSize={10}>
            <div className="w-full h-full p-4 bg-galleryAndWorkbenchBg">工作台 (C区)</div>
          </Panel>
        </PanelGroup>
      </Panel>
      <PanelResizeHandle className="w-[1px] bg-borderDefault data-[resizing]:bg-borderHighlight transition-colors" />
      <Panel defaultSize={20} minSize={15}>
        <div className="w-full h-full p-4 bg-navAndControlBg text-iconAndTitleText">详情栏 (D区)</div>
      </Panel>
    </PanelGroup>
  );
}