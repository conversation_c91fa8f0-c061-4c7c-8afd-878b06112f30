import { useState, useCallback, useRef, useMemo } from 'react';
import { useLayout } from '../../context/LayoutContext';
import { BaseModal, modalButtonStyles, modalInputStyles } from '../ui/BaseModal';
import { useCaseStore, useSwitchToCase, useDeleteCase, useCreateCase, useFetchCases } from '../../store/useCaseStore';

// 临时类型定义
interface IArchive {
  id: number;
  case_name: string;
  description?: string;
  created_at: string;
  file_count: number;
  cover_image_url?: string;
  isEditing?: boolean; // 新增：编辑状态标记
}

interface ArchiveManagerModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ArchiveManagerModal({ isOpen, onClose }: ArchiveManagerModalProps) {

  const { openUploadModal } = useLayout();
  const [view, setView] = useState<'list' | 'new'>('list');
  const [archiveName, setArchiveName] = useState('');
  const [archiveDescription, setArchiveDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedArchive, setSelectedArchive] = useState<IArchive | null>(null);
  const [editingArchive, setEditingArchive] = useState<IArchive | null>(null);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');

  // 滚动相关状态
  const [scrollPosition, setScrollPosition] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const cardWidth = 200; // 卡片宽度 + 间距
  const visibleCards = 4; // 可见卡片数量

  // 使用真实的案例数据 - ✅ 修复无限循环：分别选择单个值
  const allCases = useCaseStore(state => state.allCases);
  const casesLoading = useCaseStore(state => state.isLoading);

  // 获取案例操作函数
  const switchToCase = useSwitchToCase();
  const deleteCase = useDeleteCase();
  const createCase = useCreateCase();
  const fetchCases = useFetchCases();

  // 转换案例数据为档案库格式 - ✅ 使用useMemo避免无限循环
  const archives: IArchive[] = useMemo(() =>
    allCases.map(caseItem => ({
      id: caseItem.id,
      case_name: caseItem.name,
      description: caseItem.description || '',
      created_at: caseItem.created_at,
      file_count: caseItem.file_count,
      cover_image_url: caseItem.cover_image_url
    })), [allCases]
  );

  // 处理编辑档案库
  const handleEditArchive = useCallback((archive: IArchive) => {
    setEditingArchive(archive);
    setEditName(archive.case_name);
    setEditDescription(archive.description || '');
  }, []);

  // 处理保存编辑
  const handleSaveEdit = useCallback(() => {
    if (!editingArchive) return;

    setLoading(true);
    // 模拟保存操作
    setTimeout(() => {
      console.log(`✅ 档案库编辑成功: ${editName}`);
      setEditingArchive(null);
      setEditName('');
      setEditDescription('');
      setLoading(false);
    }, 1000);
  }, [editingArchive, editName, editDescription]);

  // 处理取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingArchive(null);
    setEditName('');
    setEditDescription('');
  }, []);

  // 处理档案库选择
  const handleArchiveSelect = useCallback((archive: IArchive) => {
    setSelectedArchive(archive);
    setError(null);
  }, []);

  // 处理档案库切换
  const handleArchiveSwitch = useCallback(async () => {
    if (!selectedArchive) return;

    setLoading(true);
    setError(null);

    try {
      await switchToCase(selectedArchive.id);
      console.log(`✅ 已切换到档案库: ${selectedArchive.case_name}`);
      setLoading(false);
      onClose();
    } catch (error) {
      console.error('❌ 切换档案库失败:', error);
      setError('切换档案库失败');
      setLoading(false);
    }
  }, [selectedArchive, switchToCase, onClose]);

  // 处理档案库删除
  const handleArchiveDelete = useCallback(async () => {
    if (!selectedArchive) return;

    if (!confirm(`确定要删除档案库"${selectedArchive.case_name}"吗？此操作不可撤销。`)) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await deleteCase(selectedArchive.id);
      console.log(`✅ 已删除档案库: ${selectedArchive.case_name}`);
      setSelectedArchive(null);
      setLoading(false);
    } catch (error) {
      console.error('❌ 删除档案库失败:', error);
      setError('删除档案库失败');
      setLoading(false);
    }
  }, [selectedArchive, deleteCase]);

  // 处理新建档案库
  const handleCreateArchive = useCallback(async () => {
    if (!archiveName.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const newCase = await createCase(archiveName.trim(), archiveDescription.trim() || undefined);

      if (newCase) {
        console.log(`✅ 档案库创建成功: ${archiveName.trim()}`);

        // 清空表单并返回列表视图
        setArchiveName('');
        setArchiveDescription('');
        setView('list');
        setLoading(false);
      } else {
        throw new Error('创建档案库失败');
      }
    } catch (error) {
      console.error('❌ 创建档案库失败:', error);
      setError('创建档案库失败');
      setLoading(false);
    }
  }, [archiveName, archiveDescription, createCase]);

  // 处理视图切换到新建
  const handleSwitchToNew = useCallback(() => {
    setView('new');
  }, []);

  // 处理视图切换到列表
  const handleSwitchToList = useCallback(() => {
    setView('list');
  }, []);

  // 处理添加按钮点击（临时占位）
  const handleAddClick = useCallback(() => {
    // TODO: 实现添加功能的正确逻辑
    console.log('添加按钮被点击，但功能尚未实现');
  }, []);

  // 滚动功能
  const handleScrollLeft = useCallback(() => {
    const newPosition = Math.max(0, scrollPosition - cardWidth);
    setScrollPosition(newPosition);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });
    }
  }, [scrollPosition, cardWidth]);

  const handleScrollRight = useCallback(() => {
    const maxScroll = Math.max(0, (archives.length - visibleCards) * cardWidth);
    const newPosition = Math.min(maxScroll, scrollPosition + cardWidth);
    setScrollPosition(newPosition);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });
    }
  }, [scrollPosition, cardWidth, archives.length, visibleCards]);

  // 点击空白区域取消选择
  const handleModalContentClick = useCallback((e: React.MouseEvent) => {
    // 如果点击的是模态框内容区域本身（不是卡片），则取消选择
    if (e.target === e.currentTarget) {
      setSelectedArchive(null);
    }
  }, []);

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={view === 'list' ? '管理档案库' : '新建档案库'}
      size="archive-manager"
      closeOnOverlayClick={true}
      closeOnEscape={true}
    >
      {view === 'list' ? (
        /* 管理档案库视图 - 严格按照自然语言描述布局 */
        <div
          className="flex flex-col h-full"
          style={{ padding: '16px 12px' }} /* (3) 上下边距16px，左右边距12px */
          onClick={handleModalContentClick} /* (9) 点击空白区域取消选择 */
        >
          {/* 错误提示 */}
          {error && (
            <div className="mb-4 p-3 bg-red-500 bg-opacity-20 border border-red-500 rounded text-red-400 text-sm">
              {error}
            </div>
          )}

          {/* 档案库卡片区域 - (5) 案例卡片整体居中 */}
          <div className="flex-1 flex items-center justify-center">
            <div className="flex items-center gap-4">
              {/* 左滚动按钮 */}
              <button
                onClick={handleScrollLeft}
                disabled={scrollPosition <= 0}
                className={`
                  w-10 h-10 rounded-full border flex items-center justify-center transition-all
                  ${scrollPosition <= 0
                    ? 'border-mizzy-border-base text-mizzy-text-content opacity-50 cursor-not-allowed'
                    : 'border-mizzy-border-base text-mizzy-text-title hover:border-mizzy-border-hover hover:bg-mizzy-bg-hover'
                  }
                `}
              >
                ←
              </button>

              {/* 档案库卡片容器 */}
              <div className="relative overflow-hidden" style={{ width: `${visibleCards * cardWidth}px` }}>
                <div
                  ref={scrollContainerRef}
                  className="flex gap-4 transition-transform duration-300"
                  style={{ transform: `translateX(-${scrollPosition}px)` }}
                >
                  {archives.map((archive) => (
                    <div
                      key={archive.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleArchiveSelect(archive);
                      }}
                      className={`
                        flex-shrink-0 cursor-pointer rounded-lg border-2 transition-all p-4
                        ${selectedArchive?.id === archive.id
                          ? 'border-mizzy-accent-primary bg-mizzy-accent-primary bg-opacity-10'
                          : 'border-mizzy-border-base hover:border-mizzy-border-hover'
                        }
                      `}
                      style={{
                        width: `${cardWidth - 16}px`, // (4) 约束案例卡片比例为横向4：3
                        aspectRatio: '4/3'
                      }}
                    >
                      {/* 档案库图标 */}
                      <div className="flex items-center justify-center text-3xl mb-3">
                        📁
                      </div>

                      {/* (8) 文字信息排列："档案库名称""文件数量""创建时间"竖向排列 */}
                      <div className="text-center space-y-1">
                        <div className="text-mizzy-text-title text-sm font-medium">
                          {archive.case_name}
                        </div>
                        <div className="text-mizzy-text-content text-xs">
                          {archive.file_count} 个文件
                        </div>
                        <div className="text-mizzy-text-content text-xs">
                          {new Date(archive.created_at).toLocaleDateString('zh-CN')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 右滚动按钮 */}
              <button
                onClick={handleScrollRight}
                disabled={scrollPosition >= Math.max(0, (archives.length - visibleCards) * cardWidth)}
                className={`
                  w-10 h-10 rounded-full border flex items-center justify-center transition-all
                  ${scrollPosition >= Math.max(0, (archives.length - visibleCards) * cardWidth)
                    ? 'border-mizzy-border-base text-mizzy-text-content opacity-50 cursor-not-allowed'
                    : 'border-mizzy-border-base text-mizzy-text-title hover:border-mizzy-border-hover hover:bg-mizzy-bg-hover'
                  }
                `}
              >
                →
              </button>
            </div>
          </div>

          {/* (8) "简介"在右侧单独显示 - 选中档案库信息 */}
          {selectedArchive && (
            <div className="mt-6 p-4 bg-mizzy-bg-main bg-opacity-30 rounded-lg">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="text-mizzy-text-title text-sm font-medium mb-2">
                    选中档案库：{selectedArchive.case_name}
                  </div>
                  <div className="text-mizzy-text-content text-xs">
                    {selectedArchive.file_count} 个文件 • {new Date(selectedArchive.created_at).toLocaleDateString('zh-CN')}
                  </div>
                </div>
                <div className="ml-4 text-right">
                  <div className="text-mizzy-text-content text-xs">
                    <span className="text-mizzy-text-title">简介：</span>
                    {selectedArchive.description || '无'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* (6) 【切换】【添加】【删除】【新增】全部挪到模态框最底下 - (2) 去除分隔线 */}
          <div className="mt-6 flex justify-between items-center">
            {/* 左侧按钮组 */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleArchiveSwitch}
                disabled={loading || !selectedArchive}
                className={`
                  ${modalButtonStyles.secondary}
                  ${loading || !selectedArchive ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                {loading ? '切换中...' : '切换'}
              </button>

              <button
                onClick={handleAddClick}
                disabled={loading}
                className={`
                  ${modalButtonStyles.secondary}
                  ${loading ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                添加
              </button>

              <button
                onClick={handleArchiveDelete}
                disabled={loading || !selectedArchive}
                className={`
                  ${modalButtonStyles.danger}
                  ${loading || !selectedArchive ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                {loading ? '删除中...' : '删除'}
              </button>
            </div>

            {/* 右侧新增按钮 */}
            <button
              onClick={handleSwitchToNew}
              disabled={loading}
              className={`
                ${modalButtonStyles.primary}
                ${loading ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              新增
            </button>
          </div>
        </div>
      ) : (
        /* 新建档案库视图 - 严格按照自然语言描述布局 */
        <div className="relative h-full" style={{ padding: '16px 12px' }}> {/* (2) 上下边距16px，左右边距12px */}
          {/* (3) 【返回】按钮在模态框左上角 */}
          <button
            onClick={handleSwitchToList}
            disabled={loading}
            className={`
              absolute top-0 left-0 w-8 h-8 flex items-center justify-center rounded-full
              ${modalButtonStyles.secondary} text-lg
              ${loading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            style={{ margin: '0' }}
          >
            ←
          </button>

          {/* 表单内容区域 */}
          <div className="flex flex-col justify-center items-center h-full pt-12">
            <div className="w-full max-w-md space-y-6">
              <div>
                <input
                  type="text"
                  value={archiveName}
                  onChange={(e) => setArchiveName(e.target.value)}
                  placeholder="请输入档案库名称"
                  className={modalInputStyles.base}
                  disabled={loading}
                />
              </div>

              <div>
                <textarea
                  value={archiveDescription}
                  onChange={(e) => setArchiveDescription(e.target.value)}
                  placeholder="请输入档案库介绍"
                  rows={4}
                  className={modalInputStyles.textarea}
                  disabled={loading}
                />
              </div>

              {/* (4) 【上传】和【新增】按钮居中，在简介输入框下方，与输入框竖向间隔20px */}
              <div className="flex justify-center gap-4" style={{ marginTop: '20px' }}>
                <button
                  onClick={openUploadModal}
                  disabled={loading}
                  className={`
                    ${modalButtonStyles.secondary}
                    ${loading ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  上传
                </button>
                <button
                  onClick={handleCreateArchive}
                  disabled={loading || !archiveName.trim()}
                  className={`
                    ${modalButtonStyles.primary}
                    ${loading || !archiveName.trim() ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  {loading ? '创建中...' : '新建'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </BaseModal>
  );
}
