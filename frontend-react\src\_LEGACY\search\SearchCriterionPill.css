/* SearchCriterionPill.css - Phase 7A: 搜索条件标签样式 */

/* ============================================================================
 * 单个搜索条件标签样式
 * ============================================================================ */

.search-criterion-pill {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  background-color: rgba(164, 159, 154, 0.1);
  border: 1px solid rgba(164, 159, 154, 0.3);
  border-radius: 16px;
  font-size: 12px;
  color: #A49F9A;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  max-width: 300px;
  user-select: none;
}

.search-criterion-pill:hover {
  background-color: rgba(164, 159, 154, 0.15);
  border-color: rgba(164, 159, 154, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-criterion-pill:focus {
  outline: none;
  border-color: #A49F9A;
  box-shadow: 0 0 0 2px rgba(164, 159, 154, 0.2);
}

.pill-content {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.field-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.field-label {
  font-weight: 500;
  color: #A49F9A;
  flex-shrink: 0;
}

.operator-label {
  font-style: italic;
  opacity: 0.8;
  flex-shrink: 0;
}

.value-label {
  font-weight: 600;
  color: #A49F9A;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.case-sensitive-indicator {
  font-size: 10px;
  background-color: rgba(164, 159, 154, 0.2);
  padding: 1px 4px;
  border-radius: 4px;
  font-weight: 500;
  flex-shrink: 0;
}

.pill-actions {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.search-criterion-pill:hover .pill-actions {
  opacity: 1;
}

.edit-button,
.remove-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  color: #A49F9A;
  cursor: pointer;
  border-radius: 50%;
  font-size: 10px;
  transition: all 0.2s ease;
  padding: 0;
}

.edit-button:hover {
  background-color: rgba(164, 159, 154, 0.2);
  color: #A49F9A;
}

.remove-button:hover {
  background-color: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.hover-indicator {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid rgba(164, 159, 154, 0.3);
  border-radius: 18px;
  pointer-events: none;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* ============================================================================
 * 搜索条件列表样式
 * ============================================================================ */

.search-criterion-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-criterion-list.empty {
  align-items: center;
  justify-content: center;
  padding: 16px;
  min-height: 60px;
}

.empty-message {
  color: #A49F9A;
  opacity: 0.6;
  font-style: italic;
  font-size: 14px;
}

.criteria-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.hidden-count-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: rgba(164, 159, 154, 0.05);
  border: 1px dashed rgba(164, 159, 154, 0.3);
  border-radius: 12px;
  font-size: 11px;
  color: #A49F9A;
  opacity: 0.7;
}

.clear-all-button {
  align-self: flex-start;
  padding: 6px 12px;
  background-color: transparent;
  border: 1px solid rgba(164, 159, 154, 0.3);
  border-radius: 6px;
  color: #A49F9A;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-button:hover {
  background-color: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

/* ============================================================================
 * 响应式设计
 * ============================================================================ */

@media (max-width: 600px) {
  .search-criterion-pill {
    max-width: 250px;
    font-size: 11px;
  }

  .value-label {
    max-width: 80px;
  }

  .criteria-container {
    gap: 4px;
  }

  .pill-actions {
    opacity: 1; /* 在移动设备上始终显示 */
  }
}

/* ============================================================================
 * 主题变体
 * ============================================================================ */

.search-criterion-pill.active {
  background-color: rgba(164, 159, 154, 0.2);
  border-color: #A49F9A;
}

.search-criterion-pill.error {
  background-color: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

.search-criterion-pill.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ============================================================================
 * 动画效果
 * ============================================================================ */

.search-criterion-pill {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.search-criterion-pill.removing {
  animation: slideOut 0.2s ease-in forwards;
}

@keyframes slideOut {
  to {
    opacity: 0;
    transform: translateX(10px) scale(0.9);
  }
}
