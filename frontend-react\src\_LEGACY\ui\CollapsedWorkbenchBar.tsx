import { useLayout } from '../../context/LayoutContext';
import { UpwardTriangleIcon } from '../icons/UpwardTriangleIcon';

export function CollapsedWorkbenchBar() {
  const { toggleWorkbench } = useLayout();

  return (
    <div style={{
      position: 'absolute',
      bottom: '12px', // 全局下边距
      left: '0', 
      right: '0',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '4px' // 全局间隔
    }}>
      {/* Third element: the icon */}
      <div
        onClick={toggleWorkbench}
        style={{
          cursor: 'pointer',
          padding: '4px',
          borderRadius: '4px',
          transition: 'background-color 0.2s ease',
          backgroundColor: 'transparent'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#353639';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
      >
        <UpwardTriangleIcon className="icon" />
      </div>
      
      {/* Second element: a placeholder for the 16px row */}
      <div style={{ height: '16px' }} />
 
      {/* First element from bottom: the 4px gap is handled by parent's 'gap' */}
    </div>
  );
}
