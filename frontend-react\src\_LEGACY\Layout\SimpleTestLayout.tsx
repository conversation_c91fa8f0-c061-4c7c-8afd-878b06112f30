import * as React from 'react';

/**
 * 简单的测试布局 - 用于验证分栏是否正常工作
 */
export interface SimpleTestLayoutProps {
  catalogPanel?: React.ReactNode;
  galleryPanel?: React.ReactNode;
  workbenchPanel?: React.ReactNode;
  infoPanel?: React.ReactNode;
  showCatalogPanel?: boolean;
  showInfoPanel?: boolean;
  showWorkbench?: boolean;
  isFullscreenGallery?: boolean;
}

const SimpleTestLayout: React.FC<SimpleTestLayoutProps> = ({
  catalogPanel,
  galleryPanel,
  workbenchPanel,
  infoPanel,
  showCatalogPanel = true,
  showInfoPanel = true,
  showWorkbench = false,
  isFullscreenGallery = false,
}) => {

  // console.log('SimpleTestLayout render:', { showCatalogPanel, showInfoPanel, showWorkbench, isFullscreenGallery }); // [CLEANED]

  // 全屏模式
  if (isFullscreenGallery) {
    return (
      <div className="h-screen w-screen bg-[#191012]">
        {galleryPanel}
      </div>
    );
  }

  return (
    <div
      style={{
        width: '100vw',
        height: '100vh',
        backgroundColor: '#191012',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* 顶部区域 - 水平分栏 */}
      <div
        style={{
          flex: 1,
          display: 'flex'
        }}
      >
        {/* 左侧目录栏 */}
        {showCatalogPanel && (
          <div
            style={{
              width: '280px',
              backgroundColor: '#040709',
              borderRight: '1px solid #2A2A2A',
              flexShrink: 0,
              minHeight: '100%'
            }}
          >
            {catalogPanel}
          </div>
        )}

        {/* 中央画廊 */}
        <div
          style={{
            flex: 1,
            backgroundColor: '#191012'
          }}
        >
          {galleryPanel}
        </div>

        {/* 右侧信息栏 */}
        {showInfoPanel && (
          <div
            style={{
              width: '280px',
              backgroundColor: '#040709',
              borderLeft: '1px solid #2A2A2A',
              flexShrink: 0,
              minHeight: '100%'
            }}
          >
            {infoPanel}
          </div>
        )}
      </div>

      {/* 底部工作台 */}
      {showWorkbench && (
        <div
          style={{
            height: '200px',
            backgroundColor: '#040709',
            borderTop: '1px solid #2A2A2A',
            flexShrink: 0,
            width: '100%'
          }}
        >
          {workbenchPanel}
        </div>
      )}
    </div>
  );
};

export { SimpleTestLayout };
