import React from 'react';

/**
 * StatusDisplay组件的属性接口
 * 用于统一处理加载、错误、空状态和正常状态的显示
 */
export interface StatusDisplayProps {
  /** 当前状态 */
  status: 'loading' | 'error' | 'empty' | 'idle';
  /** 主要消息内容 */
  message?: string | React.ReactNode;
  /** 详细错误信息（仅在error状态下显示） */
  errorMessage?: string;
  /** 正常状态下渲染的子组件 */
  children?: React.ReactNode;
  /** 自定义CSS类名 */
  className?: string;
}

/**
 * StatusDisplay - 统一的状态显示组件
 * 
 * 提供一致的加载、错误、空状态和正常状态的UI展示
 * 支持自定义消息和错误详情展开
 */
export const StatusDisplay: React.FC<StatusDisplayProps> = ({
  status,
  message,
  errorMessage,
  children,
  className = '',
}) => {
  const [showErrorDetails, setShowErrorDetails] = React.useState(false);

  // 正常状态：渲染子组件
  if (status === 'idle') {
    return <>{children}</>;
  }

  // 基础容器样式
  const containerClass = `status-display status-display--${status} ${className}`;

  // 加载状态
  if (status === 'loading') {
    return (
      <div className={containerClass}>
        <div className="status-display__content">
          <div className="status-display__spinner" aria-label="加载中">
            <div className="spinner"></div>
          </div>
          {message && (
            <div className="status-display__message animate-fade-in">
              {message}
            </div>
          )}
        </div>
      </div>
    );
  }

  // 错误状态
  if (status === 'error') {
    return (
      <div className={containerClass}>
        <div className="status-display__content">
          <div className="status-display__icon status-display__icon--error">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
              <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <div className="status-display__message animate-fade-in">
            {message || '发生了错误'}
          </div>
          {errorMessage && (
            <div className="status-display__error-details">
              <button
                className="status-display__toggle-details"
                onClick={() => setShowErrorDetails(!showErrorDetails)}
                aria-expanded={showErrorDetails}
              >
                {showErrorDetails ? '隐藏详情' : '显示详情'}
              </button>
              {showErrorDetails && (
                <div className="status-display__error-message animate-slide-up">
                  {errorMessage}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  // 空状态
  if (status === 'empty') {
    return (
      <div className={containerClass}>
        <div className="status-display__content">
          <div className="status-display__message animate-fade-in">
            {message || ''}
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default StatusDisplay;
