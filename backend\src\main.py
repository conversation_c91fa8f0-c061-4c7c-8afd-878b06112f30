# src/main.py
"""
案例管理系统 - 主应用程序
支持同步和异步操作，提升并发性能
"""
import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

from contextlib import asynccontextmanager
from fastapi import FastAPI

# 导入核心模块
from src.database import master_engine
from src.database_async import master_engine as async_master_engine, cleanup_connections
from src import models
# 🔥 BEDROCK: 直接导入核心路由器模块
from src.routers.cases import router as cases_router
# 🔥 BEDROCK: 注释掉非核心路由器导入
# from src.routers import trash, cases_async, quality, cover, tags


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时：初始化数据库表结构
    # 同步数据库初始化
    models.Case.metadata.create_all(bind=master_engine, tables=[
        models.Case.__table__,
        models.SystemConfig.__table__,
        models.CaseProcessingRule.__table__
    ])

    # 异步数据库初始化
    async with async_master_engine.begin() as conn:
        await conn.run_sync(models.Case.metadata.create_all, tables=[
            models.Case.__table__,
            models.SystemConfig.__table__,
            models.CaseProcessingRule.__table__
        ])
    
    yield
    
    # 关闭时：清理数据库连接
    await cleanup_connections()


# 创建FastAPI应用实例
app = FastAPI(
    title="智慧之眼 V1.0 - 案例管理系统 API",
    description="一个由可配置规则驱动的、高性能的标签数据核心。支持元信息的自动化、智能化采集与结构化存储，每个案例拥有独立的数据库，支持回收站功能和异步操作。",
    version="1.0.0",
    lifespan=lifespan
)

# 🔥 BEDROCK: 注册路由模块 - 只保留核心案例路由
app.include_router(cases_router, prefix="/api/v1")  # ✅ 核心骨架路由

# 🔥 BEDROCK: 注释掉所有非核心路由 - 非骨架功能
# app.include_router(trash.router, prefix="/api/v1")
# app.include_router(cases_async.router, prefix="/api/v1")  # 异步路由
# app.include_router(quality.router, prefix="/api/v1")  # 图像质量分析路由
# app.include_router(cover.router, prefix="/api/v1")  # 封面管理路由
# app.include_router(tags.router, prefix="/api/v1")  # 标签管理路由


@app.get("/", tags=["Root"])
def read_root():
    return {
        "message": "欢迎来到智慧之眼 V1.0 - 标签系统 API！",
        "version": "1.0.0",
        "features": [
            "multi-db",
            "trash-bin",
            "soft-delete",
            "async-operations",
            "connection-pooling",
            "batch-operations",
            "rule-driven-tagging",
            "flexible-tag-structure",
            "tag-based-filtering"
        ]
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """健康检查端点"""
    try:
        from .database_async import get_pool_status
        pool_status = await get_pool_status()
        
        return {
            "status": "healthy",
            "version": "1.0.0",
            "features": [
                "multi-db",
                "trash-bin",
                "soft-delete",
                "async-operations",
                "connection-pooling",
                "rule-driven-tagging",
                "flexible-tag-structure",
                "tag-based-filtering"
            ],
            "database": {
                "sync_engine": "active",
                "async_engine": "active",
                "pool_status": pool_status
            }
        }
    except Exception as e:
        return {
            "status": "degraded",
            "version": "1.0.0",
            "error": str(e)
        }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )

