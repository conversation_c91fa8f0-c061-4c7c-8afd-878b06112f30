import { useLayout } from "../../context/LayoutContext";
import { WorkbenchToggleIcon } from "../icons/WorkbenchToggleIcon";

export function WorkbenchToggleButton() {
  const { isWorkbenchCollapsed, toggleWorkbench } = useLayout();

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 'var(--spacing-2)',
        marginTop: 'auto',
        cursor: 'pointer'
      }}
      onClick={toggleWorkbench}
    >
      <WorkbenchToggleIcon isUp={!isWorkbenchCollapsed} />
    </div>
  );
}
