// useApiStatusStore.test.ts - Phase 8A: API状态store测试

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import {
  useApiStatusStore,
  useRateLimitStatus,
  useServiceHealth,
  useAiErrors,
  usePerformanceMetrics
} from '../useApiStatusStore';
import type { RateLimitInfo, AiError } from '@/lib/aiClient';

describe('useApiStatusStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useApiStatusStore.getState().resetApiStatus();
  });

  describe('Initial State', () => {
    it('应该有正确的初始状态', () => {
      const { result } = renderHook(() => useApiStatusStore());

      expect(result.current.suggestionLimitRemaining).toBe(100);
      expect(result.current.queryLimitRemaining).toBe(200);
      expect(result.current.limitResetTime).toBeNull();
      expect(result.current.isSuggestionServiceThrottled).toBe(false);
      expect(result.current.isQueryServiceThrottled).toBe(false);
      expect(result.current.serviceHealth).toBe('healthy');
      expect(result.current.lastAiError).toBeNull();
      expect(result.current.errorCount).toBe(0);
    });
  });

  describe('Rate Limit Management', () => {
    it('应该更新标签建议速率限制', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const rateLimitInfo: RateLimitInfo = {
        limit: 100,
        remaining: 45,
        reset: Date.now() + 3600000, // 1 hour from now
        type: 'tag-suggestion'
      };

      act(() => {
        result.current.updateRateLimit(rateLimitInfo);
      });

      expect(result.current.suggestionLimitRemaining).toBe(45);
      expect(result.current.limitResetTime).toBe(rateLimitInfo.reset);
      expect(result.current.isSuggestionServiceThrottled).toBe(false);
    });

    it('应该在限制耗尽时设置节流状态', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const rateLimitInfo: RateLimitInfo = {
        limit: 100,
        remaining: 0,
        reset: Date.now() + 3600000,
        type: 'tag-suggestion'
      };

      act(() => {
        result.current.updateRateLimit(rateLimitInfo);
      });

      expect(result.current.suggestionLimitRemaining).toBe(0);
      expect(result.current.isSuggestionServiceThrottled).toBe(true);
    });

    it('应该在限制较低时显示警告', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const rateLimitInfo: RateLimitInfo = {
        limit: 100,
        remaining: 5, // Low remaining count
        reset: Date.now() + 3600000,
        type: 'query-parsing'
      };

      act(() => {
        result.current.updateRateLimit(rateLimitInfo);
      });

      expect(result.current.showRateLimitWarning).toBe(true);
    });

    it('应该检查节流状态', () => {
      const { result } = renderHook(() => useApiStatusStore());

      // Set throttled state
      act(() => {
        result.current.updateRateLimit({
          limit: 100,
          remaining: 0,
          reset: Date.now() + 3600000,
          type: 'tag-suggestion'
        });
      });

      const throttleStatus = result.current.checkThrottleStatus();
      expect(throttleStatus.suggestion).toBe(true);
      expect(throttleStatus.query).toBe(false);
    });

    it('应该计算剩余重置时间', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const resetTime = Date.now() + 60000; // 1 minute from now

      act(() => {
        result.current.updateRateLimit({
          limit: 100,
          remaining: 0,
          reset: resetTime,
          type: 'tag-suggestion'
        });
      });

      const remainingTime = result.current.getRemainingTime();
      expect(remainingTime).toBeGreaterThan(50); // Should be around 60 seconds
      expect(remainingTime).toBeLessThan(70);
    });
  });

  describe('Error Management', () => {
    it('应该记录AI错误', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const aiError: AiError = {
        success: false,
        error: 'AI_ANALYSIS_FAILED',
        error_code: 'AI001',
        message: '不支持的文件格式',
        timestamp: '2025-07-28T10:00:00Z'
      };

      act(() => {
        result.current.recordAiError(aiError);
      });

      expect(result.current.lastAiError).toEqual(aiError);
      expect(result.current.errorCount).toBe(1);
      expect(result.current.lastErrorTime).toBeDefined();
    });

    it('应该在错误过多时降级服务状态', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const aiError: AiError = {
        success: false,
        error: 'AI_SERVICE_ERROR',
        error_code: 'AI001', // 使用不以AI50开头的错误码
        message: '服务暂时不可用',
        timestamp: '2025-07-28T10:00:00Z'
      };

      // Record multiple errors to trigger degradation (3 errors = degraded, 5+ errors = unavailable)
      act(() => {
        for (let i = 0; i < 4; i++) {
          result.current.recordAiError(aiError);
        }
      });

      expect(result.current.serviceHealth).toBe('degraded');
      expect(result.current.errorCount).toBe(4);
    });

    it('应该识别可重试的错误', () => {
      const { result } = renderHook(() => useApiStatusStore());

      expect(result.current.shouldRetryRequest('AI501')).toBe(true);
      expect(result.current.shouldRetryRequest('AI503')).toBe(true);
      expect(result.current.shouldRetryRequest('AI504')).toBe(true);
      expect(result.current.shouldRetryRequest('NET001')).toBe(true);
      expect(result.current.shouldRetryRequest('AI001')).toBe(false);
    });

    it('应该清除最后的错误', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const aiError: AiError = {
        success: false,
        error: 'TEST_ERROR',
        error_code: 'TEST001',
        message: '测试错误',
        timestamp: '2025-07-28T10:00:00Z'
      };

      act(() => {
        result.current.recordAiError(aiError);
      });

      expect(result.current.lastAiError).toEqual(aiError);

      act(() => {
        result.current.clearLastError();
      });

      expect(result.current.lastAiError).toBeNull();
    });
  });

  describe('Service Health Management', () => {
    it('应该更新服务健康状态', () => {
      const { result } = renderHook(() => useApiStatusStore());

      act(() => {
        result.current.updateServiceHealth('degraded');
      });

      expect(result.current.serviceHealth).toBe('degraded');
      expect(result.current.lastHealthCheck).toBeDefined();
    });

    it('应该在健康状态恢复时重置错误计数', () => {
      const { result } = renderHook(() => useApiStatusStore());

      // First, record some errors
      act(() => {
        result.current.recordAiError({
          success: false,
          error: 'TEST_ERROR',
          error_code: 'TEST001',
          message: '测试错误',
          timestamp: '2025-07-28T10:00:00Z'
        });
      });

      expect(result.current.errorCount).toBe(1);

      // Then, update to healthy status
      act(() => {
        result.current.updateServiceHealth('healthy');
      });

      expect(result.current.serviceHealth).toBe('healthy');
      expect(result.current.errorCount).toBe(0);
    });

    it('应该更新可用服务列表', () => {
      const { result } = renderHook(() => useApiStatusStore());

      const services = ['tag-suggestion', 'query-parsing'];

      act(() => {
        result.current.updateAvailableServices(services);
      });

      expect(result.current.availableServices).toEqual(services);
    });

    it('应该执行健康检查', async () => {
      const { result } = renderHook(() => useApiStatusStore());

      await act(async () => {
        await result.current.performHealthCheck();
      });

      expect(result.current.serviceHealth).toBe('healthy');
      expect(result.current.lastHealthCheck).toBeDefined();
      expect(result.current.availableServices).toContain('tag-suggestion');
    });
  });

  describe('Performance Metrics', () => {
    it('应该记录成功请求', () => {
      const { result } = renderHook(() => useApiStatusStore());

      act(() => {
        result.current.recordRequest(true, 500);
      });

      expect(result.current.performanceMetrics.totalRequests).toBe(1);
      expect(result.current.performanceMetrics.failedRequests).toBe(0);
      expect(result.current.performanceMetrics.successRate).toBe(1.0);
      expect(result.current.performanceMetrics.avgResponseTime).toBe(500);
    });

    it('应该记录失败请求', () => {
      const { result } = renderHook(() => useApiStatusStore());

      act(() => {
        result.current.recordRequest(false, 1000);
      });

      expect(result.current.performanceMetrics.totalRequests).toBe(1);
      expect(result.current.performanceMetrics.failedRequests).toBe(1);
      expect(result.current.performanceMetrics.successRate).toBe(0.0);
    });

    it('应该生成性能报告', () => {
      const { result } = renderHook(() => useApiStatusStore());

      act(() => {
        result.current.recordRequest(true, 500);
        result.current.recordRequest(true, 300);
        result.current.recordRequest(false, 1000);
      });

      const report = result.current.getPerformanceReport();
      expect(report).toContain('平均响应时间');
      expect(report).toContain('成功率');
      expect(report).toContain('总请求数 3');
    });
  });

  describe('UI State Management', () => {
    it('应该管理速率限制警告显示', () => {
      const { result } = renderHook(() => useApiStatusStore());

      // Trigger warning
      act(() => {
        result.current.updateRateLimit({
          limit: 100,
          remaining: 5,
          reset: Date.now() + 3600000,
          type: 'tag-suggestion'
        });
      });

      expect(result.current.showRateLimitWarning).toBe(true);

      // Dismiss warning
      act(() => {
        result.current.dismissRateLimitWarning();
      });

      expect(result.current.showRateLimitWarning).toBe(false);
    });

    it('应该管理服务消息显示', () => {
      const { result } = renderHook(() => useApiStatusStore());

      // Trigger service message
      act(() => {
        result.current.updateServiceHealth('unavailable');
      });

      expect(result.current.showServiceUnavailableMessage).toBe(true);

      // Dismiss message
      act(() => {
        result.current.dismissServiceMessage();
      });

      expect(result.current.showServiceUnavailableMessage).toBe(false);
    });
  });

  describe('Store Reset', () => {
    it('应该重置所有状态', () => {
      const { result } = renderHook(() => useApiStatusStore());

      // Modify some state
      act(() => {
        result.current.updateRateLimit({
          limit: 100,
          remaining: 50,
          reset: Date.now() + 3600000,
          type: 'tag-suggestion'
        });
        result.current.recordAiError({
          success: false,
          error: 'TEST_ERROR',
          error_code: 'TEST001',
          message: '测试错误',
          timestamp: '2025-07-28T10:00:00Z'
        });
      });

      expect(result.current.suggestionLimitRemaining).toBe(50);
      expect(result.current.errorCount).toBe(1);

      // Reset
      act(() => {
        result.current.resetApiStatus();
      });

      expect(result.current.suggestionLimitRemaining).toBe(100);
      expect(result.current.queryLimitRemaining).toBe(200);
      expect(result.current.errorCount).toBe(0);
      expect(result.current.lastAiError).toBeNull();
      expect(result.current.serviceHealth).toBe('healthy');
    });
  });
});

describe('Store Selectors', () => {
  beforeEach(() => {
    useApiStatusStore.getState().resetApiStatus();
  });

  describe('useRateLimitStatus', () => {
    it('应该返回速率限制状态', () => {
      const { result } = renderHook(() => useRateLimitStatus());

      expect(result.current.suggestionRemaining).toBe(100);
      expect(result.current.queryRemaining).toBe(200);
      expect(result.current.isSuggestionThrottled).toBe(false);
      expect(result.current.isQueryThrottled).toBe(false);
      expect(typeof result.current.getRemainingTime).toBe('function');
      expect(typeof result.current.dismissWarning).toBe('function');
    });
  });

  describe('useServiceHealth', () => {
    it('应该返回服务健康状态', () => {
      const { result } = renderHook(() => useServiceHealth());

      expect(result.current.health).toBe('healthy');
      expect(result.current.availableServices).toEqual([]);
      expect(result.current.lastCheck).toBeNull();
      expect(typeof result.current.performHealthCheck).toBe('function');
      expect(typeof result.current.dismissMessage).toBe('function');
    });
  });

  describe('useAiErrors', () => {
    it('应该返回AI错误状态', () => {
      const { result } = renderHook(() => useAiErrors());

      expect(result.current.lastError).toBeNull();
      expect(result.current.errorCount).toBe(0);
      expect(typeof result.current.shouldRetry).toBe('function');
      expect(typeof result.current.clearError).toBe('function');
    });
  });

  describe('usePerformanceMetrics', () => {
    it('应该返回性能指标', () => {
      const { result } = renderHook(() => usePerformanceMetrics());

      expect(result.current.metrics.totalRequests).toBe(0);
      expect(result.current.metrics.successRate).toBe(1.0);
      expect(typeof result.current.getReport).toBe('function');
      expect(typeof result.current.recordRequest).toBe('function');
    });
  });
});
