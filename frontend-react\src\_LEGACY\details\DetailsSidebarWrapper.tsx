// DetailsSidebarWrapper - Feature flag controlled details sidebar component wrapper
// 功能开关控制的详情侧边栏包装器

import React from 'react';
import { useFeatureFlag } from '@/utils/hooks/useFeatureFlags';
import { DetailsSidebar } from './DetailsSidebar';
import { InfoPanel } from '@/components/panels/InfoPanel';
import { useUIStore, useFileSystemState } from '@/store';
import { shallow } from 'zustand/shallow';
// Phase 3: 移除useFiles依赖，使用文件系统状态
// import { useFiles } from '@/hooks/useApi';

// ============================================================================
// 接口定义
// ============================================================================

interface DetailsSidebarWrapperProps {
  className?: string;
}

// ============================================================================
// DetailsSidebarWrapper 组件
// ============================================================================

export const DetailsSidebarWrapper: React.FC<DetailsSidebarWrapperProps> = (props) => {
  // 检查功能开关
  const useNewDetailsSidebar = useFeatureFlag('useNewDetailsSidebar');

  // Phase 3: 获取状态和数据，使用文件系统状态 - 使用专用选择器避免无限循环
  const {
    selectedFileIds,
    activePanels,
    toggleInfoPanelSection,
  } = useUIStore((state) => ({
    selectedFileIds: state.selectedFileIds,
    activePanels: state.activePanels,
    toggleInfoPanelSection: state.toggleInfoPanelSection,
  }), shallow);

  // Phase 3: 使用文件系统状态获取选中的文件
  const { selectedFile } = useFileSystemState();

  // 如果启用新详情栏，使用新组件
  if (useNewDetailsSidebar) {
    return <DetailsSidebar {...props} />;
  }

  // Phase 3: 使用原有的InfoPanel，但数据来源于文件系统状态
  const handleTagOperation = (operation: 'add' | 'remove', category: string, tag: string) => {
    // TODO: 实现标签操作逻辑
    // console.log('Phase 3: Tag operation:', operation, category, tag); // [CLEANED]
  };

  // Phase 3: 格式化文件时间
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <InfoPanel
      selectedFile={selectedFile ? {
        id: 1, // 临时ID
        fileName: selectedFile.name,
        filePath: selectedFile.path,
        fileType: selectedFile.isDirectory ? 'directory' : 'file',
        fileSize: selectedFile.size,
        width: undefined, // Phase 3: 暂时无法获取图片尺寸
        height: undefined,
        thumbnailPath: selectedFile.thumbnail, // Phase 3: 使用生成的缩略图
        createdAt: formatTimestamp(selectedFile.createdAt),
        tags: undefined, // Phase 3: 暂时无标签数据
      } : undefined}
      selectedCount={selectedFile ? 1 : 0} // Phase 3: 基于selectedFile状态
      activePanels={activePanels}
      onPanelToggle={toggleInfoPanelSection}
      onTagOperation={handleTagOperation}
      {...props}
    />
  );
};

// ============================================================================
// 导出
// ============================================================================

export default DetailsSidebarWrapper;
