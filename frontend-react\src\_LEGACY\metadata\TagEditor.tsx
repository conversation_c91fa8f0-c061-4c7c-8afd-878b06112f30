// TagEditor.tsx - Phase 7B: 标签编辑器组件 (后端驱动版)
// 完全依赖后端API的标签管理界面

import React, { useState, useEffect } from 'react';
import { useCurrentFileTags, useTagOperations, useMetadataErrors, useCurrentCase } from '@/store';
// Phase 8A: 导入AI相关功能
import { getAiClient, isAiServiceAvailable, isThrottled } from '@/lib/aiClientInstance';
import { useRateLimitStatus, useServiceHealth, useAiErrors } from '@/store';
import type { TagSuggestion } from '@/lib/aiClient';
import './TagEditor.css';

// ============================================================================
// 类型定义
// ============================================================================

interface TagEditorProps {
  filePath: string | null;
  className?: string;
}

// Phase 8A: AI建议相关类型
interface AiSuggestionPillProps {
  suggestion: TagSuggestion;
  onAdopt: (suggestion: TagSuggestion) => void;
  disabled?: boolean;
}

interface TagPillProps {
  tagName: string;
  tagType: 'user' | 'ai' | 'metadata' | 'cv';
  onRemove?: () => void;
  removable?: boolean;
}

// ============================================================================
// 标签药丸组件
// ============================================================================

const TagPill: React.FC<TagPillProps> = ({
  tagName,
  tagType,
  onRemove,
  removable = false
}) => {
  const getTagTypeIcon = (type: string) => {
    switch (type) {
      case 'user': return '🏷️';
      case 'ai': return '🤖';
      case 'metadata': return '📷';
      case 'cv': return '👁️';
      default: return '🏷️';
    }
  };

  const getTagTypeClass = (type: string) => {
    switch (type) {
      case 'user': return 'tag-pill--user';
      case 'ai': return 'tag-pill--ai';
      case 'metadata': return 'tag-pill--metadata';
      case 'cv': return 'tag-pill--cv';
      default: return 'tag-pill--user';
    }
  };

  return (
    <div className={`tag-pill ${getTagTypeClass(tagType)}`}>
      <span className="tag-pill__icon">
        {getTagTypeIcon(tagType)}
      </span>
      <span className="tag-pill__name">
        {tagName}
      </span>
      {removable && onRemove && (
        <button
          className="tag-pill__remove"
          onClick={onRemove}
          title={`删除标签 "${tagName}"`}
        >
          ×
        </button>
      )}
    </div>
  );
};

// ============================================================================
// Phase 8A: AI建议药丸组件
// ============================================================================

const AiSuggestionPill: React.FC<AiSuggestionPillProps> = ({
  suggestion,
  onAdopt,
  disabled = false
}) => {
  const getConfidenceClass = (confidence: number) => {
    if (confidence >= 0.9) return 'ai-suggestion--high';
    if (confidence >= 0.75) return 'ai-suggestion--medium';
    return 'ai-suggestion--low';
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'content-analysis': return '🖼️';
      case 'filename-analysis': return '📝';
      case 'metadata-analysis': return '📊';
      case 'context-learning': return '🧠';
      default: return '🤖';
    }
  };

  return (
    <button
      className={`ai-suggestion ${getConfidenceClass(suggestion.confidence)} ${disabled ? 'ai-suggestion--disabled' : ''}`}
      onClick={() => !disabled && onAdopt(suggestion)}
      disabled={disabled}
      title={`${suggestion.reasoning} (置信度: ${(suggestion.confidence * 100).toFixed(0)}%)`}
    >
      <span className="ai-suggestion__icon">
        {getSourceIcon(suggestion.source)}
      </span>
      <span className="ai-suggestion__text">
        {suggestion.tag}
      </span>
      <span className="ai-suggestion__confidence">
        {(suggestion.confidence * 100).toFixed(0)}%
      </span>
    </button>
  );
};

// ============================================================================
// 主组件
// ============================================================================

export const TagEditor: React.FC<TagEditorProps> = ({
  filePath,
  className = ''
}) => {
  // Store状态
  const { tags, isLoading } = useCurrentFileTags();
  const { addTag, removeTag, isSaving } = useTagOperations();
  const { error, clearError } = useMetadataErrors();

  // Phase 8A: AI相关状态
  const { currentCaseId } = useCurrentCase();
  const { isSuggestionThrottled } = useRateLimitStatus();
  const { health: serviceHealth } = useServiceHealth();
  const { lastError: aiError } = useAiErrors();

  // 本地状态
  const [newTagName, setNewTagName] = useState('');
  const [isAddingTag, setIsAddingTag] = useState(false);

  // Phase 8A: AI建议状态
  const [aiSuggestions, setAiSuggestions] = useState<TagSuggestion[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [suggestionsError, setSuggestionsError] = useState<string | null>(null);
  const [lastSuggestedFile, setLastSuggestedFile] = useState<string | null>(null);

  // ========================================
  // Phase 8A: AI建议加载逻辑
  // ========================================

  const loadAiSuggestions = async (targetFilePath: string) => {
    if (!currentCaseId || !targetFilePath || isThrottled('suggestion') || !isAiServiceAvailable()) {
      return;
    }

    // 避免重复加载同一文件的建议
    if (lastSuggestedFile === targetFilePath && aiSuggestions.length > 0) {
      return;
    }

    setIsLoadingSuggestions(true);
    setSuggestionsError(null);

    try {
      // console.log(`🧠 Phase 8A: 加载AI标签建议 - 文件: ${targetFilePath}`); // [CLEANED]

      // 模拟AI建议（实际实现时会调用真实API）
      const mockSuggestions: TagSuggestion[] = [
        {
          tag: '风景摄影',
          category: 'cv',
          confidence: 0.95,
          source: 'content-analysis',
          reasoning: '图像包含自然风景元素，构图符合风景摄影特征',
          metadata: {
            detected_objects: ['mountain', 'sky', 'tree'],
            dominant_colors: ['#4A90E2', '#7ED321', '#8B572A']
          }
        },
        {
          tag: '高质量',
          category: 'metadata',
          confidence: 0.89,
          source: 'metadata-analysis',
          reasoning: '图像质量分数较高，符合高质量标准',
          metadata: {
            quality_score: 87.5,
            sharpness: 0.91
          }
        },
        {
          tag: '春季',
          category: 'ai',
          confidence: 0.76,
          source: 'context-learning',
          reasoning: '基于案例历史标签模式和文件创建时间推断',
          metadata: {
            creation_month: 4,
            similar_files_count: 15
          }
        }
      ];

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      setAiSuggestions(mockSuggestions);
      setLastSuggestedFile(targetFilePath);

      // console.log(`✅ Phase 8A: 获得 ${mockSuggestions.length} 个AI标签建议`); // [CLEANED]

    } catch (error: any) {
      console.error('❌ Phase 8A: AI标签建议加载失败:', error);
      setSuggestionsError(error.message || 'AI建议加载失败');
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  // 文件变化时自动加载AI建议
  useEffect(() => {
    if (filePath && filePath !== lastSuggestedFile) {
      loadAiSuggestions(filePath);
    }
  }, [filePath, currentCaseId]);

  // ========================================
  // 事件处理
  // ========================================

  const handleAddTag = async () => {
    if (!filePath || !newTagName.trim()) return;

    setIsAddingTag(true);
    const success = await addTag(filePath, newTagName.trim());

    if (success) {
      setNewTagName('');
      // console.log(`✅ 标签添加成功: "${newTagName}"`); // [CLEANED]
    } else {
      console.error(`❌ 标签添加失败: "${newTagName}"`);
    }

    setIsAddingTag(false);
  };

  const handleRemoveTag = async (tagName: string) => {
    if (!filePath) return;

    const success = await removeTag(filePath, tagName);

    if (success) {
      // console.log(`✅ 标签删除成功: "${tagName}"`); // [CLEANED]
    } else {
      console.error(`❌ 标签删除失败: "${tagName}"`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Phase 8A: AI建议采纳处理
  const handleAdoptSuggestion = async (suggestion: TagSuggestion) => {
    if (!filePath) return;

    // console.log(`🤖 Phase 8A: 采纳AI建议 - "${suggestion.tag}" (置信度: ${suggestion.confidence})`); // [CLEANED]

    const success = await addTag(filePath, suggestion.tag);

    if (success) {
      // 从建议列表中移除已采纳的建议
      setAiSuggestions(prev => prev.filter(s => s.tag !== suggestion.tag));
      // console.log(`✅ Phase 8A: AI建议采纳成功: "${suggestion.tag}"`); // [CLEANED]
    } else {
      console.error(`❌ Phase 8A: AI建议采纳失败: "${suggestion.tag}"`);
    }
  };

  // ========================================
  // 渲染辅助函数
  // ========================================

  const renderTagSection = (
    title: string,
    tags: string[],
    tagType: 'user' | 'ai' | 'metadata' | 'cv',
    removable: boolean = false
  ) => {
    if (tags.length === 0) return null;

    return (
      <div className="tag-section">
        <h4 className="tag-section__title">{title}</h4>
        <div className="tag-section__pills">
          {tags.map((tagName, index) => (
            <TagPill
              key={`${tagType}-${tagName}-${index}`}
              tagName={tagName}
              tagType={tagType}
              removable={removable}
              onRemove={removable ? () => handleRemoveTag(tagName) : undefined}
            />
          ))}
        </div>
      </div>
    );
  };

  const renderMetadataSection = (metadata: Record<string, any>) => {
    const metadataTags = Object.entries(metadata).map(([key, value]) =>
      `${key}: ${value}`
    );

    if (metadataTags.length === 0) return null;

    return renderTagSection('📷 元数据', metadataTags, 'metadata', false);
  };

  // ========================================
  // 渲染
  // ========================================

  if (!filePath) {
    return (
      <div className={`tag-editor tag-editor--empty ${className}`}>
        <div className="tag-editor__placeholder">
          <span className="tag-editor__placeholder-icon">🏷️</span>
          <p className="tag-editor__placeholder-text">
            选择文件以查看和编辑标签
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`tag-editor tag-editor--loading ${className}`}>
        <div className="tag-editor__loading">
          <div className="tag-editor__spinner"></div>
          <p>正在加载标签...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`tag-editor tag-editor--error ${className}`}>
        <div className="tag-editor__error">
          <span className="tag-editor__error-icon">⚠️</span>
          <p className="tag-editor__error-message">{error}</p>
          <button
            className="tag-editor__error-retry"
            onClick={clearError}
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`tag-editor ${className}`}>
      {/* 文件信息 */}
      <div className="tag-editor__header">
        <h3 className="tag-editor__title">文件标签</h3>
        <p className="tag-editor__file-path" title={filePath}>
          {filePath.split(/[/\\]/).pop()}
        </p>
      </div>

      {/* 标签添加区域 */}
      <div className="tag-editor__add-section">
        <div className="tag-editor__input-group">
          <input
            type="text"
            className="tag-editor__input"
            placeholder="输入新标签..."
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isSaving || isAddingTag}
          />
          <button
            type="button"
            className="tag-editor__add-button"
            onClick={handleAddTag}
            disabled={!newTagName.trim() || isSaving || isAddingTag}
          >
            {isAddingTag ? '添加中...' : '添加'}
          </button>
        </div>
      </div>

      {/* Phase 8A: AI建议区域 */}
      {(isLoadingSuggestions || aiSuggestions.length > 0 || suggestionsError) && (
        <div className="tag-editor__ai-section">
          <h4 className="tag-editor__ai-title">
            🧠 AI建议
            {serviceHealth !== 'healthy' && (
              <span className="tag-editor__service-status">
                ({serviceHealth === 'degraded' ? '服务降级' : '服务不可用'})
              </span>
            )}
          </h4>

          {isLoadingSuggestions && (
            <div className="tag-editor__ai-loading">
              <div className="tag-editor__spinner tag-editor__spinner--small"></div>
              <span>AI正在分析文件内容...</span>
            </div>
          )}

          {suggestionsError && (
            <div className="tag-editor__ai-error">
              <span className="tag-editor__error-icon">⚠️</span>
              <span>{suggestionsError}</span>
              <button
                className="tag-editor__retry-button"
                onClick={() => filePath && loadAiSuggestions(filePath)}
                disabled={isLoadingSuggestions}
              >
                重试
              </button>
            </div>
          )}

          {aiSuggestions.length > 0 && (
            <div className="tag-editor__ai-suggestions animate-fade-in">
              {aiSuggestions.map((suggestion, index) => (
                <div
                  key={`${suggestion.tag}-${index}`}
                  className="animate-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <AiSuggestionPill
                    suggestion={suggestion}
                    onAdopt={handleAdoptSuggestion}
                    disabled={isSaving || isAddingTag}
                  />
                </div>
              ))}
            </div>
          )}

          {isSuggestionThrottled && (
            <div className="tag-editor__throttle-warning">
              <span className="tag-editor__warning-icon">⏱️</span>
              <span>AI建议服务已达到使用限制，请稍后再试</span>
            </div>
          )}
        </div>
      )}

      {/* 标签显示区域 */}
      <div className="tag-editor__tags">
        {tags && (
          <>
            {renderTagSection('🏷️ 用户标签', tags.user, 'user', true)}
            {renderTagSection('🤖 AI标签', tags.ai, 'ai', false)}
            {renderTagSection('👁️ 计算机视觉', tags.cv, 'cv', false)}
            {renderMetadataSection(tags.metadata)}
          </>
        )}

        {tags && Object.values(tags).every(tagArray =>
          Array.isArray(tagArray) ? tagArray.length === 0 : Object.keys(tagArray).length === 0
        ) && (
          <div className="tag-editor__empty-state">
            <span className="tag-editor__empty-icon">🏷️</span>
            <p>此文件暂无标签</p>
            <p className="tag-editor__empty-hint">
              在上方输入框中添加第一个标签
            </p>
          </div>
        )}
      </div>

      {/* 状态指示器 */}
      {isSaving && (
        <div className="tag-editor__status">
          <div className="tag-editor__spinner tag-editor__spinner--small"></div>
          <span>保存中...</span>
        </div>
      )}
    </div>
  );
};

export default TagEditor;
