// GalleryPanel.tsx - 新架构画廊面板
// 集成档案库选择器和文件导入功能

import React, { useState, useEffect } from 'react';
import { Search, Grid3X3, List, Settings, X, RefreshCw } from 'lucide-react';
import { CaseSelectorDropdown } from './CaseSelectorDropdown';
import { ImportImageButton } from './ImportImageButton';
import { ICase, IFile, getCaseFiles, importFilesByPath, getThumbnailUrl } from '../../services/apiService';

interface GalleryPanelProps {
  className?: string;
}

export function GalleryPanel({ className = '' }: GalleryPanelProps) {
  // ========================================
  // 状态管理
  // ========================================
  const [activeCase, setActiveCase] = useState<ICase | null>(null);
  const [files, setFiles] = useState<IFile[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<IFile[]>([]);
  const [selectedFileIds, setSelectedFileIds] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  // ========================================
  // 副作用
  // ========================================

  // 当选择档案库时，加载文件列表
  useEffect(() => {
    if (activeCase) {
      loadCaseFiles(activeCase.id);
    } else {
      setFiles([]);
      setFilteredFiles([]);
    }
  }, [activeCase]);

  // 搜索过滤
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredFiles(files);
    } else {
      const filtered = files.filter(file =>
        file.file_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredFiles(filtered);
    }
  }, [files, searchQuery]);

  // ========================================
  // 事件处理函数
  // ========================================

  const loadCaseFiles = async (caseId: number) => {
    try {
      setIsLoading(true);
      // console.log(`📁 加载档案库文件: ${caseId}`); // [CLEANED]
      const caseFiles = await getCaseFiles(caseId);
      setFiles(caseFiles);
      // console.log(`✅ 加载了 ${caseFiles.length} 个文件`); // [CLEANED]
    } catch (error) {
      console.error('❌ 加载文件列表失败:', error);
      alert('加载文件列表失败，请重试。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCaseSelect = (selectedCase: ICase) => {
    // console.log('📋 切换档案库:', selectedCase.case_name); // [CLEANED]
    setActiveCase(selectedCase);
    setSelectedFileIds([]); // 清空选中状态
  };

  const handleFilesSelected = async (filePaths: string[]) => {
    if (!activeCase) {
      alert('请先选择一个档案库');
      return;
    }

    try {
      setIsImporting(true);
      // console.log(`📁 开始导入 ${filePaths.length} 个文件到档案库 ${activeCase.case_name}`); // [CLEANED]
      
      const importedFiles = await importFilesByPath(activeCase.id, filePaths);
      
      // 更新文件列表
      setFiles(prev => [...importedFiles, ...prev]);
      
      // console.log(`✅ 成功导入 ${importedFiles.length} 个文件`); // [CLEANED]
      alert(`成功导入 ${importedFiles.length} 个文件到档案库 "${activeCase.case_name}"`);
    } catch (error) {
      console.error('❌ 文件导入失败:', error);
      alert('文件导入失败，请检查文件路径和后端连接。');
    } finally {
      setIsImporting(false);
    }
  };

  const handleFileClick = (fileId: number) => {
    setSelectedFileIds(prev => {
      if (prev.includes(fileId)) {
        return prev.filter(id => id !== fileId);
      } else {
        return [...prev, fileId];
      }
    });
  };

  const handleRefresh = () => {
    if (activeCase) {
      loadCaseFiles(activeCase.id);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  // ========================================
  // 渲染函数
  // ========================================

  const renderFileGrid = () => {
    const imageSize = 200; // 固定图片大小

    return (
      <div
        className="grid gap-2 p-4"
        style={{
          gridTemplateColumns: `repeat(auto-fill, minmax(${imageSize}px, 1fr))`,
        }}
      >
        {filteredFiles.map((file) => {
          const isSelected = selectedFileIds.includes(file.id);
          const thumbnailUrl = file.thumbnail_small_path 
            ? getThumbnailUrl(activeCase!.id, file.id)
            : '/placeholder-image.png';

          return (
            <div
              key={file.id}
              className={`relative group cursor-pointer rounded-lg overflow-hidden transition-all ${
                isSelected
                  ? 'ring-2 ring-blue-500'
                  : 'hover:ring-1 hover:ring-gray-400'
              }`}
              onClick={() => handleFileClick(file.id)}
              style={{ height: imageSize }}
            >
              {/* 图片 */}
              <img
                src={thumbnailUrl}
                alt={file.file_name}
                className="w-full h-full object-cover"
                loading="lazy"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder-image.png';
                }}
              />

              {/* 选中状态指示器 */}
              {isSelected && (
                <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
              )}

              {/* 文件信息悬浮层 */}
              <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="text-xs truncate">{file.file_name}</div>
                <div className="text-xs text-gray-300">
                  {file.file_type} • {file.width && file.height ? `${file.width}×${file.height}` : '未知尺寸'}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderFileList = () => {
    return (
      <div className="p-4 space-y-2">
        {filteredFiles.map((file) => {
          const isSelected = selectedFileIds.includes(file.id);
          const thumbnailUrl = file.thumbnail_small_path 
            ? getThumbnailUrl(activeCase!.id, file.id)
            : '/placeholder-image.png';

          return (
            <div
              key={file.id}
              className={`flex items-center gap-4 p-3 rounded-lg cursor-pointer transition-all ${
                isSelected
                  ? 'bg-blue-500 text-white'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
              onClick={() => handleFileClick(file.id)}
            >
              {/* 缩略图 */}
              <img
                src={thumbnailUrl}
                alt={file.file_name}
                className="w-16 h-16 object-cover rounded"
                loading="lazy"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder-image.png';
                }}
              />

              {/* 文件信息 */}
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">
                  {file.file_name}
                </div>
                <div className="text-sm opacity-70">
                  {file.file_type} • {file.width && file.height ? `${file.width}×${file.height}` : '未知尺寸'}
                </div>
                <div className="text-xs opacity-50">
                  创建于: {new Date(file.created_at).toLocaleString()}
                </div>
              </div>

              {/* 选中指示器 */}
              {isSelected && (
                <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                  <span className="text-blue-500 text-xs">✓</span>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // ========================================
  // 主渲染
  // ========================================

  return (
    <div className={`h-full flex flex-col bg-white dark:bg-gray-900 ${className}`}>
      {/* 顶部工具栏 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        {/* 第一行：档案库选择器和导入按钮 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1 mr-4">
            <CaseSelectorDropdown
              activeCase={activeCase}
              onCaseSelect={handleCaseSelect}
            />
          </div>
          <div className="flex items-center gap-2">
            <ImportImageButton
              onFilesSelected={handleFilesSelected}
              disabled={!activeCase || isImporting}
            />
            <button
              onClick={handleRefresh}
              disabled={!activeCase || isLoading}
              className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              title="刷新文件列表"
            >
              <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>

        {/* 第二行：搜索栏和视图控制 */}
        <div className="flex items-center justify-between">
          {/* 搜索栏 */}
          <div className="relative flex-1 mr-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索文件..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>

          {/* 视图模式切换 */}
          <div className="flex items-center gap-1 p-1 bg-gray-100 dark:bg-gray-800 rounded">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-1 rounded ${viewMode === 'grid' ? 'bg-white dark:bg-gray-700 shadow' : ''}`}
            >
              <Grid3X3 size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-1 rounded ${viewMode === 'list' ? 'bg-white dark:bg-gray-700 shadow' : ''}`}
            >
              <List size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto">
        {!activeCase ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <div className="text-lg mb-2">请选择一个档案库</div>
              <div className="text-sm">选择档案库后即可查看和导入图像文件</div>
            </div>
          </div>
        ) : isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
              <div className="text-gray-500">加载中...</div>
            </div>
          </div>
        ) : filteredFiles.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              {searchQuery ? '未找到匹配的文件' : '暂无文件，点击"导入图像"开始添加文件'}
            </div>
          </div>
        ) : viewMode === 'list' ? (
          renderFileList()
        ) : (
          renderFileGrid()
        )}
      </div>

      {/* 底部状态栏 */}
      <div className="p-2 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500">
        <div className="flex justify-between items-center">
          <span>
            {filteredFiles.length} 个文件
            {selectedFileIds.length > 0 && ` • ${selectedFileIds.length} 个已选中`}
            {isImporting && ' • 正在导入...'}
          </span>
          <span>
            {activeCase ? `档案库: ${activeCase.case_name}` : '未选择档案库'}
          </span>
        </div>
      </div>
    </div>
  );
}
