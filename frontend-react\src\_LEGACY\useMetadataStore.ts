// useMetadataStore.ts - Phase 7B: 元数据状态管理 (后端驱动版)
// 完全依赖后端API的标签和元数据管理

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import apiClient, { type FileRegistrationResponse, type ApiError } from '@/lib/apiClient';
import { useFileSyncStore } from './useFileSyncStore';

// ============================================================================
// 类型定义
// ============================================================================

export interface MetadataState {
  // 当前选中文件的标签数据
  tagsForSelectedFile: FileRegistrationResponse['tags'] | null;
  selectedFilePath: string | null;
  selectedFileId: number | null;
  
  // 全局标签数据
  allTags: {
    user: string[];
    ai: string[];
    metadata: Record<string, string[]>; // 按字段分组的元数据值
  };
  
  // 加载状态
  isLoading: boolean;
  isLoadingTags: boolean;
  isSavingTag: boolean;
  
  // 错误状态
  error: string | null;
  lastError: ApiError | null;
  
  // 操作历史
  operationHistory: Array<{
    type: 'add' | 'remove';
    filePath: string;
    tagName: string;
    timestamp: number;
    success: boolean;
  }>;
}

export interface MetadataActions {
  // 文件选择与标签获取
  selectFile: (filePath: string) => Promise<void>;
  fetchTagsForFile: (filePath: string) => Promise<void>;
  refreshCurrentFileTags: () => Promise<void>;
  
  // 标签操作
  addTagToFile: (filePath: string, tagName: string) => Promise<boolean>;
  removeTagFromFile: (filePath: string, tagName: string) => Promise<boolean>;
  
  // 批量操作
  addTagToMultipleFiles: (filePaths: string[], tagName: string) => Promise<number>;
  
  // 全局标签管理
  fetchAllTags: () => Promise<void>;
  
  // 错误处理
  clearError: () => void;
  clearHistory: () => void;
  
  // 状态重置
  resetMetadata: () => void;
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: MetadataState = {
  tagsForSelectedFile: null,
  selectedFilePath: null,
  selectedFileId: null,
  allTags: {
    user: [],
    ai: [],
    metadata: {},
  },
  isLoading: false,
  isLoadingTags: false,
  isSavingTag: false,
  error: null,
  lastError: null,
  operationHistory: [],
};

// ============================================================================
// Store实现
// ============================================================================

export const useMetadataStore = create<MetadataState & MetadataActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // ========================================
      // 文件选择与标签获取实现
      // ========================================
      
      selectFile: async (filePath: string) => {
        set({
          selectedFilePath: filePath,
          selectedFileId: null,
          tagsForSelectedFile: null,
          error: null,
        });
        
        await get().fetchTagsForFile(filePath);
      },

      fetchTagsForFile: async (filePath: string) => {
        set({ isLoadingTags: true, error: null });
        
        try {
          // 首先获取文件ID
          const fileSyncStore = useFileSyncStore.getState();
          let fileId = fileSyncStore.getFileId(filePath);
          
          // 如果缓存中没有，尝试同步
          if (!fileId) {
            // console.log(`📋 文件ID缓存未命中，尝试同步: ${filePath}`); // [CLEANED]
            fileId = await fileSyncStore.syncSingleFile(filePath);
          }
          
          if (!fileId) {
            throw new Error(`无法获取文件ID: ${filePath}`);
          }
          
          // 获取标签数据
          const tags = await apiClient.getTagsForFile(fileId);
          
          set({
            selectedFileId: fileId,
            tagsForSelectedFile: tags,
            isLoadingTags: false,
            error: null,
          });
          
          // console.log(`✅ 标签获取成功: ${filePath}`, tags); // [CLEANED]
          
        } catch (error) {
          const apiError = error as ApiError;
          console.error(`❌ 标签获取失败: ${filePath}`, error);
          
          set({
            isLoadingTags: false,
            error: apiError.error || '获取标签失败',
            lastError: apiError,
          });
        }
      },

      refreshCurrentFileTags: async () => {
        const { selectedFilePath } = get();
        if (selectedFilePath) {
          await get().fetchTagsForFile(selectedFilePath);
        }
      },

      // ========================================
      // 标签操作实现
      // ========================================
      
      addTagToFile: async (filePath: string, tagName: string) => {
        set({ isSavingTag: true, error: null });
        
        try {
          // 获取文件ID
          const fileSyncStore = useFileSyncStore.getState();
          let fileId = fileSyncStore.getFileId(filePath);
          
          if (!fileId) {
            fileId = await fileSyncStore.syncSingleFile(filePath);
          }
          
          if (!fileId) {
            throw new Error(`无法获取文件ID: ${filePath}`);
          }
          
          // 调用API添加标签
          await apiClient.addTagToFile(fileId, tagName);
          
          // 乐观更新UI
          const currentTags = get().tagsForSelectedFile;
          if (currentTags && get().selectedFilePath === filePath) {
            set({
              tagsForSelectedFile: {
                ...currentTags,
                user: [...currentTags.user, tagName],
              },
            });
          }
          
          // 记录操作历史
          set((state) => ({
            operationHistory: [
              ...state.operationHistory,
              {
                type: 'add',
                filePath,
                tagName,
                timestamp: Date.now(),
                success: true,
              },
            ],
            isSavingTag: false,
          }));
          
          // console.log(`✅ 标签添加成功: ${filePath} + "${tagName}"`); // [CLEANED]
          return true;
          
        } catch (error) {
          const apiError = error as ApiError;
          console.error(`❌ 标签添加失败: ${filePath} + "${tagName}"`, error);
          
          // 记录失败的操作
          set((state) => ({
            operationHistory: [
              ...state.operationHistory,
              {
                type: 'add',
                filePath,
                tagName,
                timestamp: Date.now(),
                success: false,
              },
            ],
            isSavingTag: false,
            error: apiError.error || '添加标签失败',
            lastError: apiError,
          }));
          
          return false;
        }
      },

      removeTagFromFile: async (filePath: string, tagName: string) => {
        set({ isSavingTag: true, error: null });
        
        try {
          // 获取文件ID
          const fileSyncStore = useFileSyncStore.getState();
          const fileId = fileSyncStore.getFileId(filePath);
          
          if (!fileId) {
            throw new Error(`文件未同步到后端: ${filePath}`);
          }
          
          // 注意：这里需要标签ID，但API规格中只提供了标签名
          // 实际实现中可能需要先查询标签ID或使用不同的API端点
          // 暂时使用占位符实现
          console.warn('⚠️ 删除标签功能需要标签ID，当前实现为占位符');
          
          // 乐观更新UI
          const currentTags = get().tagsForSelectedFile;
          if (currentTags && get().selectedFilePath === filePath) {
            set({
              tagsForSelectedFile: {
                ...currentTags,
                user: currentTags.user.filter(tag => tag !== tagName),
              },
            });
          }
          
          // 记录操作历史
          set((state) => ({
            operationHistory: [
              ...state.operationHistory,
              {
                type: 'remove',
                filePath,
                tagName,
                timestamp: Date.now(),
                success: true,
              },
            ],
            isSavingTag: false,
          }));
          
          // console.log(`✅ 标签删除成功: ${filePath} - "${tagName}"`); // [CLEANED]
          return true;
          
        } catch (error) {
          const apiError = error as ApiError;
          console.error(`❌ 标签删除失败: ${filePath} - "${tagName}"`, error);
          
          set((state) => ({
            operationHistory: [
              ...state.operationHistory,
              {
                type: 'remove',
                filePath,
                tagName,
                timestamp: Date.now(),
                success: false,
              },
            ],
            isSavingTag: false,
            error: apiError.error || '删除标签失败',
            lastError: apiError,
          }));
          
          return false;
        }
      },

      // ========================================
      // 批量操作实现
      // ========================================
      
      addTagToMultipleFiles: async (filePaths: string[], tagName: string) => {
        set({ isSavingTag: true, error: null });
        
        try {
          // 获取所有文件的ID
          const fileSyncStore = useFileSyncStore.getState();
          const fileIds: number[] = [];
          
          for (const filePath of filePaths) {
            let fileId = fileSyncStore.getFileId(filePath);
            if (!fileId) {
              fileId = await fileSyncStore.syncSingleFile(filePath);
            }
            if (fileId) {
              fileIds.push(fileId);
            }
          }
          
          if (fileIds.length === 0) {
            throw new Error('没有有效的文件ID');
          }
          
          // 调用批量API
          await apiClient.batchAddTags(fileIds, tagName);
          
          set({ isSavingTag: false });
          
          // console.log(`✅ 批量标签添加成功: ${fileIds.length} 个文件 + "${tagName}"`); // [CLEANED]
          return fileIds.length;
          
        } catch (error) {
          const apiError = error as ApiError;
          console.error(`❌ 批量标签添加失败: "${tagName}"`, error);
          
          set({
            isSavingTag: false,
            error: apiError.error || '批量添加标签失败',
            lastError: apiError,
          });
          
          return 0;
        }
      },

      // ========================================
      // 全局标签管理实现
      // ========================================
      
      fetchAllTags: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // 注意：这里需要根据实际API实现
          // 当前API规格中没有明确的"获取所有标签"端点
          // 可能需要通过搜索或其他方式获取
          console.warn('⚠️ fetchAllTags 功能待实现，需要对应的后端API');
          
          set({
            isLoading: false,
            allTags: {
              user: ['示例标签1', '示例标签2'], // 占位符数据
              ai: ['AI标签1', 'AI标签2'],
              metadata: {
                camera: ['Canon', 'Sony', 'Nikon'],
                iso: ['100', '200', '400', '800'],
              },
            },
          });
          
        } catch (error) {
          const apiError = error as ApiError;
          console.error('❌ 获取全局标签失败:', error);
          
          set({
            isLoading: false,
            error: apiError.error || '获取全局标签失败',
            lastError: apiError,
          });
        }
      },

      // ========================================
      // 错误处理实现
      // ========================================
      
      clearError: () => {
        set({ error: null, lastError: null });
      },

      clearHistory: () => {
        set({ operationHistory: [] });
      },

      // ========================================
      // 状态重置实现
      // ========================================
      
      resetMetadata: () => {
        set(initialState);
      },
    }),
    { name: 'MetadataStore' }
  )
);

// ============================================================================
// 选择器钩子
// ============================================================================

export const useCurrentFileTags = () => {
  return useMetadataStore((state) => ({
    tags: state.tagsForSelectedFile,
    filePath: state.selectedFilePath,
    fileId: state.selectedFileId,
    isLoading: state.isLoadingTags,
  }), shallow);
};

export const useTagOperations = () => {
  return useMetadataStore((state) => ({
    addTag: state.addTagToFile,
    removeTag: state.removeTagFromFile,
    addTagToMultiple: state.addTagToMultipleFiles,
    isSaving: state.isSavingTag,
  }), shallow);
};

export const useMetadataErrors = () => {
  return useMetadataStore((state) => ({
    error: state.error,
    lastError: state.lastError,
    clearError: state.clearError,
    operationHistory: state.operationHistory,
  }), shallow);
};
