// NaturalLanguageSearch.tsx - Phase 8A: 自然语言搜索组件
// 将用户的自然语言查询转换为结构化搜索

import React, { useState, useRef } from 'react';
import { useCurrentCase } from '@/store';
import { getAiClient, isAiServiceAvailable, isThrottled } from '@/lib/aiClientInstance';
import { useRateLimitStatus, useServiceHealth, useAiErrors } from '@/store';
import type { QueryParseResponse, StructuredQuery } from '@/lib/aiClient';
import { StatusDisplay } from '@/components/feedback/StatusDisplay';
import './NaturalLanguageSearch.css';

// ============================================================================
// 类型定义
// ============================================================================

interface NaturalLanguageSearchProps {
  onSearchExecute: (query: StructuredQuery) => void;
  onSearchResults?: (results: any) => void;
  className?: string;
  placeholder?: string;
}

interface QuerySuggestionProps {
  suggestion: string;
  onSelect: (suggestion: string) => void;
}

// ============================================================================
// 查询建议组件
// ============================================================================

const QuerySuggestion: React.FC<QuerySuggestionProps> = ({
  suggestion,
  onSelect
}) => {
  return (
    <button
      className="nl-search__suggestion"
      onClick={() => onSelect(suggestion)}
    >
      <span className="nl-search__suggestion-icon">💡</span>
      <span className="nl-search__suggestion-text">{suggestion}</span>
    </button>
  );
};

// ============================================================================
// 主组件
// ============================================================================

export const NaturalLanguageSearch: React.FC<NaturalLanguageSearchProps> = ({
  onSearchExecute,
  onSearchResults,
  className = '',
  placeholder = '用自然语言描述你要找的内容...'
}) => {
  // Store状态
  const { currentCaseId } = useCurrentCase();
  const { isQueryThrottled } = useRateLimitStatus();
  const { health: serviceHealth } = useServiceHealth();
  const { lastError: aiError } = useAiErrors();

  // 本地状态
  const [query, setQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [parseResult, setParseResult] = useState<QueryParseResponse | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Refs
  const inputRef = useRef<HTMLInputElement>(null);

  // ========================================
  // 查询处理逻辑
  // ========================================

  const processQuery = async (queryText: string) => {
    if (!currentCaseId || !queryText.trim() || isThrottled('query') || !isAiServiceAvailable()) {
      return;
    }

    setIsProcessing(true);
    setError(null);
    setParseResult(null);
    setSuggestions([]);

    try {
      // console.log(`🔍 Phase 8A: 处理自然语言查询 - "${queryText}"`); // [CLEANED]

      // 模拟AI查询解析（实际实现时会调用真实API）
      const mockParseResult: QueryParseResponse = {
        success: true,
        case_id: currentCaseId,
        original_query: queryText,
        parsed_intent: {
          action: 'search',
          confidence: 0.92,
          entities: [
            {
              type: 'content_type',
              value: '照片',
              confidence: 0.88,
              normalized: 'image'
            },
            {
              type: 'quality_requirement',
              value: '高质量',
              confidence: 0.90,
              normalized: {
                quality_min: 80,
                quality_max: 100
              }
            }
          ]
        },
        structured_query: {
          search_type: 'advanced',
          filters: {
            metadata_filters: {
              content_type: 'image'
            },
            quality_range: {
              min: 80,
              max: 100
            }
          },
          sort_by: [
            {
              field: 'quality_score',
              order: 'desc'
            }
          ],
          limit: 50,
          offset: 0
        },
        execution_ready: true,
        estimated_results: 23,
        query_suggestions: [
          '显示所有高质量照片',
          '查找最近的图片文件',
          '搜索评分最高的照片'
        ],
        processing_details: {
          nlp_model: 'mizzy-nlp-v2.1',
          entity_extraction_ms: 45,
          intent_classification_ms: 32,
          query_construction_ms: 18,
          total_processing_ms: 125
        }
      };

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 600));

      setParseResult(mockParseResult);

      if (mockParseResult.execution_ready) {
        // console.log(`✅ Phase 8A: 查询解析成功 - 预估结果: ${mockParseResult.estimated_results}`); // [CLEANED]

        // 自动执行搜索
        onSearchExecute(mockParseResult.structured_query);
      } else {
        // console.log(`💡 Phase 8A: 查询需要澄清 - 提供 ${mockParseResult.query_suggestions.length} 个建议`); // [CLEANED]
        setSuggestions(mockParseResult.query_suggestions);
        setShowSuggestions(true);
      }

    } catch (error: any) {
      console.error('❌ Phase 8A: 自然语言查询处理失败:', error);
      setError(error.message || '查询处理失败');
    } finally {
      setIsProcessing(false);
    }
  };

  // ========================================
  // 事件处理
  // ========================================

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      processQuery(query.trim());
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setSuggestions([]);
      setError(null);
    }
  };

  const handleSuggestionSelect = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    setSuggestions([]);
    processQuery(suggestion);
  };

  const handleClear = () => {
    setQuery('');
    setParseResult(null);
    setSuggestions([]);
    setError(null);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  // ========================================
  // 渲染辅助函数
  // ========================================

  const renderServiceStatus = () => {
    if (serviceHealth === 'unavailable') {
      return (
        <div className="nl-search__status nl-search__status--error">
          <span className="nl-search__status-icon">❌</span>
          <span>AI搜索服务不可用</span>
        </div>
      );
    }

    if (serviceHealth === 'degraded') {
      return (
        <div className="nl-search__status nl-search__status--warning">
          <span className="nl-search__status-icon">⚠️</span>
          <span>AI搜索服务性能降级</span>
        </div>
      );
    }

    if (isQueryThrottled) {
      return (
        <div className="nl-search__status nl-search__status--throttled">
          <span className="nl-search__status-icon">⏱️</span>
          <span>查询频率限制中，请稍后再试</span>
        </div>
      );
    }

    return null;
  };

  const renderParseResult = () => {
    if (!parseResult) return null;

    return (
      <div className="nl-search__result">
        <div className="nl-search__result-header">
          <span className="nl-search__result-icon">🧠</span>
          <span className="nl-search__result-title">AI理解:</span>
          <span className="nl-search__result-confidence">
            置信度 {(parseResult.parsed_intent.confidence * 100).toFixed(0)}%
          </span>
        </div>
        <div className="nl-search__result-interpretation">
          {parseResult.parsed_intent.entities.map((entity, index) => (
            <span key={index} className="nl-search__entity">
              {entity.value}
            </span>
          ))}
        </div>
        {parseResult.estimated_results > 0 && (
          <div className="nl-search__result-stats">
            预估找到 {parseResult.estimated_results} 个结果
          </div>
        )}
      </div>
    );
  };

  // ========================================
  // 渲染
  // ========================================

  const isDisabled = isProcessing || isQueryThrottled || serviceHealth === 'unavailable';

  return (
    <div className={`nl-search ${className}`}>
      {/* 服务状态 */}
      {renderServiceStatus()}

      {/* 搜索表单 */}
      <form className="nl-search__form" onSubmit={handleSubmit}>
        <div className="nl-search__input-group">
          <input
            ref={inputRef}
            type="text"
            className="nl-search__input"
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isDisabled}
          />

          {query && (
            <button
              type="button"
              className="nl-search__clear"
              onClick={handleClear}
              disabled={isProcessing}
            >
              ×
            </button>
          )}

          <button
            type="submit"
            className="nl-search__submit"
            disabled={isDisabled || !query.trim()}
          >
            {isProcessing ? (
              <>
                <div className="nl-search__spinner"></div>
                <span>分析中...</span>
              </>
            ) : (
              <>
                <span className="nl-search__submit-icon">🔍</span>
                <span>搜索</span>
              </>
            )}
          </button>
        </div>
      </form>

      {/* 解析结果 */}
      {renderParseResult()}

      {/* 错误显示 */}
      {error && (
        <div className="nl-search__error">
          <span className="nl-search__error-icon">⚠️</span>
          <span className="nl-search__error-text">{error}</span>
          <button
            className="nl-search__error-retry"
            onClick={() => query.trim() && processQuery(query.trim())}
            disabled={isProcessing}
          >
            重试
          </button>
        </div>
      )}

      {/* 查询建议 */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="nl-search__suggestions">
          <div className="nl-search__suggestions-header">
            <span className="nl-search__suggestions-icon">💡</span>
            <span>试试这些查询:</span>
          </div>
          <div className="nl-search__suggestions-list">
            {suggestions.map((suggestion, index) => (
              <QuerySuggestion
                key={index}
                suggestion={suggestion}
                onSelect={handleSuggestionSelect}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default NaturalLanguageSearch;
