{"name": "frontend-react", "private": true, "version": "0.0.0", "type": "module", "main": "electron/main.cjs", "scripts": {"dev:web": "npx vite --port 5180", "dev": "npx concurrently \"npm run dev:web\" \"npx wait-on http://localhost:5180 && npx cross-env NODE_ENV=development npx electron .\"", "build": "npx tsc -b && npx vite build", "build:electron": "npm run build && electron-builder", "lint": "npx eslint .", "preview": "npx vite preview", "electron": "npx cross-env NODE_ENV=development npx electron .", "electron:prod": "npx cross-env NODE_ENV=production npx electron .", "test": "npx vitest", "test:run": "npx vitest run", "test:ui": "npx vitest --ui", "build:check": "npx tsc --noEmit --skipLib<PERSON><PERSON>ck", "build:vite": "npx vite build --mode development", "dist": "npm run build:vite && npx electron-builder", "dist:win": "npm run build:vite && npx electron-builder --win", "dist:mac": "npm run build:vite && npx electron-builder --mac", "dist:linux": "npm run build:vite && npx electron-builder --linux"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.84.0", "@tanstack/react-virtual": "^3.13.12", "@types/node": "^24.1.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "glob": "^11.0.3", "lucide-react": "^0.487.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-resizable-panels": "^3.0.4", "react-router-dom": "^7.7.1", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "trash": "^9.0.0", "uuid": "^11.1.0", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "electron": "^37.2.4", "electron-builder": "^26.0.12", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "msw": "^2.10.4", "prettier": "^3.6.2", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4", "wait-on": "^8.0.1"}, "build": {"appId": "com.mizzy.star.imagedb", "productName": "Mizzy Star Image Database", "directories": {"output": "dist"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "public", "to": "public", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "public/icon.icns", "category": "public.app-category.graphics-design"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "public/icon.png", "category": "Graphics"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}