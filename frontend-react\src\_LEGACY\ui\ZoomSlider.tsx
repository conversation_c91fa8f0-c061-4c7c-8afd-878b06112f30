// src/components/ui/ZoomSlider.tsx
import React from 'react';

interface ZoomSliderProps {
  value?: number;
  min?: number;
  max?: number;
  step?: number;
  onChange?: (value: number) => void;
  className?: string;
}

export function ZoomSlider({
  value = 5,
  min = 1,
  max = 10,
  step = 1,
  onChange,
  className
}: ZoomSliderProps) {
  return (
    <div className={`relative ${className}`}>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange?.(Number(e.target.value))}
        className="w-[80px] h-[16px] appearance-none cursor-pointer"
        style={{
          background: '#2A2B2E',
          border: '1px solid #353639'
        }}
      />
    </div>
  );
}
