// TestImportPage.tsx - 测试导入功能的页面
// 用于验证完整的导入流程

import React from 'react';
import { GalleryPanel } from './GalleryPanel';

export function TestImportPage() {
  return (
    <div className="h-screen w-screen bg-gray-100 dark:bg-gray-900">
      <div className="h-full p-4">
        <div className="h-full bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div className="h-full flex flex-col">
            {/* 页面标题 */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                迷星图像导入测试页面
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                测试从界面到后端API的完整导入流程
              </p>
            </div>

            {/* 画廊面板 */}
            <div className="flex-1 overflow-hidden">
              <GalleryPanel />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
