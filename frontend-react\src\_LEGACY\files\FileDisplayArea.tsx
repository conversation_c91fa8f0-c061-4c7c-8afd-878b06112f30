// FileDisplayArea.tsx - 纯净的文件显示区域
// 职责单一：只负责渲染文件列表，不包含工具栏

import React from 'react';
import { useFiles } from '@/hooks/useFiles';
import { useCaseStore } from '@/store/useCaseStore';
import { FileCard } from '@/components/ui/FileCard';
import { StatusDisplay } from '@/components/feedback/StatusDisplay';
import apiClient from '@/lib/apiClient';

interface FileDisplayAreaProps {
  className?: string;
}

export function FileDisplayArea({ className }: FileDisplayAreaProps) {
  console.log(`%c🎨 Rendering: FileDisplayArea (纯净文件显示区域)`, 'color: #4CAF50; font-weight: bold;');

  // 1. 使用纯净的hook来获取数据
  const currentCaseId = useCaseStore(state => state.currentCaseId);
  const { data: filesResponse, isLoading, error, refetch } = useFiles(currentCaseId);

  // 提取文件数组，确保类型安全
  const files = filesResponse?.files || [];

  // 2. 文件删除处理函数
  const handleFileDelete = async (file: any) => {
    if (!currentCaseId) return;

    const confirmed = window.confirm(`确定要删除文件 "${file.fileName}" 吗？\n\n此操作将把文件移动到回收站。`);

    if (confirmed) {
      try {
        await apiClient.deleteFile(currentCaseId, file.id);
        console.log(`✅ 文件删除成功: ${file.fileName}`);

        // 刷新文件列表
        refetch();

        alert(`文件 "${file.fileName}" 已删除并移动到回收站`);
      } catch (error) {
        console.error('❌ 删除文件失败:', error);
        alert(`删除文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
  };

  // 3. 处理加载和错误状态
  if (isLoading) {
    return (
      <StatusDisplay 
        status="loading" 
        message="加载文件中..." 
        className={className}
      />
    );
  }

  if (error) {
    return (
      <StatusDisplay 
        status="error" 
        message={`加载失败: ${error.message}`}
        className={className}
      />
    );
  }

  if (!files || files.length === 0) {
    return (
      <StatusDisplay 
        status="empty" 
        message={
          <div className="space-y-4">
            <div className="text-6xl">📂</div>
            <h3 className="text-lg font-medium">此档案库中没有文件</h3>
            <p className="text-muted-foreground">
              点击上方的上传按钮添加图像文件
            </p>
          </div>
        }
        className={className}
      />
    );
  }

  // 3. 数据转换函数 - 确保与新API格式兼容
  const transformFileData = (backendFile: any) => {
    return {
      id: backendFile.id,
      fileName: backendFile.file_name || backendFile.fileName || 'Unknown File',
      filePath: backendFile.file_path || backendFile.filePath || '',
      fileType: backendFile.file_type || backendFile.fileType || 'application/octet-stream',
      fileSize: backendFile.file_size || backendFile.fileSize || 0,
      width: backendFile.width,
      height: backendFile.height,
      thumbnailPath: backendFile.thumbnail_small_path || backendFile.thumbnailPath,
      createdAt: backendFile.created_at || backendFile.createdAt,
      takenAt: backendFile.taken_at || backendFile.takenAt,
    };
  };

  // 4. 使用一个纯净的循环和正确的卡片组件来渲染文件
  return (
    <div className={`p-4 overflow-y-auto ${className}`}>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
        {files.map((file, index) => {
          const transformedFile = transformFileData(file);
          return (
            <div
              key={file.id}
              className="animate-slide-up"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <FileCard
                file={transformedFile}
                selected={false} // 暂时禁用选择功能，专注于显示
                onSelect={() => {}} // 暂时空实现
                onDoubleClick={() => {}} // 暂时空实现
                onDelete={handleFileDelete} // ✅ 启用删除功能
                showActions={true} // ✅ 显示操作按钮
                showFileName={true}
                showFileInfo={true}
                layout="grid"
                style={{
                  width: '200px',
                  height: '200px',
                }}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
