// ImportImageButton.tsx - 图像导入按钮组件
// 通过Electron IPC调用文件选择对话框

import React, { useState } from 'react';
import { Plus } from 'lucide-react';

interface ImportImageButtonProps {
  onFilesSelected: (filePaths: string[]) => void;
  disabled?: boolean;
  className?: string;
}

export function ImportImageButton({ 
  onFilesSelected, 
  disabled = false, 
  className = '' 
}: ImportImageButtonProps) {
  const [isSelecting, setIsSelecting] = useState(false);

  const handleImport = async () => {
    if (disabled || isSelecting) return;

    try {
      setIsSelecting(true);
      // console.log('📁 用户点击导入图像按钮'); // [CLEANED]

      // 检查Electron API是否可用
      if (!window.electronAPI?.openFileDialog) {
        console.error('❌ Electron API不可用');
        alert('错误：无法访问文件系统。请确保应用在Electron环境中运行。');
        return;
      }

      // 调用Electron IPC打开文件选择对话框
      const filePaths = await window.electronAPI.openFileDialog();
      
      if (filePaths && filePaths.length > 0) {
        // console.log(`📁 用户选择了 ${filePaths.length} 个文件:`, filePaths); // [CLEANED]
        onFilesSelected(filePaths);
      } else {
        // console.log('📁 用户取消了文件选择'); // [CLEANED]
      }
    } catch (error) {
      console.error('❌ 文件选择失败:', error);
      alert('文件选择失败，请重试。');
    } finally {
      setIsSelecting(false);
    }
  };

  return (
    <button
      onClick={handleImport}
      disabled={disabled || isSelecting}
      className={`
        inline-flex items-center gap-2 px-4 py-2 
        bg-blue-600 hover:bg-blue-700 
        disabled:bg-gray-400 disabled:cursor-not-allowed
        text-white font-medium rounded-lg
        transition-colors duration-200
        ${className}
      `}
      title="选择本地图像文件导入到当前档案库"
    >
      <Plus size={16} />
      {isSelecting ? '选择文件中...' : '导入图像'}
    </button>
  );
}
