interface SettingsIconProps {
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export function SettingsIcon({ className = '', onClick, style }: SettingsIconProps) {
  return (
    <svg
      className={`icon ${className}`}
      onClick={onClick}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ cursor: 'pointer', ...style }}
    >
      <path
        d="M8 10a2 2 0 100-4 2 2 0 000 4z"
        stroke="var(--color-text-title)"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.93 6.5a1 1 0 00.07-1l-.85-.85a1 1 0 00-1-.07l-.5.29a6 6 0 00-1.15-.66V3.5a1 1 0 00-1-1H7.5a1 1 0 00-1 1v.71a6 6 0 00-1.15.66l-.5-.29a1 1 0 00-1 .07L3 5.5a1 1 0 00-.07 1l.29.5a6 6 0 00-.66 1.15H1.85a1 1 0 00-1 1v1a1 1 0 001 1h.71a6 6 0 00.66 1.15l-.29.5a1 1 0 00.07 1l.85.85a1 1 0 001 .07l.5-.29a6 6 0 001.15.66v.71a1 1 0 001 1h1a1 1 0 001-1v-.71a6 6 0 001.15-.66l.5.29a1 1 0 001-.07l.85-.85a1 1 0 00.07-1l-.29-.5a6 6 0 00.66-1.15h.71a1 1 0 001-1v-1a1 1 0 00-1-1h-.71a6 6 0 00-.66-1.15l.29-.5z"
        stroke="var(--color-text-title)"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
