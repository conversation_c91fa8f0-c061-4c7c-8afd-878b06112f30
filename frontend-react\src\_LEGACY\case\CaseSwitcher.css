/* CaseSwitcher.css - Phase 7C: 案例切换器样式 */
/* 遵循项目既定的颜色方案和设计模式 */

/* ============================================================================
   主容器样式
   ============================================================================ */

.case-switcher {
  position: relative;
  display: inline-block;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ============================================================================
   触发按钮样式
   ============================================================================ */

.case-switcher__trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #040709;
  border: 1px solid #333;
  border-radius: 6px;
  color: #A49F9A;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  max-width: 300px;
}

.case-switcher__trigger:hover:not(:disabled) {
  background-color: #191012;
  border-color: #444;
}

.case-switcher__trigger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.case-switcher__trigger--open {
  background-color: #191012;
  border-color: #555;
}

.case-switcher__current {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.case-switcher__icon {
  font-size: 16px;
  flex-shrink: 0;
}

.case-switcher__info {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.case-switcher__name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.case-switcher__meta {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.case-switcher__placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.case-switcher__arrow {
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.case-switcher__trigger--open .case-switcher__arrow {
  transform: rotate(180deg);
}

/* ============================================================================
   加载状态样式
   ============================================================================ */

.case-switcher__loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.case-switcher__spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #333;
  border-top: 2px solid #A49F9A;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============================================================================
   下拉菜单样式
   ============================================================================ */

.case-switcher__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #040709;
  border: 1px solid #333;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  margin-top: 4px;
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ============================================================================
   搜索框样式
   ============================================================================ */

.case-switcher__search {
  padding: 12px;
  border-bottom: 1px solid #333;
}

.case-switcher__search-input {
  width: 100%;
  padding: 8px 12px;
  background-color: #191012;
  border: 1px solid #444;
  border-radius: 4px;
  color: #A49F9A;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.case-switcher__search-input:focus {
  border-color: #666;
  box-shadow: 0 0 0 2px rgba(164, 159, 154, 0.1);
}

.case-switcher__search-input::placeholder {
  color: #666;
}

/* ============================================================================
   错误显示样式
   ============================================================================ */

.case-switcher__error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: rgba(239, 68, 68, 0.1);
  border-bottom: 1px solid #333;
  color: #ef4444;
  font-size: 12px;
}

.case-switcher__error-icon {
  flex-shrink: 0;
}

.case-switcher__error-text {
  flex: 1;
}

.case-switcher__error-close {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.case-switcher__error-close:hover {
  opacity: 1;
}

/* ============================================================================
   案例列表样式
   ============================================================================ */

.case-switcher__list {
  flex: 1;
  overflow-y: auto;
  max-height: 250px;
}

.case-switcher__loading-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.case-switcher__item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  background: none;
  border: none;
  color: #A49F9A;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
  text-align: left;
  border-bottom: 1px solid rgba(51, 51, 51, 0.5);
}

.case-switcher__item:hover:not(:disabled) {
  background-color: #191012;
}

.case-switcher__item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.case-switcher__item--active {
  background-color: rgba(164, 159, 154, 0.1);
  border-left: 3px solid #A49F9A;
}

.case-switcher__item-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.case-switcher__item-content {
  flex: 1;
  min-width: 0;
}

.case-switcher__item-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.case-switcher__item-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.case-switcher__item-date {
  opacity: 0.8;
}

.case-switcher__item-desc {
  font-size: 11px;
  color: #555;
  line-height: 1.3;
  margin-top: 2px;
}

.case-switcher__item-check {
  color: #4ade80;
  font-size: 14px;
  flex-shrink: 0;
  margin-top: 2px;
}

/* ============================================================================
   空状态样式
   ============================================================================ */

.case-switcher__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  color: #666;
  text-align: center;
}

.case-switcher__empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.case-switcher__empty-text {
  font-size: 14px;
}

/* ============================================================================
   创建案例样式
   ============================================================================ */

.case-switcher__create {
  border-top: 1px solid #333;
  padding: 8px;
}

.case-switcher__create-button {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: 1px dashed #444;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.case-switcher__create-button:hover {
  background-color: #191012;
  border-color: #555;
  color: #A49F9A;
}

.case-switcher__create-icon {
  font-size: 12px;
}

.case-switcher__create-form {
  display: flex;
  gap: 4px;
}

.case-switcher__create-input {
  flex: 1;
  padding: 6px 8px;
  background-color: #191012;
  border: 1px solid #444;
  border-radius: 4px;
  color: #A49F9A;
  font-size: 12px;
  outline: none;
}

.case-switcher__create-input:focus {
  border-color: #666;
}

.case-switcher__create-actions {
  display: flex;
  gap: 2px;
}

.case-switcher__create-confirm,
.case-switcher__create-cancel {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.case-switcher__create-confirm {
  background-color: #4ade80;
  color: white;
}

.case-switcher__create-confirm:hover:not(:disabled) {
  background-color: #22c55e;
}

.case-switcher__create-confirm:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.case-switcher__create-cancel {
  background-color: #ef4444;
  color: white;
}

.case-switcher__create-cancel:hover {
  background-color: #dc2626;
}

/* ============================================================================
   滚动条样式
   ============================================================================ */

.case-switcher__list::-webkit-scrollbar {
  width: 6px;
}

.case-switcher__list::-webkit-scrollbar-track {
  background: #191012;
}

.case-switcher__list::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 3px;
}

.case-switcher__list::-webkit-scrollbar-thumb:hover {
  background: #444;
}
