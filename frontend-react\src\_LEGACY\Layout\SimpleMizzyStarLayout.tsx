// SimpleMizzyStarLayout.tsx - 简化版布局，不使用 react-resizable-panels
import React, { useState, useEffect } from 'react';
import { GalleryPanel } from '../components/mizzy/GalleryPanel';
import { MetadataPanel } from '../components/mizzy/MetadataPanel';
import type { ICase, IFile } from '../services/apiTypes';
import { getCaseFiles } from '../services/apiService';

export function SimpleMizzyStarLayout() {
  // console.log('🏗️ SimpleMizzyStarLayout 渲染中...'); // [CLEANED]
  
  const [activeCase, setActiveCase] = useState<ICase | null>(null);
  const [files, setFiles] = useState<IFile[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);

  useEffect(() => {
    if (!activeCase) {
      setFiles([]);
      return;
    }

    const fetchFiles = async () => {
      setIsLoadingFiles(true);
      try {
        const fetchedFiles = await getCaseFiles(activeCase.id);
        setFiles(fetchedFiles);
      } catch (error) {
        console.error("Failed to fetch files for case:", activeCase.id, error);
        setFiles([]);
      } finally {
        setIsLoadingFiles(false);
      }
    };

    fetchFiles();
  }, [activeCase]);

  const handleFilesImported = (newlyImportedFiles: IFile[]) => {
    setFiles(currentFiles => [...newlyImportedFiles, ...currentFiles]);
  };
  
  return (
    <div style={{ 
      width: '100%', 
      height: '100%', 
      display: 'flex',
      backgroundColor: '#1A1B1E'
    }}>
      {/* Gallery Panel - 70% width */}
      <div style={{ width: '70%', height: '100%' }}>
        <GalleryPanel
          activeCase={activeCase}
          onCaseSelect={setActiveCase}
          files={files}
          isLoading={isLoadingFiles}
          onFilesImported={handleFilesImported}
        />
      </div>
      
      {/* Divider */}
      <div style={{ 
        width: '2px', 
        backgroundColor: '#374151',
        cursor: 'col-resize'
      }} />
      
      {/* Metadata Panel - 30% width */}
      <div style={{ width: '30%', height: '100%' }}>
        <MetadataPanel />
      </div>
    </div>
  );
}