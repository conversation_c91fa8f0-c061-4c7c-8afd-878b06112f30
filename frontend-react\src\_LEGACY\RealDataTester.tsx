// RealDataTester - 测试真实文件系统数据的组件 (Phase 2升级版)

import React, { useState } from 'react';
import { useFileSystemState } from '@/store';
import { useDirectoryContents } from '@/hooks/useFileSystem';

interface RealDataTesterProps {
  className?: string;
}

export const RealDataTester: React.FC<RealDataTesterProps> = ({ className = '' }) => {
  // Phase 2: 使用Zustand状态管理
  const { currentDirectory, setCurrentDirectory } = useFileSystemState();

  // 本地状态：输入框的路径
  const [inputPath, setInputPath] = useState(currentDirectory || '');

  // 使用文件系统Hook获取当前目录数据
  const { files, isLoading, error } = useDirectoryContents(currentDirectory);

  // Phase 2: 处理路径输入变化
  const handlePathChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputPath(event.target.value);
  };

  // Phase 2: 加载目录按钮处理
  const handleLoadDirectory = () => {
    if (inputPath.trim()) {
      // console.log('🔍 RealDataTester: 设置当前目录为:', inputPath); // [CLEANED]
      setCurrentDirectory(inputPath.trim());
    }
  };

  // Phase 2: 清除目录按钮处理
  const handleClearDirectory = () => {
    // console.log('🧹 RealDataTester: 清除当前目录'); // [CLEANED]
    setCurrentDirectory(null);
    setInputPath('');
  };

  return (
    <div className={`p-6 bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          🧪 真实文件系统数据测试 (Phase 2升级版)
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          测试 Electron IPC 通信和 Zustand 状态管理集成。选择的目录将在Gallery中显示。
        </p>

        {/* 当前目录显示 */}
        {currentDirectory && (
          <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <div className="text-sm text-blue-800">
              <strong>当前目录:</strong> {currentDirectory}
            </div>
          </div>
        )}

        {/* 路径输入 */}
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={inputPath}
            onChange={handlePathChange}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="输入要浏览的目录路径"
          />
          <button
            onClick={handleLoadDirectory}
            disabled={isLoading || !inputPath.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            加载目录
          </button>
          {currentDirectory && (
            <button
              onClick={handleClearDirectory}
              className="px-4 py-2 bg-gray-500 text-white rounded-md text-sm hover:bg-gray-600"
            >
              清除
            </button>
          )}
        </div>
      </div>

      {/* 加载状态 */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">正在加载目录内容...</span>
        </div>
      )}

      {/* 错误状态 */}
      {error && !isLoading && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="text-red-400">❌</div>
            <div className="ml-2">
              <h4 className="text-sm font-medium text-red-800">错误</h4>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 文件列表 */}
      {!isLoading && !error && files.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            目录内容 ({files.length} 个项目)
          </h4>
          <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-md">
            {files.map((item, index) => (
              <div
                key={index}
                className="flex items-center px-3 py-2 border-b border-gray-100 last:border-b-0 hover:bg-gray-50"
              >
                <div className="text-lg mr-2">
                  {item.isDirectory ? '📁' : '📄'}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {item.name}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {item.path}
                  </div>
                </div>
                <div className="text-xs text-gray-400">
                  {item.isDirectory ? '目录' : '文件'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 空状态 */}
      {!isLoading && !error && currentDirectory && files.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">📂</div>
          <p>目录为空或无法访问</p>
        </div>
      )}

      {/* 未选择目录状态 */}
      {!currentDirectory && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">🗂️</div>
          <p>请输入目录路径并点击"加载目录"按钮</p>
        </div>
      )}

      {/* API 状态指示器 */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${
              window.electronAPI ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            Electron API: {window.electronAPI ? '可用' : '不可用'}
          </div>
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${
              currentDirectory ? 'bg-blue-500' : 'bg-gray-400'
            }`}></div>
            状态管理: {currentDirectory ? '已连接Gallery' : '未连接'}
          </div>
        </div>
      </div>
    </div>
  );
};
