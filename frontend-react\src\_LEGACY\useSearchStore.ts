// useSearchStore.ts - Phase 7A: 高级搜索状态管理
// 管理多维度搜索条件和搜索状态

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { v4 as uuidv4 } from 'uuid';

// ============================================================================
// 类型定义
// ============================================================================

export interface SearchCriterion {
  id: string;
  field: 'fileName' | 'fileSize' | 'fileType' | 'modifiedTime' | 'tags'; // Phase 7B: 添加标签字段
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | '>' | '<' | '>=' | '<=' | 'containsAny' | 'containsAll'; // Phase 7B: 添加标签操作符
  value: string | number | Date;
  caseSensitive?: boolean; // 仅用于文本字段
}

export interface SearchState {
  // 搜索条件
  criteria: SearchCriterion[];

  // 搜索状态
  isActive: boolean;
  resultCount: number;

  // UI状态
  isSearchPanelExpanded: boolean;
  lastSearchTime: number;

  // Phase 7B: 后端搜索状态
  useBackendSearch: boolean;
  currentCaseId: number | null;
  backendSearchEnabled: boolean;

  // 性能统计
  searchPerformance: {
    lastSearchDuration: number;
    averageSearchDuration: number;
    searchCount: number;
    backendSearchCount: number; // Phase 7B: 后端搜索计数
    localSearchCount: number;   // Phase 7B: 本地搜索计数
  };
}

export interface SearchActions {
  // 搜索条件管理
  addCriterion: (criterion: Omit<SearchCriterion, 'id'>) => void;
  removeCriterion: (id: string) => void;
  updateCriterion: (id: string, updates: Partial<SearchCriterion>) => void;
  clearSearch: () => void;

  // 搜索状态控制
  toggleSearch: () => void;
  setSearchActive: (active: boolean) => void;
  setResultCount: (count: number) => void;

  // UI状态控制
  toggleSearchPanel: () => void;
  setSearchPanelExpanded: (expanded: boolean) => void;

  // 性能统计
  recordSearchPerformance: (duration: number) => void;

  // 批量操作
  setCriteria: (criteria: SearchCriterion[]) => void;
  duplicateCriterion: (id: string) => void;

  // 预设搜索
  applyPresetSearch: (preset: 'images' | 'large-files' | 'recent' | 'old') => void;

  // Phase 7B: 后端搜索控制
  setBackendSearch: (enabled: boolean) => void;
  setCurrentCase: (caseId: number | null) => void;
  toggleSearchMode: () => void;

  // Phase 7C: 案例上下文搜索
  getSearchContext: () => { caseId: number | null; useBackendSearch: boolean };
  onCaseChange: (newCaseId: number) => void;

  // 重置功能
  resetSearch: () => void;
}

// ============================================================================
// 字段配置
// ============================================================================

export const FIELD_CONFIG = {
  fileName: {
    label: 'File Name',
    type: 'text' as const,
    operators: ['contains', 'equals', 'startsWith', 'endsWith'] as const,
    placeholder: 'Enter file name...',
    icon: '📄',
  },
  fileSize: {
    label: 'File Size',
    type: 'number' as const,
    operators: ['>', '<', '>=', '<=', 'equals'] as const,
    placeholder: 'Enter size in bytes...',
    icon: '📏',
    formatter: (value: number) => {
      if (value >= 1024 * 1024 * 1024) return `${(value / (1024 * 1024 * 1024)).toFixed(1)} GB`;
      if (value >= 1024 * 1024) return `${(value / (1024 * 1024)).toFixed(1)} MB`;
      if (value >= 1024) return `${(value / 1024).toFixed(1)} KB`;
      return `${value} B`;
    },
  },
  fileType: {
    label: 'File Type',
    type: 'text' as const,
    operators: ['equals', 'contains'] as const,
    placeholder: 'e.g., jpg, png, pdf...',
    icon: '🏷️',
  },
  modifiedTime: {
    label: 'Modified Time',
    type: 'date' as const,
    operators: ['>', '<', '>=', '<=', 'equals'] as const,
    placeholder: 'Select date...',
    icon: '📅',
  },
  // Phase 7B: 标签字段配置
  tags: {
    label: 'Tags',
    type: 'text' as const,
    operators: ['containsAny', 'containsAll', 'contains'] as const,
    placeholder: 'Enter tags (comma separated)...',
    icon: '🏷️',
    description: 'Search by user tags, AI tags, or metadata',
  },
} as const;

export const OPERATOR_LABELS = {
  contains: 'contains',
  equals: 'equals',
  startsWith: 'starts with',
  endsWith: 'ends with',
  '>': 'greater than',
  '<': 'less than',
  '>=': 'greater than or equal',
  '<=': 'less than or equal',
  // Phase 7B: 标签操作符
  containsAny: 'contains any of',
  containsAll: 'contains all of',
} as const;

// ============================================================================
// 初始状态
// ============================================================================

const initialState: SearchState = {
  criteria: [],
  isActive: false,
  resultCount: 0,
  isSearchPanelExpanded: false,
  lastSearchTime: 0,
  // Phase 7B: 后端搜索初始状态
  useBackendSearch: true, // 默认启用后端搜索
  currentCaseId: null,
  backendSearchEnabled: true,
  searchPerformance: {
    lastSearchDuration: 0,
    averageSearchDuration: 0,
    searchCount: 0,
    backendSearchCount: 0,
    localSearchCount: 0,
  },
};

// ============================================================================
// Store实现
// ============================================================================

export const useSearchStore = create<SearchState & SearchActions>()(
  devtools(
    persist(
      (set, get) => ({
      ...initialState,

      // ========================================
      // 搜索条件管理实现
      // ========================================
      addCriterion: (criterion) =>
        set((state) => {
          const newCriterion: SearchCriterion = {
            ...criterion,
            id: uuidv4(),
          };

          const newCriteria = [...state.criteria, newCriterion];

          return {
            criteria: newCriteria,
            isActive: newCriteria.length > 0,
            lastSearchTime: Date.now(),
          };
        }),

      removeCriterion: (id) =>
        set((state) => {
          const newCriteria = state.criteria.filter(c => c.id !== id);

          return {
            criteria: newCriteria,
            isActive: newCriteria.length > 0,
            lastSearchTime: Date.now(),
          };
        }),

      updateCriterion: (id, updates) =>
        set((state) => {
          const newCriteria = state.criteria.map(c =>
            c.id === id ? { ...c, ...updates } : c
          );

          return {
            criteria: newCriteria,
            lastSearchTime: Date.now(),
          };
        }),

      clearSearch: () =>
        set((state) => ({
          criteria: [],
          isActive: false,
          resultCount: 0,
          lastSearchTime: Date.now(),
        })),

      // ========================================
      // 搜索状态控制实现
      // ========================================
      toggleSearch: () =>
        set((state) => ({ isActive: !state.isActive })),

      setSearchActive: (active) =>
        set({ isActive: active }),

      setResultCount: (count) =>
        set({ resultCount: count }),

      // ========================================
      // UI状态控制实现
      // ========================================
      toggleSearchPanel: () =>
        set((state) => ({ isSearchPanelExpanded: !state.isSearchPanelExpanded })),

      setSearchPanelExpanded: (expanded) =>
        set({ isSearchPanelExpanded: expanded }),

      // ========================================
      // 性能统计实现
      // ========================================
      recordSearchPerformance: (duration) =>
        set((state) => {
          const newSearchCount = state.searchPerformance.searchCount + 1;
          const newAverageDuration =
            (state.searchPerformance.averageSearchDuration * state.searchPerformance.searchCount + duration) / newSearchCount;

          return {
            searchPerformance: {
              lastSearchDuration: duration,
              averageSearchDuration: newAverageDuration,
              searchCount: newSearchCount,
            },
          };
        }),

      // ========================================
      // 批量操作实现
      // ========================================
      setCriteria: (criteria) =>
        set({
          criteria,
          isActive: criteria.length > 0,
          lastSearchTime: Date.now(),
        }),

      duplicateCriterion: (id) =>
        set((state) => {
          const originalCriterion = state.criteria.find(c => c.id === id);
          if (!originalCriterion) return state;

          const duplicatedCriterion: SearchCriterion = {
            ...originalCriterion,
            id: uuidv4(),
          };

          return {
            criteria: [...state.criteria, duplicatedCriterion],
            lastSearchTime: Date.now(),
          };
        }),

      // ========================================
      // 预设搜索实现
      // ========================================
      applyPresetSearch: (preset) => {
        const presets = {
          images: [
            { field: 'fileType' as const, operator: 'contains' as const, value: 'jpg' },
            { field: 'fileType' as const, operator: 'contains' as const, value: 'png' },
          ],
          'large-files': [
            { field: 'fileSize' as const, operator: '>=' as const, value: 10 * 1024 * 1024 }, // 10MB
          ],
          recent: [
            { field: 'modifiedTime' as const, operator: '>=' as const, value: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // 7 days
          ],
          old: [
            { field: 'modifiedTime' as const, operator: '<=' as const, value: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // 30 days
          ],
        };

        const criteriaTemplates = presets[preset] || [];
        const criteria = criteriaTemplates.map(template => ({
          ...template,
          id: uuidv4(),
        }));

        set({
          criteria,
          isActive: criteria.length > 0,
          lastSearchTime: Date.now(),
        });
      },

      // ========================================
      // Phase 7B: 后端搜索控制实现
      // ========================================
      setBackendSearch: (enabled) =>
        set((state) => ({
          useBackendSearch: enabled,
          searchPerformance: {
            ...state.searchPerformance,
            [enabled ? 'backendSearchCount' : 'localSearchCount']:
              state.searchPerformance[enabled ? 'backendSearchCount' : 'localSearchCount'] + 1,
          },
        })),

      setCurrentCase: (caseId) =>
        set({ currentCaseId: caseId }),

      toggleSearchMode: () =>
        set((state) => ({
          useBackendSearch: !state.useBackendSearch,
        })),

      // ========================================
      // Phase 7C: 案例上下文搜索实现
      // ========================================

      getSearchContext: () => {
        const state = get();
        return {
          caseId: state.currentCaseId,
          useBackendSearch: state.useBackendSearch,
        };
      },

      onCaseChange: (newCaseId: number) => {
        // console.log(`🔍 Phase 7C: 搜索系统响应案例切换 -> ${newCaseId}`); // [CLEANED]

        set((state) => ({
          currentCaseId: newCaseId,
          // 清理搜索状态，避免跨案例数据污染
          criteria: [],
          isActive: false,
          resultCount: 0,
        }));
      },

      // ========================================
      // 重置功能实现
      // ========================================
      resetSearch: () => {
        set((state) => ({
          ...initialState,
          // 保留后端搜索配置
          useBackendSearch: state.useBackendSearch,
          currentCaseId: state.currentCaseId,
          backendSearchEnabled: state.backendSearchEnabled,
        }));
      },
    }),
    {
      name: 'search-store',
      // Phase 9B: 持久化搜索状态
      partialize: (state) => ({
        criteria: state.criteria,
        isSearchPanelExpanded: state.isSearchPanelExpanded,
        useBackendSearch: state.useBackendSearch,
        currentCaseId: state.currentCaseId,
      }),
    }
  ),
  { name: 'SearchStore' }
  )
);

// ============================================================================
// 选择器Hooks（性能优化）
// ============================================================================

export const useSearchCriteria = () => useSearchStore((state) => ({
  criteria: state.criteria,
  addCriterion: state.addCriterion,
  removeCriterion: state.removeCriterion,
  updateCriterion: state.updateCriterion,
  clearSearch: state.clearSearch,
}), shallow);

export const useSearchState = () => useSearchStore((state) => ({
  isActive: state.isActive,
  resultCount: state.resultCount,
  isSearchPanelExpanded: state.isSearchPanelExpanded,
  toggleSearch: state.toggleSearch,
  setResultCount: state.setResultCount,
  toggleSearchPanel: state.toggleSearchPanel,
}), shallow);

export const useSearchPerformance = () => useSearchStore((state) => ({
  performance: state.searchPerformance,
  recordSearchPerformance: state.recordSearchPerformance,
}), shallow);

// Phase 7B: 后端搜索选择器
export const useBackendSearchState = () => useSearchStore((state) => ({
  useBackendSearch: state.useBackendSearch,
  currentCaseId: state.currentCaseId,
  backendSearchEnabled: state.backendSearchEnabled,
  setBackendSearch: state.setBackendSearch,
  setCurrentCase: state.setCurrentCase,
  toggleSearchMode: state.toggleSearchMode,
}), shallow);

export default useSearchStore;
