// Project Novak - Electron Main Process (JavaScript Version for Testing)
// 简化版本用于测试 Electron 工作流

const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp'); // Phase 3: 添加Sharp库用于缩略图生成
const trash = require('trash'); // Phase 5C: 添加trash库用于安全删除文件

// 判断环境
const isDev = process.env.NODE_ENV === 'development';

let mainWindow;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      // Phase 9C-Hotfix Extension: 开发环境安全配置
      webSecurity: isDev ? false : true, // 开发环境禁用web安全以支持Vite
    },
    show: false,
  });

  // Phase 9C-Hotfix Extension: 配置Content Security Policy
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': isDev
          ? ['default-src * \'unsafe-inline\' \'unsafe-eval\' data: blob:; connect-src *;']
          : ['default-src \'self\'; script-src \'self\'; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data:; connect-src \'self\';']
      }
    });
  });

  // 窗口加载完成后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // 加载应用内容
  if (isDev) {
    // 开发模式：加载Vite开发服务器
    mainWindow.loadURL('http://localhost:5180');
    console.log('🚀 开发模式：加载React应用 http://localhost:5180');
  } else {
    // 生产环境：加载构建好的静态文件
    const indexPath = path.join(__dirname, '../dist/index.html');
    mainWindow.loadFile(indexPath);
    console.log('📦 生产模式：加载构建文件', indexPath);
  }

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 应用准备就绪
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// 所有窗口关闭
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// ========================================
// IPC 处理器 - 应用信息和文件系统操作
// ========================================

// 获取应用版本信息
ipcMain.handle('get-app-version', async () => {
  try {
    return {
      appVersion: app.getVersion(),
      electronVersion: process.versions.electron,
      nodeVersion: process.versions.node,
      chromeVersion: process.versions.chrome
    };
  } catch (error) {
    console.error('❌ 获取应用版本失败:', error);
    return {
      appVersion: '1.0.0',
      electronVersion: process.versions.electron || 'unknown',
      nodeVersion: process.versions.node || 'unknown',
      chromeVersion: process.versions.chrome || 'unknown'
    };
  }
});

// 获取应用路径信息
ipcMain.handle('get-app-paths', async () => {
  try {
    return {
      appData: app.getPath('appData'),
      userData: app.getPath('userData'),
      documents: app.getPath('documents'),
      downloads: app.getPath('downloads'),
      pictures: app.getPath('pictures')
    };
  } catch (error) {
    console.error('❌ 获取应用路径失败:', error);
    return {};
  }
});

// Phase 3: 升级的获取目录内容处理器 - 支持丰富的文件信息和缩略图
ipcMain.handle('get-directory-contents', async (_, dirPath) => {
  try {
    console.log('📁 Phase 3: 获取目录内容:', dirPath);

    // 检查路径是否存在
    if (!fs.existsSync(dirPath)) {
      return { error: `目录不存在: ${dirPath}` };
    }

    // 检查是否为目录
    const dirStats = fs.statSync(dirPath);
    if (!dirStats.isDirectory()) {
      return { error: `路径不是目录: ${dirPath}` };
    }

    // 读取目录内容
    const fileNames = fs.readdirSync(dirPath);
    console.log(`🔍 Phase 4: 并发处理 ${fileNames.length} 个项目...`);

    // Phase 4: 内存缓存，避免重复生成缩略图
    const thumbnailCache = new Map();

    // Phase 4: 并发处理文件信息和缩略图生成
    const fileEntryPromises = fileNames.map(async (name) => {
      const fullPath = path.join(dirPath, name);
      try {
        const stats = fs.statSync(fullPath);
        const isImage = /\.(jpe?g|png|webp|gif|bmp)$/i.test(name);

        const fileEntry = {
          name: name,
          path: fullPath,
          isDirectory: stats.isDirectory(),
          size: stats.size,
          createdAt: stats.birthtimeMs,
          modifiedAt: stats.mtimeMs,
        };

        // Phase 4: 如果是图片文件，并发生成缩略图
        if (isImage && !stats.isDirectory()) {
          // 使用文件路径和修改时间作为缓存键
          const cacheKey = `${fullPath}_${stats.mtimeMs}`;

          if (thumbnailCache.has(cacheKey)) {
            console.log(`📋 使用缓存缩略图: ${name}`);
            fileEntry.thumbnail = thumbnailCache.get(cacheKey);
          } else {
            try {
              console.log(`🖼️ 并发生成缩略图: ${name}`);
              const buffer = await sharp(fullPath)
                .resize(150, 150, { fit: 'cover' }) // 150x150 缩略图
                .jpeg({ quality: 80 }) // 转换为JPEG格式，80%质量
                .toBuffer();

              const thumbnail = `data:image/jpeg;base64,${buffer.toString('base64')}`;
              fileEntry.thumbnail = thumbnail;

              // 缓存缩略图
              thumbnailCache.set(cacheKey, thumbnail);
              console.log(`✅ 缩略图生成成功: ${name} (${buffer.length} bytes)`);
            } catch (thumbError) {
              console.error(`❌ 缩略图生成失败 ${name}:`, thumbError.message);
              fileEntry.thumbnail = undefined;
            }
          }
        }

        return fileEntry;
      } catch (e) {
        console.warn(`⚠️ 无法读取文件信息 ${fullPath}，跳过。错误:`, e.message);
        return null;
      }
    });

    // Phase 4: 等待所有文件处理完成，过滤掉null值
    const fileEntries = (await Promise.all(fileEntryPromises)).filter(entry => entry !== null);

    console.log(`✅ Phase 4: 成功并发处理目录，包含 ${fileEntries.length} 个项目`);
    return { success: true, contents: fileEntries };

  } catch (error) {
    console.error('❌ Phase 4: 读取目录失败:', error);
    return {
      error: `读取目录失败: ${error.message}`
    };
  }
});

// Phase 4: 添加缩略图生成进度反馈API
ipcMain.handle('get-thumbnail-progress', async () => {
  // 这里可以返回当前缩略图生成的进度信息
  // 暂时返回简单的状态信息
  return {
    isGenerating: false,
    completed: 0,
    total: 0
  };
});

// Phase 5B: 目录选择对话框处理器
ipcMain.handle('select-directory', async () => {
  try {
    console.log('📁 Phase 5B: 打开目录选择对话框');

    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory'],
      title: '选择图像目录',
      buttonLabel: '选择目录'
    });

    if (result.canceled) {
      console.log('📁 用户取消了目录选择');
      return { canceled: true };
    }

    const selectedPath = result.filePaths[0];
    console.log('📁 用户选择了目录:', selectedPath);

    return {
      canceled: false,
      path: selectedPath
    };
  } catch (error) {
    console.error('❌ 目录选择失败:', error);
    return {
      canceled: false,
      error: error.message
    };
  }
});

// Phase 5C: 批量删除文件处理器
ipcMain.handle('delete-files', async (_, filePaths) => {
  try {
    console.log('🗑️ Phase 5C: 开始批量删除文件:', filePaths.length, '个文件');

    // 验证所有文件路径
    const validPaths = [];
    const invalidPaths = [];

    for (const filePath of filePaths) {
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        if (stats.isFile()) {
          validPaths.push(filePath);
        } else {
          invalidPaths.push({ path: filePath, error: '不是文件' });
        }
      } else {
        invalidPaths.push({ path: filePath, error: '文件不存在' });
      }
    }

    console.log('✅ 有效文件:', validPaths.length, '个');
    console.log('❌ 无效文件:', invalidPaths.length, '个');

    // 使用Promise.allSettled进行安全的批量删除
    const deleteResults = await Promise.allSettled(
      validPaths.map(async (filePath) => {
        await trash(filePath);
        return filePath;
      })
    );

    // 处理删除结果
    const success = [];
    const failed = [];

    deleteResults.forEach((result, index) => {
      const filePath = validPaths[index];
      if (result.status === 'fulfilled') {
        success.push(filePath);
        console.log('✅ 删除成功:', path.basename(filePath));
      } else {
        failed.push({
          path: filePath,
          error: result.reason?.message || '删除失败'
        });
        console.error('❌ 删除失败:', path.basename(filePath), result.reason);
      }
    });

    // 合并无效路径到失败列表
    failed.push(...invalidPaths);

    const result = {
      success,
      failed,
      total: filePaths.length,
      successCount: success.length,
      failedCount: failed.length
    };

    console.log('🗑️ 批量删除完成:', result.successCount, '成功,', result.failedCount, '失败');
    return result;

  } catch (error) {
    console.error('❌ 批量删除异常:', error);
    return {
      success: [],
      failed: filePaths.map(path => ({ path, error: error.message })),
      total: filePaths.length,
      successCount: 0,
      failedCount: filePaths.length
    };
  }
});

// 新增：文件选择对话框处理器 - 用于导入图像文件
ipcMain.handle('dialog:openFile', async () => {
  try {
    console.log('📁 打开文件选择对话框');

    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile', 'multiSelections'], // 允许选择多个文件
      title: '选择要导入的图像文件',
      buttonLabel: '导入文件',
      filters: [
        {
          name: '图像文件',
          extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']
        },
        {
          name: '所有文件',
          extensions: ['*']
        }
      ]
    });

    if (result.canceled) {
      console.log('📁 用户取消了文件选择');
      return [];
    }

    const selectedPaths = result.filePaths;
    console.log('📁 用户选择了文件:', selectedPaths.length, '个文件');
    console.log('📁 文件路径:', selectedPaths);

    return selectedPaths;
  } catch (error) {
    console.error('❌ 文件选择失败:', error);
    return [];
  }
});

console.log('Electron main process started in', isDev ? 'development' : 'production', 'mode');
