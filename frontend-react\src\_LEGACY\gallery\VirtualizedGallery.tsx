// Phase 4: 虚拟化Gallery组件 - 支持大量文件的高性能渲染

import React, { useMemo, useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { FileCard } from '@/components/ui/FileCard';

interface VirtualizedGalleryProps {
  files: any[];
  selectedFileIds: number[];
  showFileName: boolean;
  showFileInfo: boolean;
  onFileSelect: (fileId: number, selected: boolean, event?: React.MouseEvent) => void; // Phase 5C: 添加event参数
  onFileDoubleClick: (file: any) => void;
  className?: string;
}
export const VirtualizedGallery: React.FC<VirtualizedGalleryProps> = ({
  files,
  selectedFileIds,
  showFileName,
  showFileInfo,
  onFileSelect,
  onFileDoubleClick,
  className = '',
}) => {
  const parentRef = useRef<HTMLDivElement>(null);

  // 计算网格布局参数
  const ITEM_WIDTH = 160; // 文件卡片宽度
  const ITEM_HEIGHT = 160; // 文件卡片高度
  const GAP = 16; // 间距
  const CONTAINER_PADDING = 24; // 容器内边距

  // 计算每行可以显示多少个项目
  const columnsPerRow = useMemo(() => {
    if (!parentRef.current) return 4; // 默认值
    const containerWidth = parentRef.current.clientWidth - CONTAINER_PADDING * 2;
    return Math.floor((containerWidth + GAP) / (ITEM_WIDTH + GAP)) || 1;
  }, []);

  // 数据转换函数：将后端数据格式转换为FileCard期望的格式
  const transformFileData = React.useCallback((backendFile: any) => {
    return {
      id: backendFile.id,
      fileName: backendFile.file_name || backendFile.fileName || 'Unknown File',
      filePath: backendFile.file_path || backendFile.filePath || '',
      fileType: backendFile.file_type || backendFile.fileType || 'application/octet-stream',
      fileSize: backendFile.file_size || backendFile.fileSize || 0,
      width: backendFile.width,
      height: backendFile.height,
      thumbnailPath: backendFile.thumbnail_small_path || backendFile.thumbnailPath,
      createdAt: backendFile.created_at || backendFile.createdAt,
      takenAt: backendFile.taken_at || backendFile.takenAt,
    };
  }, []);

  // 将一维文件数组转换为二维行数组
  const rows = useMemo(() => {
    const result: any[][] = [];
    for (let i = 0; i < files.length; i += columnsPerRow) {
      result.push(files.slice(i, i + columnsPerRow));
    }
    return result;
  }, [files, columnsPerRow]);

  // 配置虚拟滚动
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => ITEM_HEIGHT + GAP,
    overscan: 5, // 预渲染5行
  });
  if (files.length === 0) {
    return (
      <div className={`h-full flex items-center justify-center ${className}`}>
        <div className="text-center space-y-4 max-w-md">
          <div className="text-6xl">📂</div>
          <h3 className="text-lg font-medium">目录为空</h3>
          <p className="text-muted-foreground">
            当前目录中没有文件
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className={`h-full overflow-auto ${className}`}
      style={{ contain: 'strict' }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualRow) => {
          const row = rows[virtualRow.index];
          if (!row) return null;

          return (
            <div
              key={virtualRow.index}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`,
              }}
            >
              <div
                className="flex gap-4 px-6"
                style={{
                  height: '100%',
                  alignItems: 'flex-start',
                }}
              >
                {row.map((file, columnIndex) => {
                  const transformedFile = transformFileData(file);
                  return (
                    <div
                      key={file.id}
                      style={{
                        width: `${ITEM_WIDTH}px`,
                        height: `${ITEM_HEIGHT}px`,
                        flexShrink: 0,
                      }}
                    >
                      <FileCard
                        file={transformedFile}
                        selected={selectedFileIds.includes(file.id)}
                        showFileName={showFileName}
                        showFileInfo={showFileInfo}
                        onSelect={onFileSelect}
                        onDoubleClick={onFileDoubleClick}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
