// src/components/ui/SearchInput.tsx
import React from 'react';
import { SearchIcon } from '../../assets/icons/SearchIcon';

interface SearchInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}

export function SearchInput({ placeholder = "搜索", value, onChange, className }: SearchInputProps) {
  return (
    <div className={`relative ${className}`}>
      <input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        className="
          w-[80px] h-[20px] px-space-2 text-sm
          border border-[#353639] rounded-none
          focus:outline-none
          transition-colors
        "
        style={{
          backgroundColor: '#131416',
          color: '#9F9FA2'
        }}
      />
    </div>
  );
}
