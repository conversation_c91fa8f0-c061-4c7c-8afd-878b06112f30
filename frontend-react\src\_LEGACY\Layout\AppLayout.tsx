import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { useLayout } from "../../context/LayoutContext";
// 新架构面板组件导入
import { NavPanel } from "../panels/NavPanel";
// 🚨 移除僵尸组件：BackendGalleryPanel
// import { BackendGalleryPanel } from "../panels/BackendGalleryPanel";
import { WorkbenchPanel } from "../panels/WorkbenchPanel";
import { DetailsPanel } from "../panels/DetailsPanel";
// ✅ 导入纯净的文件显示组件
import { FileDisplayArea } from "../files/FileDisplayArea";
// 6.4.1 弹窗组件导入
import { ArchiveManagerModal } from "../modals/ArchiveManagerModal";
// 🔧 重新启用ImageUploadModal以确认问题
import { ImageUploadModal } from "../modals/ImageUploadModal";
// 工作台折叠指示器
import { CollapsedWorkbenchBar } from "../ui/CollapsedWorkbenchBar";
// 侧边栏折叠指示器
import { CollapsedSidebarBar } from "../ui/CollapsedSidebarBar";
// 工具栏组件 - 我们接下来会创建这些
import { NavigationToolbarSet } from '../toolbars/NavigationToolbarSet';
import { GalleryToolbarSet } from '../toolbars/GalleryToolbarSet';
import { WorkbenchToolbarSet } from '../toolbars/WorkbenchToolbarSet';
import { DetailsToolbarSet } from '../toolbars/DetailsToolbarSet';

export function AppLayout() {
  const {
    isNavVisible,
    isDetailsVisible,
    isWorkbenchVisible,
    isArchiveModalOpen,
    isUploadModalOpen,
    closeArchiveModal,
    closeUploadModal,
    navPanelRef,
    detailsPanelRef,
    workbenchPanelRef
  } = useLayout();

  // 你的规格书定义了像素级的内边距，我们直接应用
  const panelPadding = 'p-[12px_8px]'; // 上下12px, 左右8px

  return (
    // 设置全局字体和颜色
    <div className="h-screen w-screen bg-mizzy-bg-main font-sans text-sm text-mizzy-text-content overflow-hidden">
      <PanelGroup direction="horizontal" autoSaveId="main-layout">
        {/* === A: 导航面板 === */}
        {isNavVisible && (
          <>
            <Panel
              ref={navPanelRef}
              defaultSize={10}
              minSize={8}
              maxSize={23}
              className={`${panelPadding} bg-mizzy-bg-panel flex flex-col`}
              collapsible={true}
              collapsedSize={8}
              style={{ backgroundColor: '#1F2023' }}
            >
              <NavigationToolbarSet />
              {/* 导航面板的其余内容 (例如标签树) 将放在这里 */}
              <NavPanel />
            </Panel>
            {/* 分割线：宽度1px，应用特定颜色和悬停效果 */}
            <PanelResizeHandle
              className="w-[1px] bg-mizzy-resizer-line hover:bg-mizzy-resizer-hover transition-colors cursor-col-resize"
              style={{ minWidth: '1px' }}
            />
          </>
        )}

        {/* === 中间区域 (B & C 面板) === */}
        <Panel defaultSize={80}>
          <PanelGroup direction="vertical" autoSaveId="main-content">

            {/* === B: 画廊面板 === */}
            <Panel defaultSize={70} className={`${panelPadding} bg-mizzy-bg-main flex flex-col`} style={{ backgroundColor: '#18191C' }}>
              {/* 上半部分: 纯净的工具栏 */}
              <div className="flex-shrink-0">
                <GalleryToolbarSet />
              </div>

              {/* 下半部分: 纯净、可滚动、并填满剩余空间的文件显示区域 */}
              <div className="flex-1 overflow-auto">
                <FileDisplayArea />
              </div>
            </Panel>

            {/* Workbench Panel (C区) */}
            <PanelResizeHandle
              className="h-[1px] bg-mizzy-resizer-line hover:bg-mizzy-resizer-hover transition-colors cursor-row-resize"
              style={{ minHeight: '1px' }}
            />

            {/* === C: 工作台面板 === */}
            <Panel
              ref={workbenchPanelRef}
              defaultSize={isWorkbenchVisible ? 30 : 0}
              minSize={15}
              maxSize={50}
              collapsible={true}
              className={`${panelPadding} bg-mizzy-bg-main flex flex-col`}
              collapsedSize={0}
              style={{ backgroundColor: '#18191C' }}
            >
              <WorkbenchToolbarSet />
              {/* 工作台内容将放在这里 */}
              <WorkbenchPanel />
            </Panel>
          </PanelGroup>
        </Panel>

        {/* === D: 详情面板 === */}
        {isDetailsVisible && (
          <>
            <PanelResizeHandle
              className="w-[1px] bg-mizzy-resizer-line hover:bg-mizzy-resizer-hover transition-colors cursor-col-resize"
              style={{ minWidth: '1px' }}
            />

            <Panel
              ref={detailsPanelRef}
              defaultSize={10}
              minSize={8}
              maxSize={23}
              className={`${panelPadding} bg-mizzy-bg-panel flex flex-col`}
              collapsible={true}
              collapsedSize={8}
              style={{ backgroundColor: '#1F2023' }}
            >
              <DetailsToolbarSet />
              {/* 详情面板内容 */}
              <DetailsPanel />
            </Panel>
          </>
        )}
      </PanelGroup>

      {/* Modals */}
      <ArchiveManagerModal
        isOpen={isArchiveModalOpen}
        onClose={closeArchiveModal}
      />
      {/* 图像上传模态窗口 - 纯本地路径导入版本 */}
      {/* 🔧 重新启用ImageUploadModal以确认问题 */}
      <ImageUploadModal
        isOpen={isUploadModalOpen}
        onClose={closeUploadModal}
      />

      {/* Collapsed Workbench Indicator */}
      {!isWorkbenchVisible && (
        <CollapsedWorkbenchBar />
      )}

      {/* Collapsed Sidebar Indicator */}
      {!isNavVisible && !isDetailsVisible && (
        <CollapsedSidebarBar />
      )}
    </div>
  );
}
