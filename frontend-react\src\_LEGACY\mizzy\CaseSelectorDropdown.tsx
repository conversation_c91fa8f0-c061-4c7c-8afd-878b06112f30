// CaseSelectorDropdown.tsx - 档案库选择下拉组件
// 替代左侧边栏，成为选择档案库的主要方式

import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Folder, Plus } from 'lucide-react';
import { ICase, getAllCases, createNewCase } from '../../services/apiService';

interface CaseSelectorDropdownProps {
  activeCase: ICase | null;
  onCaseSelect: (selectedCase: ICase) => void;
  className?: string;
}

export function CaseSelectorDropdown({ 
  activeCase, 
  onCaseSelect, 
  className = '' 
}: CaseSelectorDropdownProps) {
  const [cases, setCases] = useState<ICase[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newCaseName, setNewCaseName] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 加载档案库列表
  useEffect(() => {
    loadCases();
  }, []);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsCreating(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadCases = async () => {
    try {
      setIsLoading(true);
      // console.log('📋 加载档案库列表'); // [CLEANED]
      const caseList = await getAllCases();
      setCases(caseList);
      // console.log(`✅ 加载了 ${caseList.length} 个档案库`); // [CLEANED]
    } catch (error) {
      console.error('❌ 加载档案库列表失败:', error);
      alert('加载档案库列表失败，请检查后端连接。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCaseSelect = (selectedCase: ICase) => {
    // console.log('📋 选择档案库:', selectedCase.case_name); // [CLEANED]
    onCaseSelect(selectedCase);
    setIsOpen(false);
  };

  const handleCreateCase = async () => {
    if (!newCaseName.trim()) {
      alert('请输入档案库名称');
      return;
    }

    try {
      // console.log('📋 创建新档案库:', newCaseName); // [CLEANED]
      const newCase = await createNewCase({
        case_name: newCaseName.trim(),
        description: `创建于 ${new Date().toLocaleString()}`
      });
      
      // 更新列表并选中新创建的档案库
      setCases(prev => [newCase, ...prev]);
      onCaseSelect(newCase);
      setNewCaseName('');
      setIsCreating(false);
      setIsOpen(false);
      
      // console.log('✅ 档案库创建成功:', newCase.case_name); // [CLEANED]
    } catch (error) {
      console.error('❌ 创建档案库失败:', error);
      alert('创建档案库失败，请重试。');
    }
  };

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* 下拉触发按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className="
          flex items-center justify-between w-full px-4 py-2
          bg-gray-800 hover:bg-gray-700 border border-gray-600
          text-white rounded-lg transition-colors duration-200
          disabled:opacity-50 disabled:cursor-not-allowed
        "
      >
        <div className="flex items-center gap-2">
          <Folder size={16} />
          <span className="truncate">
            {isLoading 
              ? '加载中...' 
              : activeCase 
                ? activeCase.case_name 
                : '选择档案库'
            }
          </span>
        </div>
        <ChevronDown 
          size={16} 
          className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="
          absolute top-full left-0 right-0 mt-1 z-50
          bg-gray-800 border border-gray-600 rounded-lg shadow-lg
          max-h-64 overflow-y-auto
        ">
          {/* 创建新档案库选项 */}
          <div className="p-2 border-b border-gray-600">
            {isCreating ? (
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newCaseName}
                  onChange={(e) => setNewCaseName(e.target.value)}
                  placeholder="输入档案库名称"
                  className="
                    flex-1 px-2 py-1 bg-gray-700 border border-gray-500
                    text-white text-sm rounded focus:outline-none focus:border-blue-500
                  "
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleCreateCase();
                    if (e.key === 'Escape') setIsCreating(false);
                  }}
                  autoFocus
                />
                <button
                  onClick={handleCreateCase}
                  className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded"
                >
                  创建
                </button>
              </div>
            ) : (
              <button
                onClick={() => setIsCreating(true)}
                className="
                  flex items-center gap-2 w-full px-2 py-1
                  text-blue-400 hover:text-blue-300 hover:bg-gray-700
                  text-sm rounded transition-colors duration-200
                "
              >
                <Plus size={14} />
                新建档案库
              </button>
            )}
          </div>

          {/* 档案库列表 */}
          <div className="py-1">
            {cases.length === 0 ? (
              <div className="px-4 py-2 text-gray-400 text-sm">
                暂无档案库
              </div>
            ) : (
              cases.map((caseItem) => (
                <button
                  key={caseItem.id}
                  onClick={() => handleCaseSelect(caseItem)}
                  className={`
                    flex items-center gap-2 w-full px-4 py-2 text-left
                    hover:bg-gray-700 transition-colors duration-200
                    ${activeCase?.id === caseItem.id ? 'bg-blue-600 text-white' : 'text-gray-300'}
                  `}
                >
                  <Folder size={14} />
                  <div className="flex-1 min-w-0">
                    <div className="truncate font-medium">{caseItem.case_name}</div>
                    {caseItem.description && (
                      <div className="truncate text-xs text-gray-400">
                        {caseItem.description}
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-gray-400">
                    {caseItem.files?.length || 0} 文件
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
