import * as React from 'react';
import { Button, Input, TagItem } from '@/components/ui';
import { cn } from '@/utils/cn';
// Phase 7B: 导入TagEditor组件
import TagEditor from '@/components/metadata/TagEditor';
import { StatusDisplay } from '@/components/feedback/StatusDisplay';

// ============================================================================
// InfoPanel Component Interface
// ============================================================================

export interface InfoPanelProps {
  /**
   * 当前选中的文件信息
   */
  selectedFile?: {
    id: number;
    fileName: string;
    filePath: string;
    fileType: string;
    fileSize: number;
    width?: number;
    height?: number;
    thumbnailPath?: string;
    createdAt?: string;
    tags?: {
      metadata: Record<string, string>;
      cv: Record<string, unknown>;
      user: string[];
      ai: string[];
      custom: Record<string, string>;
    };
  };

  /**
   * 选中的文件数量
   */
  selectedCount?: number;

  /**
   * 活动面板
   */
  activePanels?: Array<'metadata' | 'rules' | 'cv' | 'ai'>;

  /**
   * 面板切换回调
   */
  onPanelToggle?: (panel: 'metadata' | 'rules' | 'cv' | 'ai') => void;

  /**
   * 标签操作回调
   */
  onTagOperation?: (operation: 'add' | 'remove', category: string, tag: string) => void;

  className?: string;
}

// ============================================================================
// InfoPanel Component Implementation
// ============================================================================

/**
 * InfoPanel 组件 - 信息栏面板
 *
 * 实现 Mizzy Star 的文件信息展示和编辑功能：
 * 1. 元数据面板 - 显示文件基本信息
 * 2. 研究者标签规则 - 自定义标签管理
 * 3. 计算机视觉规则 - 质量分析配置
 * 4. AI分析 - 智能标签和描述
 *
 * 对应原始需求中的【信息栏】功能
 */
const InfoPanel = React.forwardRef<HTMLDivElement, InfoPanelProps>(
  ({
    selectedFile,
    selectedCount = 0,
    activePanels = ['metadata'],
    onPanelToggle,
    onTagOperation,
    className,
    ...props
  }, ref) => {



    return (
      <div
        ref={ref}
        className={cn(
          'h-full flex flex-col bg-card',
          className
        )}
        {...props}
      >
        {/* 面板标签栏 */}
        <div className="flex-shrink-0 border-b border-border">
          <div className="flex flex-wrap">
            <Button
              variant={activePanels.includes('metadata') ? 'primary' : 'ghost'}
              size="sm"
              className="rounded-none border-r text-xs"
              onClick={() => onPanelToggle?.('metadata')}
            >
              📊 元数据
            </Button>
            <Button
              variant={activePanels.includes('rules') ? 'primary' : 'ghost'}
              size="sm"
              className="rounded-none border-r text-xs"
              onClick={() => onPanelToggle?.('rules')}
            >
              👤 规则
            </Button>
            <Button
              variant={activePanels.includes('cv') ? 'primary' : 'ghost'}
              size="sm"
              className="rounded-none border-r text-xs"
              onClick={() => onPanelToggle?.('cv')}
            >
              🤖 视觉
            </Button>
            <Button
              variant={activePanels.includes('ai') ? 'primary' : 'ghost'}
              size="sm"
              className="rounded-none text-xs"
              onClick={() => onPanelToggle?.('ai')}
            >
              🧠 AI
            </Button>
          </div>
        </div>

        {/* 信息内容区域 */}
        <div className="flex-1 overflow-y-auto">
          <StatusDisplay
            status={!selectedFile ? 'empty' : 'idle'}
            message={!selectedFile ? (
              <div className="space-y-4">
                <div className="text-4xl">📄</div>
                <h3 className="text-lg font-medium">未选择文件</h3>
                <p className="text-muted-foreground">
                  {selectedCount > 0
                    ? `已选择 ${selectedCount} 个文件`
                    : '在画廊中选择文件查看详细信息'
                  }
                </p>
              </div>
            ) : undefined}
          >
            <div className="space-y-4 animate-fade-in">
              {/* 元数据面板 */}
              {activePanels.includes('metadata') && (
                <div className="animate-slide-up">
                  <MetadataPanel file={selectedFile} />
                </div>
              )}

              {/* 研究者标签规则面板 */}
              {activePanels.includes('rules') && (
                <div className="animate-slide-up" style={{ animationDelay: '100ms' }}>
                  <RulesPanel
                    file={selectedFile}
                    onTagOperation={onTagOperation}
                  />
                </div>
              )}

              {/* 计算机视觉面板 */}
              {activePanels.includes('cv') && (
                <div className="animate-slide-up" style={{ animationDelay: '200ms' }}>
                  <CVPanel file={selectedFile} />
                </div>
              )}

              {/* AI分析面板 */}
              {activePanels.includes('ai') && (
                <div className="animate-slide-up" style={{ animationDelay: '300ms' }}>
                  <AIPanel file={selectedFile} />
                </div>
              )}
            </div>
          </StatusDisplay>
        </div>
      </div>
    );
  }
);

// ============================================================================
// 元数据面板子组件
// ============================================================================

interface MetadataPanelProps {
  file: InfoPanelProps['selectedFile'];
}

const MetadataPanel: React.FC<MetadataPanelProps> = ({ file }) => {
  const [imageLoading, setImageLoading] = React.useState(true);
  const [imageError, setImageError] = React.useState(false);

  if (!file) return null;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 当文件变化时重置加载状态
  React.useEffect(() => {
    setImageLoading(true);
    setImageError(false);
  }, [file.filePath]);

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-sm font-medium text-foreground border-b pb-2">
        📊 文件信息
      </h3>

      {/* 缩略图 */}
      <div className="flex justify-center">
        <div className="relative w-32 h-32">
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted rounded-lg border">
              <div className="spinner w-6 h-6"></div>
            </div>
          )}
          {imageError ? (
            <div className="w-32 h-32 flex items-center justify-center bg-muted rounded-lg border">
              <div className="text-center text-muted-foreground">
                <div className="text-2xl">🖼️</div>
                <div className="text-xs">加载失败</div>
              </div>
            </div>
          ) : (
            <img
              src={file.thumbnailPath || file.filePath}
              alt={file.fileName}
              className="w-32 h-32 object-cover rounded-lg border animate-fade-in"
              onLoad={() => setImageLoading(false)}
              onError={() => {
                setImageLoading(false);
                setImageError(true);
              }}
              style={{ display: imageLoading ? 'none' : 'block' }}
            />
          )}
        </div>
      </div>

      {/* 基本信息 */}
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-muted-foreground">文件名:</span>
          <span className="font-medium truncate ml-2">{file.fileName}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">大小:</span>
          <span>{formatFileSize(file.fileSize)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">类型:</span>
          <span>{file.fileType}</span>
        </div>
        {file.width && file.height && (
          <div className="flex justify-between">
            <span className="text-muted-foreground">尺寸:</span>
            <span>{file.width} × {file.height}</span>
          </div>
        )}
        {file.createdAt && (
          <div className="flex justify-between">
            <span className="text-muted-foreground">创建时间:</span>
            <span>{new Date(file.createdAt).toLocaleDateString()}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span className="text-muted-foreground">路径:</span>
          <span className="text-xs truncate ml-2" title={file.filePath}>
            {file.filePath}
          </span>
        </div>
      </div>

      {/* 元数据标签 */}
      {file.tags?.metadata && Object.keys(file.tags.metadata).length > 0 && (
        <div className="space-y-2">
          <h4 className="text-xs font-medium text-muted-foreground">元数据标签</h4>
          <div className="flex flex-wrap gap-1">
            {Object.entries(file.tags.metadata).map(([key, value]) => (
              <TagItem key={key} variant="outline" size="sm">
                {key}: {value}
              </TagItem>
            ))}
          </div>
        </div>
      )}

      {/* Phase 7B: 集成TagEditor */}
      <div className="mt-4 pt-4 border-t">
        <TagEditor filePath={file.filePath} />
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-2 pt-2 border-t">
        <Button variant="outline" size="sm" className="flex-1">
          📁 打开位置
        </Button>
        <Button variant="outline" size="sm" className="flex-1">
          📋 复制路径
        </Button>
      </div>
    </div>
  );
};

// ============================================================================
// 研究者标签规则面板子组件
// ============================================================================

interface RulesPanelProps {
  file: InfoPanelProps['selectedFile'];
  onTagOperation?: InfoPanelProps['onTagOperation'];
}

const RulesPanel: React.FC<RulesPanelProps> = ({ file, onTagOperation }) => {
  const [newTag, setNewTag] = React.useState('');

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-sm font-medium text-foreground border-b pb-2">
        👤 研究者标签规则
      </h3>

      {/* 文件名生成标签 */}
      <div className="space-y-3">
        <h4 className="text-xs font-medium text-muted-foreground">文件名生成标签</h4>
        <div className="p-3 bg-muted/30 rounded-lg space-y-2">
          <div className="text-xs text-muted-foreground">
            当前文件名: {file?.fileName}
          </div>
          <Input
            placeholder="输入标签模式，如：摄影师_年代_编号"
            size="sm"
          />
          <Button variant="primary" size="sm" className="w-full">
            🏷️ 应用规则
          </Button>
        </div>
      </div>

      {/* 手动添加标签 */}
      <div className="space-y-3">
        <h4 className="text-xs font-medium text-muted-foreground">手动添加标签</h4>
        <div className="flex gap-2">
          <Input
            placeholder="输入标签名称"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            size="sm"
            className="flex-1"
          />
          <Button
            variant="primary"
            size="sm"
            onClick={() => {
              if (newTag.trim()) {
                onTagOperation?.('add', 'user', newTag.trim());
                setNewTag('');
              }
            }}
          >
            ➕
          </Button>
        </div>
      </div>

      {/* 当前用户标签 */}
      {file?.tags?.user && file.tags.user.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-xs font-medium text-muted-foreground">用户标签</h4>
          <div className="flex flex-wrap gap-1">
            {file.tags.user.map((tag, index) => (
              <TagItem
                key={index}
                variant="secondary"
                size="sm"
                removable
                onRemove={() => onTagOperation?.('remove', 'user', tag)}
              >
                {tag}
              </TagItem>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// 计算机视觉面板子组件
// ============================================================================

interface CVPanelProps {
  file: InfoPanelProps['selectedFile'];
}

const CVPanel: React.FC<CVPanelProps> = ({ file: _file }) => {
  return (
    <div className="p-4 space-y-4">
      <h3 className="text-sm font-medium text-foreground border-b pb-2">
        🤖 计算机视觉分析
      </h3>

      {/* 质量评分 */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-muted-foreground">质量评分</h4>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="p-2 bg-muted/30 rounded">
            <div className="text-muted-foreground">清晰度</div>
            <div className="font-medium">85/100</div>
          </div>
          <div className="p-2 bg-muted/30 rounded">
            <div className="text-muted-foreground">曝光</div>
            <div className="font-medium">92/100</div>
          </div>
          <div className="p-2 bg-muted/30 rounded">
            <div className="text-muted-foreground">色彩</div>
            <div className="font-medium">78/100</div>
          </div>
          <div className="p-2 bg-muted/30 rounded">
            <div className="text-muted-foreground">构图</div>
            <div className="font-medium">88/100</div>
          </div>
        </div>
      </div>

      {/* 视觉映射配置 */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-muted-foreground">视觉映射配置</h4>
        <Button variant="outline" size="sm" className="w-full">
          ⚙️ 配置映射规则
        </Button>
      </div>

      {/* 生成的标签 */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-muted-foreground">生成标签</h4>
        <div className="flex flex-wrap gap-1">
          <TagItem variant="success" size="sm">标准曝光</TagItem>
          <TagItem variant="primary" size="sm">高清晰度</TagItem>
          <TagItem variant="warning" size="sm">色彩偏暖</TagItem>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// AI分析面板子组件
// ============================================================================

interface AIPanelProps {
  file: InfoPanelProps['selectedFile'];
}

const AIPanel: React.FC<AIPanelProps> = ({ file }) => {
  return (
    <div className="p-4 space-y-4">
      <h3 className="text-sm font-medium text-foreground border-b pb-2">
        🧠 AI分析
      </h3>

      {/* 模型配置 */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-muted-foreground">模型配置</h4>
        <div className="space-y-2">
          <Input placeholder="API Key" size="sm" type="password" />
          <select className="w-full p-2 text-xs border border-border rounded">
            <option>GPT-4 Vision</option>
            <option>Claude Vision</option>
            <option>Gemini Pro Vision</option>
          </select>
        </div>
      </div>

      {/* AI生成的描述 */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-muted-foreground">图像描述</h4>
        <div className="p-3 bg-muted/30 rounded-lg text-xs">
          这是一张城市夜景照片，展现了现代建筑群在夜晚的璀璨灯光。画面构图均衡，色彩层次丰富，具有很强的视觉冲击力。
        </div>
      </div>

      {/* AI标签 */}
      {file?.tags?.ai && file.tags.ai.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-xs font-medium text-muted-foreground">AI标签</h4>
          <div className="flex flex-wrap gap-1">
            {file.tags.ai.map((tag, index) => (
              <TagItem key={index} variant="primary" size="sm">
                {tag}
              </TagItem>
            ))}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="space-y-2">
        <Button variant="primary" size="sm" className="w-full">
          🔄 重新分析
        </Button>
        <Button variant="outline" size="sm" className="w-full">
          💾 保存结果
        </Button>
      </div>
    </div>
  );
};

InfoPanel.displayName = 'InfoPanel';

export { InfoPanel };
