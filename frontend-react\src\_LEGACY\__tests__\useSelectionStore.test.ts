// useSelectionStore.test.ts - Phase 6: 选择状态管理测试
// 测试所有选择操作：单选、多选、范围选择、清除等

import { renderHook, act } from '@testing-library/react';
import { useSelectionStore } from '../useSelectionStore';
import type { FileEntry } from '@/types/electron';

// ============================================================================
// 测试数据
// ============================================================================

const mockFiles: FileEntry[] = [
  {
    name: 'file1.jpg',
    path: '/test/file1.jpg',
    size: 1024,
    isDirectory: false,
    modifiedTime: Date.now(),
    thumbnail: null,
  },
  {
    name: 'file2.png',
    path: '/test/file2.png',
    size: 2048,
    isDirectory: false,
    modifiedTime: Date.now(),
    thumbnail: null,
  },
  {
    name: 'file3.gif',
    path: '/test/file3.gif',
    size: 4096,
    isDirectory: false,
    modifiedTime: Date.now(),
    thumbnail: null,
  },
];

// ============================================================================
// 测试套件
// ============================================================================

describe('useSelectionStore', () => {
  beforeEach(() => {
    // 每个测试前重置store状态
    const { result } = renderHook(() => useSelectionStore());
    act(() => {
      result.current.clearSelection();
    });
  });

  describe('基础选择操作', () => {
    it('应该正确设置选中文件列表', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.setSelectedFiles([mockFiles[0], mockFiles[1]]);
      });

      expect(result.current.selectedFiles).toHaveLength(2);
      expect(result.current.selectedFiles[0]).toEqual(mockFiles[0]);
      expect(result.current.selectedFiles[1]).toEqual(mockFiles[1]);
      expect(result.current.selectedFile).toEqual(mockFiles[1]); // 最后选中的文件
    });

    it('应该正确添加文件到选择', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.addToSelection(mockFiles[0]);
      });

      expect(result.current.selectedFiles).toHaveLength(1);
      expect(result.current.selectedFiles[0]).toEqual(mockFiles[0]);
      expect(result.current.selectedFile).toEqual(mockFiles[0]);

      act(() => {
        result.current.addToSelection(mockFiles[1]);
      });

      expect(result.current.selectedFiles).toHaveLength(2);
      expect(result.current.selectedFile).toEqual(mockFiles[1]);
    });

    it('应该避免重复添加相同文件', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.addToSelection(mockFiles[0]);
        result.current.addToSelection(mockFiles[0]); // 重复添加
      });

      expect(result.current.selectedFiles).toHaveLength(1);
      expect(result.current.selectedFiles[0]).toEqual(mockFiles[0]);
    });

    it('应该正确从选择中移除文件', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.setSelectedFiles([mockFiles[0], mockFiles[1], mockFiles[2]]);
      });

      act(() => {
        result.current.removeFromSelection(mockFiles[1].path);
      });

      expect(result.current.selectedFiles).toHaveLength(2);
      expect(result.current.selectedFiles.find(f => f.path === mockFiles[1].path)).toBeUndefined();
      expect(result.current.selectedFile).toEqual(mockFiles[2]); // 最后一个文件
    });

    it('应该正确清除所有选择', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.setSelectedFiles([mockFiles[0], mockFiles[1]]);
      });

      expect(result.current.selectedFiles).toHaveLength(2);

      act(() => {
        result.current.clearSelection();
      });

      expect(result.current.selectedFiles).toHaveLength(0);
      expect(result.current.selectedFile).toBeNull();
    });
  });

  describe('高级选择操作', () => {
    it('应该正确切换文件选择状态', () => {
      const { result } = renderHook(() => useSelectionStore());

      // 切换选择（添加）
      act(() => {
        result.current.toggleFileSelection(mockFiles[0]);
      });

      expect(result.current.selectedFiles).toHaveLength(1);
      expect(result.current.selectedFiles[0]).toEqual(mockFiles[0]);

      // 切换选择（移除）
      act(() => {
        result.current.toggleFileSelection(mockFiles[0]);
      });

      expect(result.current.selectedFiles).toHaveLength(0);
      expect(result.current.selectedFile).toBeNull();
    });

    it('应该正确执行范围选择', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.selectRange(mockFiles, mockFiles[0], mockFiles[2]);
      });

      expect(result.current.selectedFiles).toHaveLength(3);
      expect(result.current.selectedFiles).toEqual(mockFiles);
      expect(result.current.selectedFile).toEqual(mockFiles[2]);
    });

    it('应该正确处理反向范围选择', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.selectRange(mockFiles, mockFiles[2], mockFiles[0]);
      });

      expect(result.current.selectedFiles).toHaveLength(3);
      expect(result.current.selectedFiles).toEqual(mockFiles);
      expect(result.current.selectedFile).toEqual(mockFiles[0]);
    });

    it('应该正确选择所有文件', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.selectAll(mockFiles);
      });

      expect(result.current.selectedFiles).toHaveLength(3);
      expect(result.current.selectedFiles).toEqual(mockFiles);
      expect(result.current.selectedFile).toEqual(mockFiles[2]);
    });
  });

  describe('向后兼容操作', () => {
    it('应该正确设置单选文件', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.setSelectedFile(mockFiles[0]);
      });

      expect(result.current.selectedFiles).toHaveLength(1);
      expect(result.current.selectedFiles[0]).toEqual(mockFiles[0]);
      expect(result.current.selectedFile).toEqual(mockFiles[0]);
    });

    it('应该正确清除单选文件', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.setSelectedFile(mockFiles[0]);
      });

      act(() => {
        result.current.setSelectedFile(null);
      });

      expect(result.current.selectedFiles).toHaveLength(0);
      expect(result.current.selectedFile).toBeNull();
    });
  });

  describe('查询操作', () => {
    it('应该正确检查文件是否被选中', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.setSelectedFiles([mockFiles[0], mockFiles[2]]);
      });

      expect(result.current.isFileSelected(mockFiles[0].path)).toBe(true);
      expect(result.current.isFileSelected(mockFiles[1].path)).toBe(false);
      expect(result.current.isFileSelected(mockFiles[2].path)).toBe(true);
    });

    it('应该正确返回选择计数', () => {
      const { result } = renderHook(() => useSelectionStore());

      expect(result.current.getSelectionCount()).toBe(0);

      act(() => {
        result.current.setSelectedFiles([mockFiles[0], mockFiles[1]]);
      });

      expect(result.current.getSelectionCount()).toBe(2);
    });
  });

  describe('边界情况', () => {
    it('应该处理空文件列表的范围选择', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.selectRange([], mockFiles[0], mockFiles[1]);
      });

      expect(result.current.selectedFiles).toHaveLength(0);
    });

    it('应该处理不存在文件的范围选择', () => {
      const { result } = renderHook(() => useSelectionStore());
      const nonExistentFile: FileEntry = {
        name: 'nonexistent.jpg',
        path: '/test/nonexistent.jpg',
        size: 0,
        isDirectory: false,
        modifiedTime: Date.now(),
        thumbnail: null,
      };

      act(() => {
        result.current.selectRange(mockFiles, nonExistentFile, mockFiles[1]);
      });

      expect(result.current.selectedFiles).toHaveLength(0);
    });

    it('应该处理空选择的移除操作', () => {
      const { result } = renderHook(() => useSelectionStore());

      act(() => {
        result.current.removeFromSelection('/nonexistent/path');
      });

      expect(result.current.selectedFiles).toHaveLength(0);
      expect(result.current.selectedFile).toBeNull();
    });
  });
});
