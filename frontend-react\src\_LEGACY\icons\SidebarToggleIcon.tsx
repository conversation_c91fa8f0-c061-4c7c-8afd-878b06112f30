interface SidebarToggleIconProps {
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export function SidebarToggleIcon({ className = '', onClick, style }: SidebarToggleIconProps) {
  return (
    <svg
      className={`icon ${className}`}
      onClick={onClick}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ cursor: 'pointer', ...style }}
    >
      <path
        d="M2 3h12M2 8h12M2 13h12"
        stroke="var(--color-text-title)"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
}
